<!-- <PERSON><PERSON>IN HEADER -->
<div class="page-header navbar navbar-fixed-top">
    <!-- BEGIN HEADER INNER -->
    <div class="page-header-inner ">
        <!-- BEGIN LOGO -->
        <div class="page-logo">
            <a href="<?php echo route('casa'); ?>">
                FONP</a>
            <div class="menu-toggler sidebar-toggler">
                <span></span>
            </div>
        </div>
        <!-- END LOGO -->
        <!-- BEGIN RESPONSIVE MENU TOGGLER -->
        <a href="javascript:;" class="menu-toggler responsive-toggler" data-toggle="collapse"
           data-target=".navbar-collapse">
            <span></span>
        </a>
        <!-- END RESPONSIVE MENU TOGGLER -->
        <!-- BEGIN TOP NAVIGATION MENU -->
        <div class="top-menu">
            <ul class="nav navbar-nav pull-right">

                <li class="dropdown dropdown-language">
                    <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown"
                       data-close-others="true">
                        {{--<img alt="" src="../assets/global/img/flags/us.png">--}}
                        {{ strtoupper(LaravelLocalization::getCurrentLocale()) }}<i class="fa fa-angle-down"></i></a>

                    </a>
                    <ul class="dropdown-menu dropdown-menu-default">
                        @if(LaravelLocalization::getCurrentLocale()!='pt')
                            <li>

                                <a href="{{ LaravelLocalization::getLocalizedURL('pt', url()->current()) }}">
                                    <img alt=""
                                         src="{{asset('assets/global/img/flags/pt.png')}}">{{ trans('menus.country_pt') }}
                                </a>
                            </li>
                        @endif
                        @if(LaravelLocalization::getCurrentLocale()!='en')
                            <li>
                                <a href="{{ LaravelLocalization::getLocalizedURL('en', url()->current()) }}">
                                    <img alt=""
                                         src="{{asset('assets/global/img/flags/us.png')}}">{{ trans('menus.country_en') }}
                                </a>
                            </li>
                        @endif
                        @if(LaravelLocalization::getCurrentLocale()!='es')
                            <li>
                                <a href="{{ LaravelLocalization::getLocalizedURL('es', url()->current()) }}">
                                    <img alt=""
                                         src="{{asset('assets/global/img/flags/es.png')}}">{{ trans('menus.country_es') }}
                                </a>
                            </li>
                        @endif
                        @if(LaravelLocalization::getCurrentLocale()!='fr')
                            <li>
                                <a href="{{ LaravelLocalization::getLocalizedURL('fr', url()->current()) }}">
                                    <img alt=""
                                         src="{{asset('assets/global/img/flags/fr.png')}}">{{ trans('menus.country_fr') }}
                                </a>
                            </li>
                        @endif
                        @if(LaravelLocalization::getCurrentLocale()!='nl')
                            <li>
                                <a href="{{ LaravelLocalization::getLocalizedURL('nl', url()->current()) }}">
                                    <img alt=""
                                         src="{{asset('assets/global/img/flags/nl.png')}}">{{ trans('menus.country_nl') }}
                                </a>
                            </li>
                        @endif
                        @if(LaravelLocalization::getCurrentLocale()!='it')
                            <li>
                                <a href="{{ LaravelLocalization::getLocalizedURL('it', url()->current()) }}">
                                    <img alt=""
                                         src="{{asset('assets/global/img/flags/it.png')}}">{{ trans('menus.country_it') }}
                                </a>
                            </li>
                        @endif
                        @if(LaravelLocalization::getCurrentLocale()!='tr')
                            <li>
                                <a href="{{ LaravelLocalization::getLocalizedURL('tr', url()->current()) }}">
                                    <img alt=""
                                         src="{{asset('assets/global/img/flags/tr.png')}}">{{ trans('menus.country_tr') }}
                                </a>
                            </li>
                        @endif

                    </ul>
                </li>

                <!-- END TODO DROPDOWN -->
                <!-- BEGIN USER LOGIN DROPDOWN -->
                <!-- DOC: Apply "dropdown-dark" class after below "dropdown-extended" to change the dropdown styte -->
                <li class="dropdown dropdown-user">
                    <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown"
                       data-close-others="true">
                        {{--<img alt="" class="img-circle" src="{{asset('assets/layouts/layout/img/avatar3_small.jpg')}}"/>--}}
                        <i class="icon-user"></i>
                        <span class="username username-hide-on-mobile">{{ Auth::user()->name}}</span>
                        <i class="fa fa-angle-down"></i>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-default">
                        <li>
                            <a href="{{ route('criadorperfil') }}">
                                <i class="icon-user"></i> {{trans('menus.edit_profile')}}</a>
                        </li>
                        <li>
                            <a href="{!! url('/logout') !!}">
                                <i class="icon-key"></i>{{trans('menus.logout')}}</a>
                        </li>
                    </ul>
                </li>
                <!-- END USER LOGIN DROPDOWN -->
                <!-- BEGIN QUICK SIDEBAR TOGGLER -->
                <!-- DOC: Apply "dropdown-dark" class after below "dropdown-extended" to change the dropdown styte -->
            {{-- <li class="dropdown dropdown-quick-sidebar-toggler">
                 <a href="javascript:;" class="dropdown-toggle">
                     <i class="icon-logout"></i>
                 </a>
             </li>--}}
            <!-- END QUICK SIDEBAR TOGGLER -->


            </ul>

        </div>
        <!-- END TOP NAVIGATION MENU -->
    </div>
    <!-- END HEADER INNER -->
</div>
<!-- END HEADER -->