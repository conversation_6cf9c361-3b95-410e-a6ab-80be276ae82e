<!DOCTYPE html>
<!--
Template Name: Metronic - Responsive Admin Dashboard Template build with Twitter Bootstrap 3.3.7
Version: 4.7.5
Author: KeenThemes
Website: http://www.keenthemes.com/
Contact: <EMAIL>
Follow: www.twitter.com/keenthemes
Dribbble: www.dribbble.com/keenthemes
Like: www.facebook.com/keenthemes
Purchase: http://themeforest.net/item/metronic-responsive-admin-dashboard-template/4021469?ref=keenthemes
Renew Support: http://themeforest.net/item/metronic-responsive-admin-dashboard-template/4021469?ref=keenthemes
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<!-- BEGIN HEAD -->

<head>
    <meta charset="utf-8"/>
    <title>FONP</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="Miguel Vaz" name="author"/>
    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,300,600,700&subset=all" rel="stylesheet"
          type="text/css"/>
    <link href="../assets/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css"/>
    <link href="../assets/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css"/>
    <link href="../assets/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="../assets/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet"
          type="text/css"/>
    <!-- END GLOBAL MANDATORY STYLES -->
    <!-- BEGIN PAGE LEVEL PLUGINS -->
    <link href="../assets/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="../assets/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <!-- END PAGE LEVEL PLUGINS -->
    <!-- BEGIN THEME GLOBAL STYLES -->
    <link href="../assets/global/css/components.min.css" rel="stylesheet" id="style_components" type="text/css"/>
    <link href="../assets/global/css/plugins.min.css" rel="stylesheet" type="text/css"/>
    <!-- END THEME GLOBAL STYLES -->
    <!-- BEGIN PAGE LEVEL STYLES -->
    <link href="../assets/pages/css/login-3.css" rel="stylesheet" type="text/css"/>
    <!-- END PAGE LEVEL STYLES -->
    <!-- BEGIN THEME LAYOUT STYLES -->
    <!-- END THEME LAYOUT STYLES -->
    <link rel="shortcut icon" href="favicon.ico"/>
</head>
<!-- END HEAD -->

<body class=" login">
<!-- BEGIN LOGO -->
<div class="logo">
    <h2 class="page-title"><b>SISCOM </b><br>{{ trans('messages.login_subtitulo') }}</h2>
    <h4>{{ trans('messages.login-com') }}</h4>
</div>
<!-- END LOGO -->
<!-- BEGIN LOGIN -->
<div class="content" style="width: 500px">
    <div style="height: 35px">
        <ul class="nav navbar-nav pull-right">
            <li class="dropdown dropdown-language">
                <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown"
                   data-close-others="true">
                    {{--<img alt="" src="../assets/global/img/flags/us.png">--}}
                    {{ strtoupper(LaravelLocalization::getCurrentLocale()) }}<i class="fa fa-angle-down"></i></a>

                </a>
                <ul class="dropdown-menu dropdown-menu-default">
                    @if(LaravelLocalization::getCurrentLocale()!='pt')
                        <li>

                            <a href="{{ LaravelLocalization::getLocalizedURL('pt', url()->current()) }}">
                                <img alt=""
                                     src="{{asset('assets/global/img/flags/pt.png')}}">{{ trans('menus.country_pt') }}
                            </a>
                        </li>
                    @endif
                    @if(LaravelLocalization::getCurrentLocale()!='en')
                        <li>
                            <a href="{{ LaravelLocalization::getLocalizedURL('en', url()->current()) }}">
                                <img alt=""
                                     src="{{asset('assets/global/img/flags/us.png')}}">{{ trans('menus.country_en') }}
                            </a>
                        </li>
                    @endif
                    @if(LaravelLocalization::getCurrentLocale()!='es')
                        <li>
                            <a href="{{ LaravelLocalization::getLocalizedURL('es', url()->current()) }}">
                                <img alt=""
                                     src="{{asset('assets/global/img/flags/es.png')}}">{{ trans('menus.country_es') }}
                            </a>
                        </li>
                    @endif
                    @if(LaravelLocalization::getCurrentLocale()!='fr')
                        <li>
                            <a href="{{ LaravelLocalization::getLocalizedURL('fr', url()->current()) }}">
                                <img alt=""
                                     src="{{asset('assets/global/img/flags/fr.png')}}">{{ trans('menus.country_fr') }}
                            </a>
                        </li>
                    @endif
                    @if(LaravelLocalization::getCurrentLocale()!='nl')
                        <li>
                            <a href="{{ LaravelLocalization::getLocalizedURL('nl', url()->current()) }}">
                                <img alt=""
                                     src="{{asset('assets/global/img/flags/nl.png')}}">{{ trans('menus.country_nl') }}
                            </a>
                        </li>
                    @endif
                        @if(LaravelLocalization::getCurrentLocale()!='it')
                            <li>
                                <a href="{{ LaravelLocalization::getLocalizedURL('it', url()->current()) }}">
                                    <img alt=""
                                         src="{{asset('assets/global/img/flags/it.png')}}">{{ trans('menus.country_it') }}
                                </a>
                            </li>
                        @endif
                        @if(LaravelLocalization::getCurrentLocale()!='tr')
                            <li>
                                <a href="{{ LaravelLocalization::getLocalizedURL('tr', url()->current()) }}">
                                    <img alt=""
                                         src="{{asset('assets/global/img/flags/tr.png')}}">{{ trans('menus.country_tr') }}
                                </a>
                            </li>
                        @endif

                </ul>
            </li>
        </ul>
    </div>
    <!-- BEGIN LOGIN FORM -->
    <form class="login-form" action="{{ url('/'.LaravelLocalization::getCurrentLocale().'/login') }}" method="post">
        {!! csrf_field() !!}
        <h3 class="form-title">{{ trans('messages.login_mensagem') }}</h3>
        <div class="form-group">
            @if (session('status'))
                <div class="alert alert-success">
                    {{ session('status') }}
                </div>
            @endif

        </div>

        <div class="alert alert-danger display-hide">
            <button class="close" data-close="alert"></button>
            <span>{{ __('sem nenhum login') }}</span>
        </div>
        <div class="form-group">
            <span class=" has-feedback {{ $errors->has('stam') ? ' has-error' : '' }}">
            @if ($errors->has('stam'))
                    <span class="help-block">
                    <strong>{{ $errors->first('stam') }}</strong>
                </span>
                @endif</span>
            <!--ie8, ie9 does not support html5 placeholder, so we just show field title for that-->
            <label class="control-label visible-ie8 visible-ie9">Username</label>
            <div class="input-icon">
                <i class="fa fa-user"></i>
                <input class="form-control placeholder-no-fix" type="text" autocomplete="off" placeholder="STAM"
                       name="stam" value="{{ old('stam') }}"/></div>
        </div>
        <div class="form-group">
            <label class="control-label visible-ie8 visible-ie9">Password</label>
            <div class="input-icon">
                <i class="fa fa-lock"></i>
                <input class="form-control placeholder-no-fix" type="password" autocomplete="off" placeholder="Password"
                       name="password"/></div>
        </div>
        <div class="form-actions">
            <label class="rememberme mt-checkbox mt-checkbox-outline">
                <input type="checkbox" name="remember" value="1"/> {{ trans('auth.remember') }}
                <span></span>
            </label>
            <span></span>
            <button type="submit" class="btn green pull-right"> Login</button>
        </div>
        <div class="forget-password">
            <h5>{{ trans('auth.forgotpassword') }}</h5>
            {{--<p> {{ trans('auth.forgotpasswordclickhere') }} <a href="javascript:;" id="forget-password"> {{ trans('auth.forgotherelink') }} </a></p>--}}

            {{--o valido é o seguinte:--}}
            <p> {{ trans('auth.forgotpasswordclickhere') }} <a href="{{ route('password.request') }}" id="forget-password2"> {{ trans('auth.forgotherelink') }} </a></p>
        </div>

    </form>
    <!-- END LOGIN FORM -->

    <!-- BEGIN FORGOT PASSWORD FORM -->
    <form class="forget-form" action="{{ route('password.email') }}" method="post">
        <h3>{{ trans('auth.forgotpassword') }}</h3>
        <p> {{ trans('auth.forgotenteryouremail') }} </p>
        {{ csrf_field() }}
        {{--<div class="form-group">--}}
            {{--<div class="input-icon">--}}
                {{--<i class="fa fa-envelope"></i>--}}
                {{--<input class="form-control placeholder-no-fix" type="text" autocomplete="off" placeholder="Email"--}}
                       {{--name="email"/></div>--}}
        {{--</div>--}}
        <div class="form-group">
            <div class="input-icon">
                <i class="fa fa-address-book"></i>
                <input class="form-control placeholder-no-fix" type="text" autocomplete="off" placeholder="STAM"
                       name="stam"/></div>
        </div>
        <div class="form-actions">
            <button type="button" id="back-btn" class="btn grey-salsa btn-outline"> {{ trans('auth.forgotpasswordback') }}</button>
            <button type="submit" class="btn green pull-right"> {{ trans('auth.forgotpasswordsubmit') }}</button>
        </div>
    </form>
    <!-- END FORGOT PASSWORD FORM -->
    <div class="portlet light" id="gdpr">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-info font-grey-gallery"></i>
                <span class="caption-helper"> {{ trans('auth.conta_nao_funciona') }}</span>
            </div>
            <div class="tools">
                <a href="javascript:;" class="expand" data-original-title="" title=""> </a>
            </div>
        </div>
        <div class="portlet-body portlet-collapsed">
            <p style="font-size: 11px;">

                {!! trans('auth.gdpr_texto_titulo') !!}
                {!! trans('auth.gdpr_texto2') !!}
            </p>

            <p style="font-size: 11px;">
                {!! trans('auth.conta_nao_funciona_txt') !!}
            </p>
            <div>
                <a href="{{ url('/'.LaravelLocalization::getCurrentLocale().'/activarregisto') }}"
                   class="btn btn-primary btn-flat pull-right">{{ trans('auth.activar_conta') }}</a>
            </div>
        </div>
    </div>
</div>
<br><br>

<!-- END LOGIN -->
@include('auth.scripts')
</body>

</html>