@extends('layouts.app')



@section('content')

@section('titulo', trans('general.dashboard_gestorn'))

<section id="infotralha">
    <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
            {{--<a class="dashboard-stat dashboard-stat-v2 blue" href="{!! route('gestornuserslist') !!}">--}}
            <a class="dashboard-stat dashboard-stat-v2 blue" href="#">
                <div class="visual">
                    <i class="fa fa-users"></i>
                </div>
                <div class="details">
                    <div class="number">
                        <span data-counter="counterup" data-value="{{$nusers}}">{{$nusers}}</span>
                    </div>
                    <div class="desc"> {{ trans('general.utilizadores') }}</div>
                    {{--<div class="desc"><h6>({{ trans('general.clique_gerir') }})</h6></div>--}}
                </div>
            </a>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
            <a class="dashboard-stat dashboard-stat-v2 green" href="#">
                <div class="visual">
                    <i class="fa fa-building"></i>
                </div>
                <div class="details">
                    <div class="number">
                        <span data-counter="counterup" data-value="{{$nclubes}}">{{$nclubes}}</span>
                    </div>
                    <div class="desc"> {{ trans('general.clubes_associacoes') }}</div>
                    {{--<div class="desc"><h6>({{ trans('general.clique_gerir') }})</h6></div>--}}
                </div>
            </a>
        </div>

    </div>

</section>
<br>

<section id="eventos">

    <div class="portlet box blue-hoki">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-share font-white"></i>
                <span class="caption-subject font-white uppercase">{{ trans('general.eventos_activos') }}</span>
            </div>

        </div>
        <div class="portlet-body">
            <div class="row">
                @foreach ($eventos_activos as $evt)
                    <div class="col-lg-6 col-md-6 col-xs-12">
                        <div class="mt-element-ribbon bg-grey-steel" style="min-height: 250px">
                            <div class="ribbon ribbon-color-default uppercase bold">{{ $evt->nome }}</div>
                            <table style="margin-bottom: 20px;" class="table table-striped task-table">
                                <tbody>
                                <tr>
                                    <td class="table-text">
                                        <div>
                                            <strong>{{ trans('general.data_inicio') }}</strong><br> {{ $evt->data_inicio }}
                                        </div>
                                        <div><strong>{{ trans('general.data_fim') }}</strong><br> {{ $evt->data_fim }}
                                        </div>
                                    </td>
                                    <td class="table-text">
                                        <div style="margin-top: 10px;">{{ trans('general.registration_period') }}<br>
                                            <strong>{{ $evt->data_inicio_inscricoes }}</strong> a
                                            <strong>{{ $evt->data_fim_inscricoes }}</strong></div>
                                    </td>
                                    <td class="table-text">
                                        <a href="{{ route('eventohomegestorn',[$evt->id]) }}">
                                            <button class="btn btn-success">{{ trans('general.registrations') }}</button>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="col-md-6 col-sm-6 col-xs-12">

                                <div class="box-footer">
                                    <ul class="nav nav-stacked">
                                        <li>
                                            {{ trans('general.registrations') }} <span
                                                    class="pull-right badge bg-blue">{{$evt->num_inscricoes}}</span>
                                        </li>
                                        <li>
                                            {{ trans('general.birds') }} <span
                                                    class="pull-right badge bg-aqua">{{$evt->num_passaros}}</span>
                                        </li>

                                    </ul>
                                </div>

                            </div><!-- /.col -->
                            <div class="col-md-6 col-sm-6 col-xs-12">

                                <div class="box-footer">
                                    <ul class="nav nav-stacked">
                                        <li>
                                            {{ trans('general.juizes') }} <span
                                                    class="pull-right badge bg-green">{{$evt->num_juizes}}</span>
                                        </li>
                                        <li>
                                            {{ trans('general.fichas_julgamento') }}<span
                                                    class="pull-right badge bg-red">{{$evt->num_fichas_julgamento}}</span>
                                        </li>
                                        <li>
                                            {{ trans('general.seccoes_classes') }}<span
                                                    class="pull-right badge bg-red">{{$evt->num_seccoes_classes}}</span>
                                        </li>
                                    </ul>
                                </div>


                            </div><!-- /.col -->


                        </div>
                    </div>

                @endforeach
            </div>

        </div>
    </div>

</section>

@endsection

@section('scripts')
    <script>


    </script>






@endsection

