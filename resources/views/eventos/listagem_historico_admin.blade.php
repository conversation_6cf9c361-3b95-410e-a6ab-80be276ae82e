@extends('layouts.app')


@section('content')

@section('titulo', 'Histórico de Eventos')
<section id="tralha">
    <br>

    @if (count($eventos_historico) > 0)
        <section id="eventos">

            <div class="col-md-12 col-xs-12">
                <div class="box box-solid box-default">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans ('eventos.historico_de_eventos')}}</h3>
                    </div>
                    <div class="box-body">
                        @foreach ($eventos_historico as $evt)

                            <div class="col-md-6 col-xs-12">
                                <div class="box box-primary">
                                    <div class="box-header with-border">
                                        <h3 class="box-title">{{ $evt->nome }}</h3>
                                    </div>
                                    <div class="box-body">
                                        <table style="margin-bottom: 20px;" class="table table-striped task-table">
                                            <tbody>
                                            <tr>
                                                <td class="table-text">
                                                    <div><strong>{{trans ('general.data_inicio')}}:</strong> {{ $evt->data_inicio }}</div>
                                                    <div><strong>{{trans ('general.data_fim')}}:</strong> {{ $evt->data_fim }}</div>
                                                </td>
                                                <td class="table-text">
                                                    <div style="margin-top: 10px;">{{trans ('general.inscricoes')}}<strong>{{ $evt->data_inicio_inscricoes }}</strong> a <strong>{{ $evt->data_fim_inscricoes }}</strong></div>
                                                </td>
                                                <td class="table-text">
                                                    <a href="{{ route('eventohome',[$evt->id]) }}">
                                                        <button class="btn btn-success">{{trans ('general.gerir')}}</button>
                                                    </a>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>



                                    </div>
                                </div>
                            </div>

                        @endforeach

                    </div>
                </div>
            </div>
        </section>

    @endif


</section>


@endsection
