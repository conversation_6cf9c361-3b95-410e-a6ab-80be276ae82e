@extends('layouts.app')


@section('titulo','Ajustar Etiquetas')


@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> {{trans ('clubes.inicio')}}</a></li>
        <li class="active">{{trans ('clubes.etiquetas')}}</li>
    </ol>
@endsection

@section('content')
    <div class="content body">
        <div class="row">
            <div class="col-sm-10">
                <div class="box box-solid box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans ('clubes.ajustes_etiquetas_provisorias')}}</h3>
                    </div>
                    <div class="box-body">
                        <form method="post" name="f0" action="<?php echo route('gravarajustesetiquetas'); ?>" method="post">
                            <input type="text" name="orig" hidden value="provisorias">

                            <table>
                                <tr>
                                    <td width="8%"> Linha 1:</td>
                                    <td width="15%"><input type="text" size=6 maxlength=6 name="ep1" style="text-align: center;" value="<?php echo $etiquetas[0]->linha1_p; ?>"></td>
                                    <td width="8%"> Linha 2:</td>
                                    <td width="15%"><input type="text" size=6 maxlength=6 name="ep2" style="text-align: center;" value="<?php echo $etiquetas[0]->linha2_p; ?>">
                                        <d>
                                    <td width="8%"> Linha 3:</td>
                                    <td width="15%"><input type="text" size=6 maxlength=6 name="ep3" style="text-align: center;" value="<?php echo $etiquetas[0]->linha3_p; ?>"></td>
                                    <td width="8%"> Linha 4:</td>
                                    <td width="15%"><input type="text" size=6 maxlength=6 name="ep4" style="text-align: center;" value="<?php echo $etiquetas[0]->linha4_p; ?>"></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td> Linha 5:</td>
                                    <td><input type="text" size=6 maxlength=6 name="ep5" style="text-align: center;" value="<?php echo $etiquetas[0]->linha5_p; ?>"></td>
                                    <td> Linha 6:</td>
                                    <td><input type="text" size=6 maxlength=6 name="ep6" style="text-align: center;" value="<?php echo $etiquetas[0]->linha6_p; ?>"></td>
                                    <td> Linha 7:</td>
                                    <td><input type="text" size=6 maxlength=6 name="ep7" style="text-align: center;" value="<?php echo $etiquetas[0]->linha7_p; ?>"></td>
                                    <td> Linha 8:</td>
                                    <td><input type="text" size=6 maxlength=6 name="ep8" style="text-align: center;" value="<?php echo $etiquetas[0]->linha8_p; ?>"></td>
                                    <td colspan=8></td>

                                    <td align="center"> &nbsp;<input class="btn" type="submit" value="Guardar"/></td>
                                </tr>
                            </table>
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        </form>

                    </div>

                </div>

            </div>
        </div>
        <div class="row">
            <div class="col-sm-10">
                <div class="box box-solid box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans ('clubes.ajustes_etiquetas_definitivas')}}</h3>
                    </div>
                    <div class="box-body">
                        <form name="f1" action="<?php echo route('gravarajustesetiquetas'); ?>" method="post">
                            <input type="text" name="orig" hidden value="definitivas">
                            <table>
                                <tr>
                                    <td width="8%"> Linha 1:</td>
                                    <td width="15%"><input type="text" size=6 maxlength=6 name="ed1" style="text-align: center;" value="<?php echo $etiquetas[0]->linha1_d; ?>"></td>
                                    <td width="8%"> Linha 2:</td>
                                    <td width="15%"><input type="text" size=6 maxlength=6 name="ed2" style="text-align: center;" value="<?php echo $etiquetas[0]->linha2_d; ?>"></td>
                                    <td width="8%"> Linha 3:</td>
                                    <td width="15%"><input type="text" size=6 maxlength=6 name="ed3" style="text-align: center;" value="<?php echo $etiquetas[0]->linha3_d; ?>"></td>
                                    <td width="8%"> Linha 4:</td>
                                    <td width="15%"><input type="text" size=6 maxlength=6 name="ed4" style="text-align: center;" value="<?php echo $etiquetas[0]->linha4_d; ?>"></td>
                                    <td>
                                </tr>
                                <tr>
                                    <td> Linha 5:</td>
                                    <td><input type="text" size=6 maxlength=6 name="ed5" style="text-align: center;" value="<?php echo $etiquetas[0]->linha5_d; ?>"></td>
                                    <td> Linha 6:</td>
                                    <td><input type="text" size=6 maxlength=6 name="ed6" style="text-align: center;" value="<?php echo $etiquetas[0]->linha6_d; ?>"></td>
                                    <td> Linha 7:</td>
                                    <td><input type="text" size=6 maxlength=6 name="ed7" style="text-align: center;" value="<?php echo $etiquetas[0]->linha7_d; ?>"></td>
                                    <td> Linha 8:</td>
                                    <td><input type="text" size=6 maxlength=6 name="ed8" style="text-align: center;" value="<?php echo $etiquetas[0]->linha8_d; ?>"></td>
                                    <td colspan=8></td>

                                    <td align="center"> &nbsp;<input class="btn" type="submit" value="Guardar"></td>

                                </tr>
                            </table>
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        </form>
                    </div>

                </div>

            </div>
        </div>
    </div>
    <!-- Main content -->

    <br>
    <br>




@endsection
