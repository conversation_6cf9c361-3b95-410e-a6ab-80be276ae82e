<div class="container-fluid">

    <div class="row" style="width: 400px;height: 540px">
        <div class="col-md-12">
            <div class="col-md-12">
                <form id="form_inscrever" action="<?php echo route('updateinscricaocriadorevento'); ?>" method="post">

                    <input type="hidden" name="inscricaoid" value="{!! $inscricao->id !!}">
                    <input type="hidden" name="redirectto" value="{{$redirectto}}">
                    <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                    <br>
                    <div class="form-group">
                        <label for="num_jantares">{{trans ('general.numero_de_jantares')}}:
                            ({!! $evento->preco_jantar !!}€)</label>
                        <input type="text" class="form-control" name="num_jantares" id="num_jantares"
                               value="{!! $inscricao->num_jantares !!}">
                    </div>
                    @if((int)$evento->tipo_evento_id !== 6)
                        <div class="form-group">
                            <label for="email">{{trans ('general.numero_de_mesas')}} ({!! $evento->preco_mesa_venda !!}
                                €)</label>
                            <input type="text" class="form-control" name="num_mesas_venda" id="num_mesas_venda"
                                   value="{!! $inscricao->num_mesas_venda !!}">
                        </div>
                        <div class="form-group">
                            <label for="num_stands">{{trans ('general.numero_de_stands')}} ({!! $evento->preco_stand !!}
                                €)</label>
                            <input type="text" class="form-control" name="num_stands" id="num_stands"
                                   value="{!! $inscricao->num_stands !!}">
                        </div>
                    @endif
                    @if((int)$evento->tipo_evento_id === 6)
                    <div class="form-group">

                        <label class="col-md-10 control-label">{{trans('general.utiliza_caixas')}}:</label><br>
                        <div class="mt-radio-inline">
                            <label class="mt-radio">
                                {!! Form::radio('tipo_caixa', 0, ($inscricao->tipo_caixa==0)?true:false); !!}{{trans('general.caixa_propria')}}
                                <span></span>
                            </label>
                            <label class="mt-radio">
                                {!! Form::radio('tipo_caixa', 1, ($inscricao->tipo_caixa==1)?true:false); !!}{{trans('general.caixa_federativa')}}

                                <span></span>
                            </label>
                        </div>

                    </div>
                    @endif
                        <div class="form-group">
                            <label class="col-md-10 control-label">{{trans('general.local_recolha')}}:</label><br>
                            <div class="form-group col-md-12">
                                <select id="localrecolha" name="localrecolha" class="form-control col-sm-12" data-live-search="false">
                                    <option {{$inscricao->local_recolha == 0 ? 'selected' : '' }} value="0">{{trans('general.local_evento')}}</option>
                                    @foreach($locaisrecolha as $lr)
                                        <option {{$lr->id==$inscricao->local_recolha ? 'selected' : '' }} value="{{ $lr->id }}">{{$lr->local_nome}} ({{ $lr->local_morada }})</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                    <input type="submit" class="btn btn-info" value="Gravar">
                    <button type="button" class="btn default"
                            data-dismiss="modal">{{trans ('clubes.cancelar')}}</button>
                </form>
            </div>
        </div>
    </div>
</div>

