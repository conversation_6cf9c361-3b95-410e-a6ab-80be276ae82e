<div class="row">
    <div class="col-md-12">


        <div class="form-group col-md-12">
            <div>{{trans('general.filtrar_inscricoes')}}</div>
            <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-default active">
                    <input id="filterall" type="radio" name="filtro" value="all" class="toggle">{{trans('general.todas')}}</label>
                <label class="btn btn-default">
                    <input id="filteractive" type="radio" name="filtro" value="1" class="toggle">{{trans('general.activas')}}</label>
                <label class="btn btn-default">
                    <input id="filterpending" type="radio" name="filtro" value="2" class="toggle">{{trans('general.pendentes')}}</label>
            </div>

        </div>


        <div class="form-group col-md-12">
            <label for="grid_inscricoes">{{trans('general.inscritos')}}</label>
            {{--<select id="id_inscricao_select" name="id_inscricao">--}}
            {{----}}
            {{--</select>--}}
            <div id="grid_inscricoes" style="height: 200px;"></div>

        </div>
        <div class="form-group col-md-12" style="margin-bottom: 0">
            <table class="table table-condensed">
                <tr>
                    <td><strong>{{trans('general.criadores_inscritos')}}</strong></td>
                    <td><strong>{{trans('general.passaros_inscritos')}}</strong></td>


                </tr>
                <tr>
                    <td style="font-size: 10px"><span style="font-size: 12px" id="tinscritos"></span> (<span id="tinscritosa"></span> {{trans('general.activo')}} / <span style="font-size: 10px" id="tinscritosp"></span> {{trans('general.pendentes')}})</td>
                    <td><div id="tpassaros"></div></td>
                </tr>
            </table>
        </div>
        <div class="form-group col-md-12">


            {{-- <button class="btn btn-primary btn-lg" data-toggle="modal" data-target="#editpassaro">Launch demo modal</button>--}}
            <form id="form_inscrever_passaro" method="post">
                <input type="hidden" name="eventoid" value="{{$evento->id}}">
                <input type="hidden" name="rdt" value="ajax">
                <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                <table class="table table-condensed">
                    <tr>
                        <td colspan="8"><strong>{{trans('general.seccao_classe')}}</strong></td>

                    </tr>
                    <tr>
                        <td colspan="8">
                            <select name="seccaoclasseid" id="listaseccoesp" class="col-sm-12 selectpicker"
                                    data-live-search="true">
                                @foreach($classes as $ci)
                                    <option value="{{ $ci->id }}">{{$ci->seccao}}.{{$ci->classe}}
                                        - {{$ci->nome}}</option>
                                @endforeach
                            </select>
                        </td>

                    </tr>
                    <tr>
                        <td><strong>{{trans('general.preco')}}</strong></td>
                        <td><strong>{{trans('general.anilha')}}</strong></td>
                        <td><strong>{{trans('general.ano')}}</strong></td>
                        <td><strong>{{trans('general.vendido')}}</strong></td>
                        <td><strong>{{trans('general.ausente')}}</strong></td>
                        <td><strong>{{trans('general.gaiola_propria')}}</strong></td>
                        <td><strong>Obs</strong></td>

                    </tr>
                    <tr>

                        <td><input type="text" size="10" class="form-control" name="preco" placeholder="Preço"></td>
                        <td><input type="text" size="10" class="form-control" name="anilha" placeholder="Anilha"></td>
                        <td><input type="text" size="10" class="form-control" name="ano" placeholder="Ano"
                                   value="<?php echo date('Y'); ?>"></td>
                        <td><select class="form-control" name="vendido" id="vendido">
                                <option selected value="0">{{trans('general.nao')}}</option>
                                <option value="1">{{trans('general.sim')}}</option>
                            </select></td>
                        <td><select class="form-control" name="ausente" id="ausente">

                                <option selected value="0">{{trans('general.nao')}}</option>
                                <option value="1">{{trans('general.sim')}}</option>
                            </select></td>
                        <td><select class="form-control" name="gaiola_propria" id="gaiola_propria">
                                <option selected value="0">{{trans('general.nao')}}</option>
                                <option value="1">{{trans('general.sim')}}</option>
                            </select></td>
                        <td><input type="text" size="10" class="form-control" name="observacao"
                                   placeholder="Observação"></td>

                    </tr>

                    <tr>
                        <td colspan="6">

                        </td>
                        <td>
                            <strong>{{trans('general.multiplicador')}} (<=10)</strong> <i class="fa fa-question-circle"
                                                                                          title="{{trans('general.texto_multiplicador')}} (<=10)"></i>
                        </td>

                    </tr>
                    <tr>
                        <td colspan="2">
                            <a role="button" class="btn btn-default" id="ficha_inscricao"><i
                                        class="fa fa-sticky-note-o"></i> {{trans('general.ficha_inscricao')}}</a>
                        </td>
                        <td colspan="3"></td>

                        <td>
                            <input type="submit" class="btn btn-info" value="{{trans('general.adicionar_passaro')}}">

                        </td>
                        <td>
                            <input type="text" size="2" class="form-control" value="1" name="multiplicador">
                        </td>
                    </tr>

                </table>
            </form>
            {{--<button id="adicionar_bt" type="button" class="btn btn-primary">Adicionar Pássaro</button>--}}


        </div>
        <div class="form-group col-md-12">
            <label for="grid_passaros">{{trans('general.passaros')}}</label><span> ({{ trans('general.total') }}
                : <span id="total_passaros">{{$num_passaros}}</span>)</span>
            <div id="grid_passaros" style="height: 500px;"></div>
        </div>

        <div class="modal fade" id="ajaxinscreverc" role="basic" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body">
                        <img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">
                        <span> &nbsp;&nbsp;{{trans('general.loading')}}</span>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>