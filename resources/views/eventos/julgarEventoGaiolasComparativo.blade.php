@extends('layouts.app')


@section('titulo','Julgamento de Evento')
@section('subtitulo',$evento->nome)


@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> {{trans ('clubes.inicio')}}</a></li>
        <li class="active">{{trans ('general.julgamento_evento_comparativo')}}</li>
    </ol>
@endsection

@section('content')
    <div class="content body">
        <div class="row">
            <div class="col-sm-10">
                <div class="box box-solid box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans ('general.gaiolas_em')}} {!! $seccaoclasse->seccao." ".$seccaoclasse->classe !!} {!! $seccaoclasse->nome !!}</h3>
                    </div>
                    <div class="box-body">
                        <div class="col-sm-12" style="margin-bottom: 10px;">

                            <a href="{!! route('julgareventoseccao',[$evento->id,$seccaoclasse->seccao]) !!}"
                               class="col-sm-3 btn btn-primary" role="button">< {{trans ('general.regressar_classes')}}</a>

                        </div>

                        @if($problemas!="")
                            <div class="col-sm-12">
                                <div class="box box-danger">
                                    <div class="box-header with-border">
                                        <h3 class="box-title">{{trans ('general.problemas')}}</h3>

                                        <div class="box-tools pull-right">
                                            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i
                                                        class="fa fa-minus"></i>
                                            </button>
                                        </div>
                                        <!-- /.box-tools -->
                                    </div>
                                    <!-- /.box-header -->
                                    <div class="box-body">
                                        {!! $problemas !!}
                                    </div>
                                    <!-- /.box-body -->
                                </div>
                            </div>


                        @endif
                        <div tabindex="-1" class="modal fade" id="classificar" role="basic" aria-hidden="true"
                             style="display: none;">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button class="close" aria-hidden="true" type="button"
                                                data-dismiss="modal"></button>
                                        <h4 class="modal-title">{{trans ('clubes.num_gaiola')}}: <span id="fnumgaiola"></span></h4>
                                    </div>
                                    <div class="modal-body">
                                        <div class="col-md-12">
                                            <form id="form_classificar"
                                                  action="{!! route('gravargaiolacomparativo') !!}" method="post">
                                                <input type="hidden" name="eventoid" value="{{$evento->id}}">
                                                <input type="hidden" name="passaroid" value="2222">
                                                <input type="hidden" name="rescid" value="3333">
                                                <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                                                <br>
                                                <div class="form-group">
                                                    <label for="pclassificacao">{{trans ('general.classificacao')}}:</label>
                                                    <input type="text" class="form-control" name="pclassificacao"
                                                           id="pclassificacao" value="ccccccc">
                                                </div>
                                                <div class="form-group">
                                                    <label for="ppremio">{{trans ('general.premio')}}:</label>
                                                    <input type="text" class="form-control" name="ppremio" id="ppremio"
                                                           value="ppppppp">
                                                </div>

                                                <button class="btn btn-danger" type="button" data-dismiss="modal">
                                                {{trans ('clubes.cancelar')}}
                                                </button>
                                                <input type="submit" class="btn btn-primary" value="Gravar">

                                            </form>


                                        </div>


                                    </div>

                                    <div class="modal-footer">


                                    </div>
                                </div>
                                <!-- /.modal-content -->
                            </div>
                            <!-- /.modal-dialog -->
                        </div>


                        <div class="col-sm-12">
                            <table class="table">
                                <thead>
                                <th>{{trans ('general.gaiola')}}</th>
                                <th>{{trans ('general.classificacao')}}</th>
                                <th>{{trans ('general.premio')}}</th>

                                </thead>
                                <tbody>

                                @foreach($gaiolas as $g)
                                    <tr>
                                        <td>

                                            <button class="list-group-item" name="editar-{!! $g->id !!}" role="button"
                                                    data-id="{!! $g->id !!}" data-cl="{!! $g->classificacao !!}"
                                                    data-rescid="{!! $g->rel_evento_seccao_classe_id !!}"
                                                    data-numg="{!! $g->numgaiola !!}"
                                                    data-op="{!! $g->outro_premio !!}" data-toggle="modal"
                                                    data-target="#classificar">{!! $g->numgaiola !!}</button>

                                        </td>

                                        <td>

                                            {!! $g->classificacao !!}

                                        </td>
                                        <td>
                                            {!! $g->outro_premio !!}
                                        </td>

                                    </tr>

                                @endforeach


                                </tbody>


                            </table>


                        </div>


                    </div>

                </div>

            </div>
        </div>

    </div>
    <!-- Main content -->

    <br>
    <br>




@endsection

@section('scripts')

    <script>

        $(document).ready(function () {
            $("button[name^='editar-']").click(function () {

                var idp = $(this).data("id");
                var cl = $(this).data("cl");
                var pr = $(this).data("op");
                var rescid = $(this).data("rescid");
                var numg = $(this).data("numg");

                console.log("entrou: " + idp);
                console.log("entrou: " + cl);
                console.log("entrou: " + pr);

                $("form#form_classificar input[name='passaroid']").val(idp);
                $("form#form_classificar input[name='rescid']").val(rescid);
                $("form#form_classificar input[name='pclassificacao']").val(cl);
                $("form#form_classificar input[name='ppremio']").val(pr);
                $("span#fnumgaiola").html(numg);

            });

        });

    </script>

@endsection