<div class="row">
    <div class="col-md-6 col-sm-12 col-xs-12">

        <div class="list-group">
            <a class="list-group-item" target="_blank"
               href="<?php echo route('listamedalhas', [$evento->id, 'pais']); ?>">1. {{trans('clubes.lista_medalhas_pais')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('listatotaisclasses', [$evento->id, 'pais']); ?>">2. {{trans('clubes.lista_totais_classes')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdffichasinscricaoadmin', [$evento->id]); ?>">3. {{trans('general.fichas_de_engaiolamento')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdffichasinscricaoadminsort', [$evento->id, 'pais']); ?>">4. {{trans('general.fichas_de_engaiolamento_pais')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdffichasinscricaoadminsort', [$evento->id, 'local']); ?>">5. {{trans('general.fichas_de_engaiolamento_pais_local')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            {{--<button name="femodal" data-id="99" class="list-group-item">{{trans('general.ficha_engaiolamento_individual')}}...--}}
            {{--</button>--}}
            <button type="button" class="list-group-item" data-toggle="modal" data-target="#femodal2">6. {{trans('general.ficha_engaiolamento_individual')}}...</button>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('listageralpassarospais', [$evento->id]); ?>">7. {{trans('clubes.lista_geral_passaros_pais')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasprovisorias', [$evento->id]); ?>">8. {{trans('clubes.etiquetas_provisorias_todas')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasprovisoriasnapoles', [$evento->id, 1]); ?>">8.1. (Nap.) {{trans('clubes.etiquetas_provisorias_todas')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasprovisoriasnapoles', [$evento->id, 2]); ?>">8.2. (Nap.) ordem gaiolas - {{trans('clubes.etiquetas_provisorias_todas')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasprovisoriastipo', [$evento->id, 'equipas', 0]); ?>">9. {{trans('clubes.etiquetas_provisorias_equipas')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasprovisoriastipo', [$evento->id, 'individuais', 0]); ?>">10. {{trans('clubes.etiquetas_provisorias_individuais')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasprovisoriastipo', [$evento->id, 'equipas', 1]); ?>">11. {{trans('clubes.etiquetas_provisorias_equipas')}} (GP) (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank" href="<?php echo route('pdfetiquetasprovisoriastipo', [$evento->id, 'individuais', 1]); ?>">12. {{trans('clubes.etiquetas_provisorias_individuais')}} (GP) (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item"
               href="<?php echo route('etiquetasprovisoriasindividuais', [$evento->id]); ?>">13. {{trans('clubes.etiquetas_provisorias_individuais')}}
                 ...</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasdefinitivas', [$evento->id]); ?>">14. {{trans('clubes.etiquetas_definitivas')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasdefinitivasnapoles', [$evento->id, 1]); ?>">14.1. (Nap.). {{trans('clubes.etiquetas_definitivas')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasdefinitivasnapoles', [$evento->id, 2]); ?>">14.2. (Nap.) ordem gaiolas - {{trans('clubes.etiquetas_definitivas')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item"
               href="<?php echo route('etiquetasdefinitivasindividuais', [$evento->id]); ?>">15. {{trans('clubes.etiquetas_definitivas_individuais')}}
                 ...</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdffichascontrolojulgamento', [$evento->id]); ?>">16. {{trans('clubes.fichas_controlo_julgamento')}}(<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdffichascontrolojulgamentoseccaojuiz', [$evento->id]); ?>">17. {{trans('clubes.fichas_controlo_julgamento_seccao_juiz')}}(<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfcatalogoavesclasse', [$evento->id]); ?>">18. {{trans('clubes.catalogo_aves_por_classe')}}(<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfcatalogoavesclasse', [$evento->id,'1']); ?>">19. {{trans('clubes.catalogo_aves_por_classe').' (no classif) '}}(<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfcatalogoavesclassecondensado', [$evento->id]); ?>">20. {{trans('clubes.catalogo_aves_por_classe_condensado')}}(<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfcatalogoavesclassecondensadomundial', [$evento->id]); ?>">21. {{trans('clubes.catalogo_aves_por_classe_condensado_mundial')}}(<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdffichasdevolucao', [$evento->id]); ?>">22. {{trans('clubes.fichas_de_devolução_e_pagamentos')}}
                (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportanumgaiolastipo', [$evento->id, 'pdf']); ?>">23. {{trans('clubes.quantidade_gaiolas_por_tipo')}}
                 (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportanumgaiolastipo', [$evento->id, 'excel']); ?>">24. {{trans('clubes.quantidade_gaiolas_por_tipo')}}
                 (<i style="color: #207245" class="fa fa-table"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportanumgaiolastiposeccao', [$evento->id]); ?>">25. {{trans('clubes.quantidade_gaiolas_seccao_tipo')}}
                 (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportaexpositores', [$evento->id, '1']); ?>">26. {{trans('clubes.Expositores_ordenada_por_nome')}}
                 (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportaexpositores', [$evento->id, '2']); ?>">27. {{trans('clubes.Expositores_ordenada_por_num_expositores')}}
                 (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportaexpositorescontactos', [$evento->id]); ?>">28. {{trans('clubes.Expositores_contacto')}}
                (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportaexpositorescontactospais', [$evento->id]); ?>">29.{{trans('general.expositores_contactos_pais')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportaexpositorescontactospaisexcel', [$evento->id]); ?>">30. {{trans('general.expositores_contactos_pais')}} (<i
                        style="color: #207245" class="fa fa fa-table"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportaexpositoresgeralexcel', [$evento->id]); ?>">31. {{trans('general.expositores_info_geral')}} (<i
                        style="color: #207245" class="fa fa fa-table"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportanumextrasexpositores', [$evento->id]); ?>">32. {{trans('clubes.mesas_jantares_e_ministands')}}
                 (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportanumpassarosclasse', [$evento->id]); ?>">33. {{trans('clubes.numero_de_passaros_seccao_classe')}}
                 (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportaexpositoresdespesas', [$evento->id]); ?>">34. {{trans('clubes.Expositores_despesas_totais')}}
                Totais (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportaexpositoresdespesas', [$evento->id, 'excel']); ?>">35. {{trans('clubes.Expositores_despesas_totais')}}
                 (<i style="color: #207245" class="fa fa-table"></i>)</a>

            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportacontroloanilhas', [$evento->id]); ?>">36. {{trans('clubes.controlo_de_anilhas')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportapremioscriador', [$evento->id]); ?>">37. {{trans('clubes.premios_por_criador')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportadiplomas', [$evento->id, 'vencedores']); ?>">38. {{trans('clubes.diplomas_de_vencedores')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportadiplomas', [$evento->id, 'vencedores', 1, 'pais']); ?>">39. {{trans('clubes.diplomas_de_vencedores_pais')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportadiplomas', [$evento->id, 'vencedores', 1, 'seccao']); ?>">40. {{trans('clubes.diplomas_de_vencedores_seccao')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportadiplomas', [$evento->id, 'participacao']); ?>">41. {{trans('clubes.diplomas_de_participacao')}}
                 (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportadiplomas', [$evento->id, 'participacao', 1, 'pais']); ?>">42. {{trans('clubes.diplomas_de_participacao_pais')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportadiplomas', [$evento->id, 'participacao', 1, 'seccao']); ?>">43. {{trans('clubes.diplomas_de_participacao_seccao')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportadiplomas', [$evento->id, 'vencedores', 0]); ?>">44. {{trans('clubes.diplomas_de_vencedores')}} {{trans('clubes.sem_logos')}}
                  (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportadiplomas', [$evento->id, 'participacao', 0]); ?>">45. {{trans('clubes.diplomas_de_participacao')}} {{trans('clubes.sem_logo')}}
                 (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportapremiosprestigio', [$evento->id]); ?>">46. {{trans('clubes.premios_prestigios')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

            <a class="list-group-item"  target="_blank" href="<?php echo route('pdffichasjulgamentoevento', [$evento->id]); ?>">47. {{trans('clubes.fichas_de_julgamneto_detalhadas')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item"  target="_blank" href="<?php echo route('pdffichasjulgamentoevento', [$evento->id, 'pais']); ?>">48. {{trans('clubes.fichas_de_julgamneto_detalhadas_pais')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item"  target="_blank" href="<?php echo route('pdffichasjulgamentoevento', [$evento->id, 'seccao']); ?>">49. {{trans('clubes.fichas_de_julgamneto_detalhadas_seccao')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

            <a class="list-group-item" target="_blank"
               href="<?php echo route('exportaexpositoresclassificacoes', [$evento->id]); ?>">50. {{trans('clubes.listagem_expositores_passaros_Classificacoes')}} (<i style="color: #207245" class="fa fa-table"></i>)</a>

            <a class="list-group-item" target="_blank" href="<?php echo route('pdffichasdevolucaopais', [$evento->id]); ?>">51. {{trans ('clubes.fichas_de_devolução_e_pagamentos_pais')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
            <a class="list-group-item" target="_blank"
               href="<?php echo route('pdfetiquetasescaparates', [$evento->id]); ?>">52. {{trans('clubes.etiquetas_escaparates')}} (<i
                        style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

        </div>
    </div>
    <div class="col-md-3 col-sm-3 col-xs-12">

        <form onsubmit="return validarF()" id="form_procurar_gaiola" action="{!! route('procurar_gaiola') !!}"
              method="post">
            <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
            <input type="hidden" name="evento_id" value="<?php echo $evento->id; ?>">
            <label for="numgaiola">Nº Gaiola</label>
            <input type="text" size="4" class="form-control" name="numgaiola" placeholder="Nº Gaiola">
            <input type="submit" class="btn btn-info" value="Procurar">
        </form>
        <br><br>

    </div>
    <div class="modal fade" id="femodal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="display: none;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">{{trans ('general.ficha_de_engaiolamento')}}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form target="_blank" id="form_ficha_engaiolamento" action="<?php echo route('exportafichaengaiolamentoindividual'); ?>" method="post">
                        <input type="hidden" name="eeventoid" value="{{$evento->id}}">
                        <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                        <table class="table table-condensed">
                            <thead>
                            <tr>
                                <th>{{trans ('general.num_expositor')}}</th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr>

                                <td><input type="text" size="3" class="form-control" name="numexpositor" placeholder="Nº Expositor"></td>

                                <td><input type="submit" class="btn btn-info" value="Imprimir"></td>

                            </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{trans ('clubes.cancelar')}}</button>
                </div>
            </div>
        </div>
    </div>
    @role('admin')
    <div class="col-md-3 col-sm-3 col-xs-12">

        <a href="{!! route('recalcularclassificacoes',[$evento->id]) !!}" class="btn btn-primary">Recalcular
            Classificações</a>


    </div>
    @endrole
</div>