@extends('layouts.app')


@section('titulo',trans('general.julgamento_evento'))
@section('subtitulo',$evento->nome)
@section('page_css')
    <link href="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.css')}}" rel="stylesheet"
          type="text/css"/>

@endsection

@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('homecriador'); ?>">{{trans('menus.home')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span> {{ trans('general.julgamento') }} {{$evento->nome}}</span>
        </li>
    </ul>

@endsection

@section('content')
    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{ trans('general.seccoes_atribuidas') }}
                        </div>

                    </div>
                    <div class="portlet-body">
                        <div class="form-group row" >
                        @foreach($seccoesatribuidas as $sa)

                                <a href="{!! route('julgareventoseccao',[$evento->id,$sa->seccao]) !!}"
                                   style="margin-left: 5px;height: 60px;display: inline-block; align: top;" class="col-sm-2 btn btn-primary btn-lg"
                                   role="button">{!! $sa->seccao !!}</a>

                        @endforeach
                        </div>
                    </div>

                </div>


            </div>


        </div>

    </div>
    <!-- Main content -->

    <br>
    <br>




@endsection
