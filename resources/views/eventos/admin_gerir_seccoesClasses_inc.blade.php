<div class="row">
    <div class="col-md-12">
        <div class="note note-warning">
            <h4><i class="icon fa fa-ban"></i> {{trans ('general.cuidado')}}</h4>
            {{trans ('general.gerir_seccoes_txt1')}}
        </div>
        <div class="col-md-6">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-upload"></i>
                        <span class="caption-subject bold font-yellow-crusta uppercase"> {{trans ('general.importar')}}</span>
                        <span class="caption-helper">{{trans ('general.seccoes_classes')}}</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="col-md-12">
                            <form action="{{ route('importexcelseccoes') }}"
                                  class="form-horizontal" method="post" enctype="multipart/form-data">
                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                <input type="hidden" name="evento_id" value="{{ $evento->id }}">
                                <div class="form-group col-xm-12 col-md-2">
                                    <input type="file" name="import_file"/><br>
                                    <button class="btn btn-primary">{{trans ('general.importar_ficheiro')}}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-upload"></i>
                        <span class="caption-subject bold font-green-dark uppercase"> {{trans ('general.exportar')}}</span>
                        <span class="caption-helper">{{trans ('general.seccoes_classes')}}</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="col-md-12">
                            <a href="{{ route('downloadexcelclasses',[$evento->id,'xlsx']) }}">
                                <button class="btn btn-default">{{trans ('general.download_seccoes_classes')}} (<i style="color: #207245"
                                                                                             class="fa fa-table"></i>)
                                </button>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div id="grid_classes" style="width: 800px;height: 450px;"></div>
        </div>
    </div>
</div>

