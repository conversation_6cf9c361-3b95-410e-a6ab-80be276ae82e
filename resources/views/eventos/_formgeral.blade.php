<div id="evtformconteudo" class="tabbable-custom" >
    <ul class="nav nav-tabs ">
        <li class="active">
            <a href="#tab1" data-toggle="tab" aria-expanded="false"> {{trans('eventos.geral')}} </a>
        </li>
        <li class="">
            <a href="#tab2" data-toggle="tab" aria-expanded="true"> {{trans('eventos.valores_inscricoes')}} </a>
        </li>
        <li class="">
            <a href="#tab3" data-toggle="tab" aria-expanded="false"> {{trans('eventos.outros_valores')}} </a>
        </li>
        <li class="">
            <a href="#tab4" data-toggle="tab" aria-expanded="false"> {{trans('general.pontos')}} </a>
        </li>
        <li class="">
            <a href="#tab5" data-toggle="tab" aria-expanded="false"> {{trans('general.clubes_organizadores')}} </a>
        </li>
        <li class="">
            <a href="#tab6" data-toggle="tab" aria-expanded="false"> {{trans('general.locais_recolha')}} </a>
        </li>
    </ul>
    <div class="tab-content">

        <div class="tab-pane active" id="tab1">
            <div class="row">

                <div class="form-group col-md-12">

                    <div class="col-md-12">
                        <div class="form-group col-md-5">
                            {!! Form::label('tipo_evento_id',trans('general.tipo_evento')) !!}
                            <select id="tipo_evento_id" name="tipo_evento_id" class="form-control col-sm-6" data-live-search="true">



                                @foreach($tipos_evento as $te)
                                    <option value="{{ $te->id }}" {{($tipo!='create' && $te->id==$evento->tipo_evento_id)?'selected':''}}>{{$te->tipo_evento}}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form-group col-md-3">

                                <label class="col-md-10 control-label">{{trans('general.utiliza_caixas')}}:</label><br>
                                <div class="mt-radio-inline">
                                    <label class="mt-radio">
                                        {!! Form::radio('utiliza_caixas', 0, true); !!}{{trans('general.nao')}}
                                        <span></span>
                                    </label>
                                    <label class="mt-radio">
                                        {!! Form::radio('utiliza_caixas', 1); !!}{{trans('general.sim')}}

                                        <span></span>
                                    </label>
                                </div>

                        </div>
                        <div class="form-group col-md-4">

                            <label class="col-md-10 control-label">{{trans('general.utiliza_qrcode')}}:</label><br>
                            <div class="mt-radio-inline">
                                <label class="mt-radio">
                                    {!! Form::radio('utiliza_qrcode', 0, true); !!}{{trans('general.nao')}}
                                    <span></span>
                                </label>
                                <label class="mt-radio">
                                    {!! Form::radio('utiliza_qrcode', 1); !!}{{trans('general.sim')}}

                                    <span></span>
                                </label>
                            </div>

                        </div>
                    </div>

                </div>
                <div class="form-group col-md-12">

                    <div class="col-md-3">
                        <label class="col-md-2 control-label">{{trans('general.estado')}}:</label><br>
                        <div class="mt-radio-inline">
                            <label class="mt-radio">
                                {!! Form::radio('estado', 1); !!}{{trans('general.activo')}}
                                <span></span>
                            </label>
                            <label class="mt-radio">
                                {!! Form::radio('estado', 2, true); !!}{{trans('general.inactivo')}}

                                <span></span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-9">
                        {!! Form::label('nome',trans('forms.nome')) !!}
                        {!! Form::text('nome', null, ['class'=> 'form-control' ]) !!}
                    </div>
                </div>

                <div class="form-group col-md-4" style="border-right: 1px solid #bbbbbb">
                    {!! Form::label('data_inicio',trans('forms.data_inicio')) !!}
                    <div class="input-group">
                        {!! Form::text('data_inicio', null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                    <br>
                    {!! Form::label('data_fim',trans('forms.data_fim')) !!}
                    <div class="input-group">
                        {!! Form::text('data_fim', null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4" style="border-right: 1px solid #bbbbbb">
                    {!! Form::label('data_inicio_inscricoes',trans('general.data_inicio_inscricoes')) !!}
                    <div class="input-group">
                        {!! Form::text('data_inicio_inscricoes', null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                    <br>
                    {!! Form::label('data_fim_inscricoes',trans('general.data_fim_inscricoes')) !!}
                    <div class="input-group">
                        {!! Form::text('data_fim_inscricoes', null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('data_inicio_julg',trans('general.data_inicio_julgamento'))!!}
                    <div class="input-group">
                        {!! Form::text('data_inicio_julg', null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                    <br>
                    {!! Form::label('data_fim_julg',trans('general.data_fim_julgamento')) !!}
                    <div class="input-group">
                        {!! Form::text('data_fim_julg', null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane" id="tab2">
            <div class="row">
                <div class="form-group col-md-6">
                {!! Form::label('iben',"IBAN")!!}
                <div class="input-group">
                    {!! Form::text('iban', ($tipo=='create')?'':null, ['class'=> 'form-control' ]) !!}
                    </span>
                </div>

                </div>
            </div>
            <div class="row">
                <div class="form-group col-md-4">
                    {!! Form::label('preco_inscricao_ind',trans('general.preco_para_nao_socio')) !!}
                    <div class="input-group">
                        {!! Form::text('preco_inscricao_ind', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('preco_inscricao_equipa',trans('general.preco_equipas_para_nao_socios')) !!}
                    <div class="input-group">
                        {!! Form::text('preco_inscricao_equipa', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('preco_inscricao_ave_feira',trans('general.preco_ave_feira')) !!}
                    <div class="input-group">
                        {!! Form::text('preco_inscricao_ave_feira', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('preco_inscricao_ind_socio', trans('general.preco_para_socio')) !!}
                    <div class="input-group">
                        {!! Form::text('preco_inscricao_ind_socio', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('preco_inscricao_equipa_socio',trans('general.preco_para_equipas'))  !!}
                    <div class="input-group">
                        {!! Form::text('preco_inscricao_equipa_socio', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('preco_inscricao_catalogo',trans('general.preco_catalogo')) !!}
                    <div class="input-group">
                        {!! Form::text('preco_inscricao_catalogo', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="form-group col-md-4">
                    {!! Form::label('paiseslista',trans('general.pais')) !!}
                    <select id="paiseslista" name="paiseslista" class="form-control col-sm-6" data-live-search="true">
                        @foreach($paises as $p)
                            <option value="{{ $p->id }}">{{$p->pais}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('valor_sobretaxa_pais',trans('forms.valor_sobretaxa')) !!}
                    <div class="input-group">
                        {!! Form::text('valor_sobretaxa_pais', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4" style="padding-top: 5px;"><br>
                    <button type="button" id="adicionarsobretaxapaisbt" class="btn">{{trans('forms.adicionar_sobretaxa_pais')}}</button>
                </div>


            </div>
            <hr>
            <div class="row">
                <div id="paisesescolhas" style="overflow-y: auto">
                    @if( isset($sobretaxaspaises))
                        @foreach($sobretaxaspaises as $sp)
                            <span class="col-md-6" id="ps{{ $sp->pais_id }}" ><span class="col-md-6" style="height: 34px;padding-top: 5px">{{ $sp->pais }}</span><span class="col-md-6">{{ $sp->taxa }}<a onclick="removePaisSobretaxa('{{ $sp->pais_id }}')" href="#" id="btps{{ $sp->id }}" class="btn"><i class="fa fa-trash font-red"></i></a></span></span>

                        @endforeach
                    @endif
                </div>



            </div>
        </div>
        <div class="tab-pane" id="tab3">
            <div class="row">
                <div class="form-group col-md-4">
                    {!! Form::label('preco_jantar',trans('general.preco_jantar'))!!}
                    <div class="input-group">
                        {!! Form::text('preco_jantar', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('preco_mesa_venda',trans('general.preco_mesa')) !!}
                    <div class="input-group">
                        {!! Form::text('preco_mesa_venda', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('percentagem_venda_aves',trans('general.percentagem_venda_aves')) !!}
                    <div class="input-group">
                        {!! Form::text('percentagem_venda_aves', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-percent"></i></span>
                    </div>
                </div>

                <div class="form-group col-md-4">
                    {!! Form::label('preco_stand',trans('general.preco_stand')) !!}
                    <div class="input-group">
                        {!! Form::text('preco_stand', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('desconto_gaiola_propria',trans('general.desconto_gaiola_propria')) !!}
                    <div class="input-group">
                        {!! Form::text('desconto_gaiola_propria', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-eur"></i></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    {!! Form::label('percentagem_venda_aves_feira',trans('general.percentagem_venda_aves_feira')) !!}
                    <div class="input-group">
                        {!! Form::text('percentagem_venda_aves_feira', ($tipo=='create')?'0':null, ['class'=> 'form-control' ]) !!}
                        <span class="input-group-addon"><i class="fa fa-percent"></i></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane" id="tab4">
            <div class="row">
                <div class="form-group col-md-12">
                    {!! Form::label('pontos_ind_minimos_premio',trans('general.pontos_ind_minimos_premio')) !!}
                    <div class="input-group">
                        {!! Form::text('pontos_indiv_minimos_premio', ($tipo=='create')?'87':null, ['class'=> 'form-control' ]) !!}

                    </div>
                </div>
                <div class="form-group col-md-12">
                    {!! Form::label('pontos_equipa_minimos_premio',trans('general.pontos_equipa_minimos_premio')) !!}
                    <div class="input-group">
                        {!! Form::text('pontos_equipa_minimos_premio', ($tipo=='create')?'354':null, ['class'=> 'form-control' ]) !!}

                    </div>
                </div>
            </div>

        </div>
        <div class="tab-pane" id="tab5">
            <div class="row">
                <div class="form-group col-md-6">
                    {{trans('general.clubes_associacoes')}}:
                    <br><br>
                    <select id="clubeslista" name="clubeslista" class="form-control col-sm-6" data-live-search="true">
                        @foreach($clubes as $c)
                            <option value="{{ $c->id }}">{{$c->sigla}} | {{ $c->nome }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group col-md-6"><br><br>
                    <button type="button" id="associarclubebt" class="btn">{{trans('forms.associar_clube_ao_evento')}}</button>
                </div>
            </div>
            <hr>
            <div class="row">
                <div id="clubesescolhas">
                    @if( isset($clubesorganizadores))
                        @foreach($clubesorganizadores as $c)
                            <span class="col-md-12" id="c{{ $c->clube_id }}"><span class="col-md-9">{{ $c->nome }}</span><span class="col-md-3"><a onclick="removeClube('{{ $c->clube_id }}')" href="#" id="bt{{ $c->clube_id }}" class="btn"><i class="fa fa-trash font-red"></i></a></span></span>

                        @endforeach
                    @endif


                </div>



            </div>

        </div>
        <div class="tab-pane" id="tab6">
            <div class="row">
                <div class="form-group col-md-12" style="margin-bottom: 0">
                    {{trans('general.locais_recolha')}}:
                    <hr>
                </div>
            </div>
            <div class="row">
                <div class="form-group col-md-2">
                    {!! Form::label('paiseslista',trans('general.pais')) !!}
                    <select id="lrpaiseslista" name="lrpaiseslista" class="form-control col-sm-6" data-live-search="true">
                        @foreach($paises as $p)
                            <option value="{{ $p->id }}">{{$p->pais}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group col-md-3">
                    <label for="local_nome">{{trans('general.nome')}}</label>
                    <div class="input-group">
                        <input class="form-control" style="width: 250px;" name="local_nome" type="text" value="" id="local_nome">

                    </div>
                </div>
                <div class="form-group col-md-4">
                    <label for="local_morada">{{trans('general.morada')}}</label>
                    <div class="input-group">
                        <input class="form-control" style="width: 300px;" name="local_morada" type="text" value="" id="local_morada">

                    </div>
                </div>
                <div class="form-group col-md-3">
                    <br>
                    <button type="button" id="adicionarlocalrecolhabt" class="btn">{{trans('forms.adicionar_local_recolha')}}</button>
                </div>
            </div>
            <hr>
            <div class="row">
                <div id="locaisrecolha">
                    @if( isset($locaisrecolha))
                        @foreach($locaisrecolha as $lr)
                            <span class="col-md-6" id="lr{{ $lr->id }}"><span class="col-md-9">{{ $lr->local_nome }} ({{$lr->paisnome}})</span><span class="col-md-3"><a onclick="removeLocalRecolha('{{ $lr->id }}')" href="#" id="btlr{{ $lr->id }}" class="btn"><i class="fa fa-trash font-red"></i></a></span></span>

                        @endforeach
                    @endif
                </div>



            </div>

        </div>


    </div>
</div>



