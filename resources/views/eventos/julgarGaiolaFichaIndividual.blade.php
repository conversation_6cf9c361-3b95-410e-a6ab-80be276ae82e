@extends('layouts.app')


@section('titulo',trans('general.julgamento_evento'))
@section('subtitulo',$evento->nome)


@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('homecriador'); ?>">{{trans('menus.home')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('julgareventohome',[$evento->id]) !!}">{{trans('general.julgamento')}} {{$evento->nome}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('julgareventoseccao',[$evento->id,$seccaoclasse->seccao]) !!}">{{trans('general.julgamento_seccao')}} {!! $seccaoclasse->seccao !!}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('julgareventogaiolas', ['id_evento' => $evento->id,'rescid' => $passaro->rescid]) !!}">{{trans('general.gaiolas_em')}} {!! $seccaoclasse->seccao !!}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{trans('general.gaiola')}}: {!! $numg !!}
                - {!! $seccaoclasse->seccao." ".$seccaoclasse->classe !!}</span>
        </li>
    </ul>
@endsection

@section('content')
    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{trans('general.gaiola')}}: {!! $numg !!} -
                            ({!! $seccaoclasse->seccao." ".$seccaoclasse->classe !!})
                        </div>

                    </div>
                    <div class="portlet-body">
                        <div class="col-sm-12" style="margin-bottom: 10px;">
                            <a href="{!! route('julgareventogaiolas', ['id_evento' => $evento->id,'rescid' => $passaro->rescid]) !!}"
                               class="col-sm-3 btn btn-primary"
                               role="button">< {{trans('general.regressar_gaiolas')}}</a>


                        </div>
                        <div class="form-group">
                            <div class="table-scrollable">
                                <form onsubmit="return validarF()" id="form_pontuar_passaros"
                                      action="{!! route('gravargaiolapontuacao') !!}" method="post">
                                    <input type="hidden" name="eventoid" value="{{$evento->id}}">
                                    <input type="hidden" name="passaroid" value="{!! $passaro->id !!}">
                                    <input type="hidden" name="rescid" value="{!! $passaro->rescid !!}">
                                    <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <th></th>
                                        <th style="text-align: center;">{{trans('general.maximo')}}</th>
                                        <th style="text-align: center;">{{trans('general.pontos')}}</th>


                                        </thead>
                                        <tbody>
                                        @for($i=0;$i<count($rubricas);$i++)
                                            <tr>
                                                <td class="nome_rubrica">{!! $rubricas[$i]->nome !!}</td>
                                                <td style="text-align: center;"
                                                    id="mval-{!! $rubricas[$i]->id !!}">{!! $rubricas[$i]->valor_max !!}</td>
                                                <td style="text-align: center;font-size: 28px;vertical-align: top">
                                                    <button name="btd-{!! $rubricas[$i]->id !!}"
                                                            data-max="{!! $rubricas[$i]->valor_max !!}"
                                                            data-val="val-{!! $rubricas[$i]->id !!}" data-calc="-1"
                                                            style="margin-right: 20px;" class="btn btn-default btn-lg green-soft"
                                                            type="button"><span class="glyphicon glyphicon-chevron-left"
                                                                                aria-hidden="true"></span></button>

                                                    <input type="text" size=6 name="val-{!! $rubricas[$i]->id !!}"
                                                           readonly value="0">


                                                    <button name="btu-{!! $rubricas[$i]->id !!}"
                                                            data-max="{!! $rubricas[$i]->valor_max !!}"
                                                            data-val="val-{!! $rubricas[$i]->id !!}" data-calc="1"
                                                            style="margin-left: 20px;" class="btn btn-default btn-lg green-soft"
                                                            type="button"><span
                                                                class="glyphicon glyphicon-chevron-right"
                                                                aria-hidden="true"></span></button>
                                                </td>

                                            </tr>


                                        @endfor

                                        <tr>
                                            <td>
                                                <input name="est1" style="margin-right: 20px;"
                                                       class="btn btn-info btn-lg" data-tipo="NJ" data-width="75"
                                                       data-height="50" type="checkbox" data-toggle="toggle"
                                                       data-on="NJ" data-off="NJ" data-onstyle="warning"
                                                       data-offstyle="primary">

                                                <input name="est2" style="margin-right: 20px;"
                                                       class="btn btn-info btn-lg" data-tipo="DQ" data-width="75"
                                                       data-height="50" type="checkbox" data-toggle="toggle"
                                                       data-on="DQ" data-off="DQ" data-onstyle="warning"
                                                       data-offstyle="primary">

                                                <input name="est3" style="margin-right: 20px;"
                                                       class="btn btn-info btn-lg" data-tipo="FC" data-width="75"
                                                       data-height="50" type="checkbox" data-toggle="toggle"
                                                       data-on="DC" data-off="DC" data-onstyle="warning"
                                                       data-offstyle="primary">
                                                <button name="est4" style="margin-right: 20px;"
                                                        class="btn btn-default btn-lg" data-tipo="--" type="button">
                                                    Reset
                                                </button>
                                            </td>
                                            <td style="text-align: center;">100</td>
                                            <td style="text-align: center;"><input type="text" size=6 name="total1"
                                                                                   readonly value="0"></td>


                                        </tr>

                                        <tr>
                                            <td></td>
                                            <td></td>
                                            <td style="text-align: center;">


                                            </td>


                                        </tr>
                                        <tr>


                                            @if (count($pontuacao)===0)
                                                <td><input type="text" size="10" class="form-control" name="observacao_julgamento"
                                                           placeholder="Observação"></td>
                                                <td>{{trans('general.estado')}}<input type="text" size=6 name="estado"
                                                                                      readonly value=""></td>
                                            @else
                                                <td><input type="text" size="10" class="form-control" name="observacao_julgamento"
                                                           value="{!! $passaro->observacao_julgamento !!}"
                                                           placeholder="Observação"></td>
                                                <td>{{trans('general.estado')}}<input type="text" size=6 name="estado"
                                                                                      readonly
                                                                                      value="{!! $passaro->estado !!}">
                                                </td>
                                            @endif
                                            <td style="text-align: center;"><input type="submit"
                                                                                   class="btn btn-primary btn-lg"
                                                                                   value="{{trans('forms.gravar')}}">
                                            </td>

                                        </tr>
                                        </tbody>
                                    </table>


                                </form>
                            </div>
                            <div class="col-sm-3">

                            </div>


                        </div>

                    </div>

                </div>
            </div>

        </div>
    </div>




    <!-- Main content -->

    <br>
    <br>




@endsection

@section('scripts')


    <script>
        calculaTotal();

        function calculaTotal() {

            var total = 0;
            var valores = $(":input[name^='val-']").each(function () {
                total = total + parseInt($(this).val());
            });


            $("form input[name='total1']").val(total);
        }


        function validarF() {

            var valores;


        }

        function limparPontos() {
            $(":input[name^='val-']").each(function () {
                $(this).val(0);
            });
            calculaTotal();

        }

        function reporValoresReferencia() {
            @for($i=0;$i<count($rubricas);$i++)
            $("form input[name='val-{!! $rubricas[$i]->id !!}']").val('{!! $rubricas[$i]->valor_ref !!}');
            $("form input[name='val-{!! $rubricas[$i]->id !!}']").val('{!! $rubricas[$i]->valor_ref !!}');
            $("form input[name='val-{!! $rubricas[$i]->id !!}']").val('{!! $rubricas[$i]->valor_ref !!}');
            $("form input[name='val-{!! $rubricas[$i]->id !!}']").val('{!! $rubricas[$i]->valor_ref !!}');
            @endfor




            $("form input[name^='est1']").bootstrapToggle('off');
            $("form input[name^='est2']").bootstrapToggle('off');
            $("form input[name^='est3']").bootstrapToggle('off');
            $("form input[name='estado']").val("");
            $("form input[name='observacao_julgamento']").val("");

        }


        @if(count($pontuacao)!==0)
        function reporValoresGravados() {

                @for($i=0;$i<count($rubricas);$i++){

                $("form input[name='val-{!! $rubricas[$i]->id !!}']").val('{!! $pontuacao[$i]->pontos !!}');
console.log({!! $rubricas[$i]->id !!});
console.log({!! $pontuacao[$i]->pontos !!});

            }


            @endfor

            $("form input[name='estado']").val("");
            $("form input[name='observacoes']").val("");

            @if($passaro->nj==1)

            $("form input[name^='est1']").bootstrapToggle('on');
            @else
            $("form input[name^='est1']").bootstrapToggle('off');

            @endif
            @if($passaro->dq==1)

            $("form input[name^='est2']").bootstrapToggle('on');
            @else
            $("form input[name^='est2']").bootstrapToggle('off');

            @endif
            @if($passaro->fc==1)

            $("form input[name^='est3']").bootstrapToggle('on');
            @else
            $("form input[name^='est3']").bootstrapToggle('off');

            @endif





        }

        @endif

        $(document).ready(function () {


            @if(count($pontuacao)==0)
            reporValoresReferencia();
            @else

            reporValoresGravados();
            @endif

            calculaTotal();

            $("form input[name^='est']").change(function () {


                var tipo = $(this).data("tipo");


                if (tipo == '--') {


                }
                if (tipo == 'NJ') {
                    if (this.checked) {
                        limparPontos();

                        $("form input[name='estado']").val("NJ");
                    } else {
                        $("form input[name='estado']").val("");

                    }


                }
                if (tipo == 'DQ') {
                    if (this.checked) {
                        limparPontos();
                        $("form input[name='estado']").val("DQ");
                    } else {
                        $("form input[name='estado']").val("");

                    }
                }
                if (tipo == 'FC') {
                    if (this.checked) {
                        $("form input[name='estado']").val("DC");
                    } else {
                        $("form input[name='estado']").val("");

                    }

                }


                calculaTotal();


            });


            $("form button[name^='est']").click(function () {


                var tipo = $(this).data("tipo");


//                if (tipo == '') {
//                    limparPontos();
//                    $("form input[name='estado']").val("");
//
//                }
//                if (tipo == 'NJ') {
//                    limparPontos();
//                    $("form input[name='estado']").val("NJ");
//
//                }
//                if (tipo == 'DQ') {
//                    limparPontos();
//                    $("form input[name='estado']").val("DQ");
//
//                }
//                if (tipo == 'FC') {
//                    $("form input[name='estado']").val("FC");
//
//                }
                if (tipo == '--') {
                    // valores referencia
                    var result = confirm("Tem a certeza que pretende reverter todas as alterações para os valores de referência?");
                    if (result) {
                        @for($i=0;$i<count($rubricas);$i++)
                        $("form input[name='val-{!! $rubricas[$i]->id !!}']").val('{!! $rubricas[$i]->valor_ref !!}');
                        @endfor
                        $("form input[name='estado']").val("");
                        $("form input[name='observacoes']").val("");
                    }

                }
                calculaTotal();
            });


            $("form button[name^='bt']").click(function () {
                if ($("form input[name='estado']").val() == 'FC' || $("form input[name='estado']").val() == '') {
                    var target = $(this).data("val");
                    var c = $(this).data("calc");
                    var max = $(this).data("max");

                    var res = parseInt($("form input[name='" + target + "']").val()) + c;

                    if (res <= max && res >= 0) {

                        $("form input[name='" + target + "']").val(res);
                        calculaTotal();
                    }

                }

            });

        });

    </script>

@endsection