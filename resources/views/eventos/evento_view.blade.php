<div class="form-group row">
    <label class="col-sm-2 bold">{{trans('general.estado')}}</label>
    <div class="col-sm-3">
        {{($evento->estado==1)?trans('general.activo'):trans('general.pendente')}}
    </div>
    <label class="col-sm-2 bold">{{trans('general.tipo_evento')}}</label>
    <div class="col-sm-3">
        {{$evento->tipo_evento}}
    </div>
</div>

<div class="form-group row">
    <label class="col-sm-2 bold">{{trans('general.data_inicio_inscricoes')}}</label>
    <div class="col-sm-3">
        {{$evento->data_inicio_inscricoes}}
    </div>
    <label class="col-sm-2 bold">{{trans('general.data_fim_inscricoes')}}</label>
    <div class="col-sm-3">
        {{$evento->data_fim_inscricoes}}
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-2 bold">{{trans('general.data_inicio')}}</label>
    <div class="col-sm-3">
        {{$evento->data_inicio}}
    </div>
    <label class="col-sm-2 bold">{{trans('general.data_fim')}}</label>
    <div class="col-sm-3">
        {{$evento->data_fim}}
    </div>
</div>

<div class="form-group row">
    <label class="col-sm-2 bold">{{trans('general.data_inicio_julgamento')}}</label>
    <div class="col-sm-3">
        {{$evento->data_inicio_julg}}
    </div>
    <label class="col-sm-2 bold">{{trans('general.data_fim_julgamento')}}</label>
    <div class="col-sm-3">
        {{$evento->data_fim_julg}}
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-2 bold">{{trans('general.tipo_evento')}}</label>
    <div class="col-sm-3">
        {{$evento->tipo_evento}}
    </div>
    <label class="col-sm-2 bold"></label>
    <div class="col-sm-3">

    </div>
</div>

<div class="form-group row">
    <label class="col-sm-2 bold">{{trans('general.preco_para_nao_socio')}}</label>
    <div class="col-sm-3">
        {{$evento->preco_inscricao_ind}}€
    </div>
    <label class="col-sm-2 bold">{{trans('general.preco_equipas_para_nao_socios')}}</label>
    <div class="col-sm-3">
        {{$evento->preco_inscricao_equipa}}€
    </div>
</div>
@if((int)$evento->tipo_evento_id!==6)
    <div class="form-group row">
        <label class="col-sm-2 bold">{{trans('general.preco_para_socio')}}</label>
        <div class="col-sm-3">
            {{$evento->preco_inscricao_ind_socio}}€
        </div>
        <label class="col-sm-2 bold">{{trans('general.preco_para_equipas')}}</label>
        <div class="col-sm-3">
            {{$evento->preco_inscricao_equipa_socio}}€
        </div>

    </div>



@endif

<div class="form-group row">
    <label class="col-sm-2 bold">{{trans('general.preco_catalogo')}}</label>
    <div class="col-sm-3">
        {{$evento->preco_inscricao_catalogo}}€
    </div>
    <label class="col-sm-2 bold">{{trans('general.preco_jantar')}}</label>
    <div class="col-sm-3">
        {{$evento->preco_jantar}}€
    </div>

</div>
@if((int)$evento->tipo_evento_id!==6)
    <div class="form-group row">
        <label class="col-sm-2 bold">{{trans('general.preco_mesa')}}</label>
        <div class="col-sm-3">
            {{$evento->preco_mesa_venda}}€
        </div>
        <label class="col-sm-2 bold">{{trans('general.preco_stand')}}</label>
        <div class="col-sm-3">
            {{$evento->preco_stand}}€
        </div>

    </div>
@endif
@if((int)$evento->tipo_evento_id===6 && isset($sobretaxa_pais))
    <div class="form-group row">
        <label class="col-sm-2 bold">{{trans('general.sobretaxa_pais')}}</label>
        <div class="col-sm-3">
            {{$sobretaxa_pais->taxa}}€
        </div>
        <label class="col-sm-2 bold"></label>
        <div class="col-sm-3">

        </div>

    </div>
@endif
