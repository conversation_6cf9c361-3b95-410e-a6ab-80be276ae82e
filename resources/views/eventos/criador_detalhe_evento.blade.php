@extends('layouts.app')

@section('page_css')
    <link href="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.css')}}" rel="stylesheet"
          type="text/css"/>
@endsection
@section('titulo','Gestão de Evento')
@section('subtitulo','')

@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="{{URL::route('homecriador')}}">{{trans ('clubes.inicio')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{trans ('clubes.gestao_de_evento')}}</span>
        </li>
    </ul>

@endsection

@section('content')

    <section id="evento">


        <div class="content body">
            <div class="row">
                <div class="col-sm-12">
                    <div class="portlet box blue">
                        <div class="portlet-title">
                            <div class="caption">
                                <i class=" icon-layers font-green"></i>
                                <span class="caption-subject bold uppercase">{{$evento->nome}}</span>
                            </div>

                        </div>

                        <div class="portlet-body">
                            @include("eventos.evento_view")


                        </div>


                    </div>


                    <div class="portlet box grey">
                        <div class="portlet-title">
                            <div class="caption font-blue-dark">
                                <span class="caption-subject bold uppercase">{{trans('eventos.inscricao')}}</span>
                            </div>
                            <div class="actions">
                                <div class="btn-group btn-group-devided" data-toggle="buttons">

                                </div>
                            </div>
                        </div>
                        <div class="portlet-body">
                            @if($inscricao==null)
                                <div class="form-actions">
                                    @if($evento->editavelcriador)

                                        <form id="forminsc"
                                              action="<?php echo route('gravarinscricaocriadorevento'); ?>"
                                              method="post">
                                            <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                                            <input type="hidden" name="eventoid" value="{{$evento->id}}">
                                            <input type="hidden" name="id_criador" value="{{$user_id}}">
                                            <input type="hidden" name="redirectto" value="">
                                            <input type="submit" class="btn btn-info"
                                                   value="{{trans('eventos.botao_inscrever_em_evento')}}">

                                        </form>

                                    @else
                                        {{trans('eventos.txt_nao_se_inscreveu')}}
                                    @endif

                                </div>
                                <div class="modal fade bs-modal-sm" id="ajax" role="basic" style="display: none;">
                                    <div class="modal-dialog modal-sm">
                                        <div class="modal-content">
                                            <div class="modal-body">
                                                <span> &nbsp;&nbsp;Loading... </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            @else
                                @include("eventos.inscricao_view")
                                <div id="inline" style="display:none;width:500px;">
                                    <p class="text-center"><h5>{{trans('eventos.remover_inscricao_pergunta')}}</h5></p>
                                    <form id="form_inscrever"
                                          action="<?php echo route('removerinscricaocriadorevento'); ?>"
                                          method="post">
                                        <input type="hidden" name="eventoid" value="{{$evento->id}}">
                                        <input type="hidden" name="inscricaoid" value="{{$inscricao->id}}">
                                        <input type="hidden" name="rdp" value="eventohomecriador">
                                        <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                                        <br>
                                        <input type="submit" class="btn btn-danger" value="Remover">
                                        <a href="javascript:parent.jQuery.fancybox.close();">
                                            <button type="button" class="btn btn-default">{{trans('clubes.cancelar')}}</button>
                                        </a>
                                    </form>
                                </div>
                            @endif


                        </div>
                    </div>


                    @if($inscricao!=null)

                        <div class="portlet box grey">
                            <div class="portlet-title">
                                <div class="caption font-blue-dark">
                                    <span class="caption-subject bold uppercase">{{trans('eventos.passaros_inscritos')}} </span>
                                </div>
                                <div class="actions">
                                    <div class="btn-group btn-group-devided" data-toggle="buttons">

                                    </div>
                                </div>
                            </div>
                            <div class="portlet-body">
                                <div class="form-group row">
                                    <label class="col-sm-2 bold"># {{trans('eventos.passaros_inscritos')}}:</label>
                                    <div class="col-sm-3">
                                        {{$num_passaros}}
                                    </div>
                                    <label class="col-sm-2 bold"></label>
                                    <div class="col-sm-3">
                                        @if($evento->editavelcriador)
                                            <a href="{!! route('gerirpassaroscriador',[$inscricao->id]) !!}"
                                               class="btn btn-primary">{{trans('eventos.gerir_passaros')}} </a>
                                        @endif
                                    </div>
                                </div>

                            </div>
                        </div>



                    @endif
                </div>
            </div>
        </div>
    </section>

@endsection


@section('scripts')
    <!-- BEGIN PAGE LEVEL SCRIPTS -->

    <script src="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.min.js')}}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/pages/scripts/ui-sweetalert.min.js')}}" type="text/javascript"></script>

    <script>

        $('#forminsc').submit(function (e) {
            e.preventDefault();
            var $form = $(this);
            var urlpost = $form.attr('action');

            swal({
                    title: "Inscrição",
                    text: "Pretende inscrever-se neste evento?",
                    type: "info",
                    showCancelButton: true,
                    showConfirmButton: true,
                    cancelButtonText: "Cancelar",
                    confirmButtonText:
                        'SIM, inscrever-me',
                    closeOnConfirm:
                        false
                },

                function () {


                    $.ajax({
                        type: "POST",
                        url: urlpost,
                        headers: {'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')},
                        data: $('#forminsc').serialize(),
                        success: function (response) {


                            swal({
                                    title: "Inscrito com sucesso",
                                    text: "Está inscrito no evento.",
                                    type: "success",
                                    showCancelButton: false,
                                    showConfirmButton: true,
                                    cancelButtonText: "Cancelar",
                                    confirmButtonText:
                                        'OK',
                                    closeOnConfirm:
                                        true
                                },

                                function () {

                                    location.reload();

                                });



                        },
                        error: function (response) {

                        },
                        statusCode: {
                            422: function () {
                            }
                        }
                    }).fail(function (response) {
                    });

                });

        });


        $('[data-toggle=confirmation]').on("confirmed.bs.confirmation", function () {
            console.log("ok");
            return true;
        });
        $('[data-toggle=confirmation]').on("canceled.bs.confirmation", function () {
            event.preventDefault();
            return false;
        });


    </script>
    <script src="{{asset('assets/global/plugins/bootstrap-confirmation/bootstrap-confirmation.min.js')}}"
            type="text/javascript"></script>

@endsection