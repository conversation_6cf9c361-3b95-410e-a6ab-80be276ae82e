@extends('layouts.app')


@section('titulo',trans('general.julgamento_evento'))
@section('subtitulo',$evento->nome)
@section('page_css')


@endsection

@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('homecriador'); ?>">{{trans('menus.home')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('julgareventohome',[$evento->id]) !!}">{{trans('general.julgamento')}} {{$evento->nome}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('julgareventoseccao',[$evento->id,$seccaoclasse->seccao]) !!}">{{trans('general.julgamento_seccao')}} {!! $seccaoclasse->seccao !!}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('julgareventogaiolas', ['id_evento' => $evento->id,'rescid' => $passaro->rescid]) !!}">{{trans('general.gaiolas_em')}} {!! $seccaoclasse->seccao !!}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{trans('general.gaiola')}}: {!! $numg !!}
                - {!! $seccaoclasse->seccao." ".$seccaoclasse->classe !!}</span>
        </li>
    </ul>

@endsection

@section('content')
    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{trans('general.gaiola')}}: {!! $numg !!} A B C D
                            - {!! $seccaoclasse->seccao." ".$seccaoclasse->classe !!}
                        </div>

                    </div>
                    <div class="portlet-body">
                        <div class="col-sm-12" style="margin-bottom: 10px;">
                            <a href="{!! route('julgareventogaiolas', ['id_evento' => $evento->id,'rescid' => $passaro->rescid]) !!}"
                               class="col-sm-4 btn btn-primary"
                               role="button">< {{trans('general.regressar_gaiolas')}}</a>


                        </div>
                        <div class="form-group">
                            <div class="table-scrollable">
                                <form id="form_pontuar_passaros" action="{!! route('gravargaiolapontuacaoequipa') !!}"
                                      method="post">
                                    <input type="hidden" name="eventoid" value="{{$evento->id}}">
                                    <input type="hidden" name="passaroida" value="{!! $equipa[0]->id !!}">
                                    <input type="hidden" name="passaroidb" value="{!! $equipa[1]->id !!}">
                                    <input type="hidden" name="passaroidc" value="{!! $equipa[2]->id !!}">
                                    <input type="hidden" name="passaroidd" value="{!! $equipa[3]->id !!}">
                                    <input type="hidden" name="rescid" value="{!! $passaro->rescid !!}">
                                    <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                                    <table class="table table-striped table-hover tabelapontuacao_equipa">
                                        <thead>
                                        <th>{{trans('general.rubrica')}}</th>
                                        <th style="text-align: center;max-width: 40px;">Máx.</th>
                                        <th style="text-align: center;">A</th>
                                        <th style="text-align: center;">B</th>
                                        <th style="text-align: center;">C</th>
                                        <th style="text-align: center;">D</th>

                                        </thead>
                                        <tbody>
                                        @for($i=0;$i<count($rubricas);$i++)
                                            <tr>
                                                <td style="vertical-align: center"
                                                    class="nome_rubrica">{!! $rubricas[$i]->nome !!}</td>
                                                <td style="text-align: center;"
                                                    id="mval-{!! $rubricas[$i]->id !!}">{!! $rubricas[$i]->valor_max !!}</td>

                                                <td style="text-align: center;"><input type="text" size=3
                                                                                       name="val-{!! $rubricas[$i]->id !!}A"
                                                                                       readonly value="0"></td>
                                                <td style="text-align: center;"><input type="text" size=3
                                                                                       name="val-{!! $rubricas[$i]->id !!}B"
                                                                                       readonly value="0"></td>
                                                <td style="text-align: center;"><input type="text" size=3
                                                                                       name="val-{!! $rubricas[$i]->id !!}C"
                                                                                       readonly value="0"></td>
                                                <td style="text-align: center;"><input type="text" size=3
                                                                                       name="val-{!! $rubricas[$i]->id !!}D"
                                                                                       readonly value="0"></td>

                                            </tr>
                                        @endfor
                                        <tr>
                                            <td>
                                                Totais
                                            </td>
                                            <td style="text-align: center;"><input style="text-align: center;border: 0"
                                                                                   class="input-lg" type="text" size=6
                                                                                   name="totalmax" readonly value="100">
                                            </td>
                                            <td style="text-align: center;"><input
                                                        style="text-align: center;font-weight: bold;border: 0"
                                                        class="input-lg" type="text" size=6 name="totalA" readonly
                                                        value="0"></td>
                                            <td style="text-align: center;"><input
                                                        style="text-align: center;font-weight: bold;border: 0"
                                                        class="input-lg" type="text" size=6 name="totalB" readonly
                                                        value="0"></td>
                                            <td style="text-align: center;"><input
                                                        style="text-align: center;font-weight: bold;border: 0"
                                                        class="input-lg" type="text" size=6 name="totalC" readonly
                                                        value="0"></td>
                                            <td style="text-align: center;"><input
                                                        style="text-align: center;font-weight: bold;border: 0"
                                                        class="input-lg" type="text" size=6 name="totalD" readonly
                                                        value="0"></td>

                                        </tr>
                                        <tr>
                                            <td colspan="2"></td>
                                            <td>
                                                <div class="mt-checkbox-inline">
                                                    <input name="zcnj_1" style="margin-right: 5px;"
                                                           data-tipo="NJ" data-pid="{!! $equipa[0]->id !!}"
                                                           data-gaiola="A" type="checkbox" data-toggle="toggle"
                                                           data-on="NJ" data-off="NJ" data-onstyle="warning">
                                                    <input name="zcdq_1" style="margin-right: 5px;"
                                                           data-tipo="DQ" data-pid="{!! $equipa[0]->id !!}"
                                                           data-gaiola="A" type="checkbox" data-toggle="toggle"
                                                           data-on="DQ" data-off="DQ" data-onstyle="warning">
                                                    <input name="zcdc_1" style="margin-right: 5px;"
                                                           data-tipo="DC" data-pid="{!! $equipa[0]->id !!}"
                                                           data-gaiola="A" type="checkbox" data-toggle="toggle"
                                                           data-on="DC" data-off="DC" data-onstyle="warning">

                                                </div>
                                            </td>
                                            <td>
                                                <div class="mt-checkbox-inline">
                                                    <input name="zcnj_2" style="margin-right: 5px;"
                                                           data-tipo="NJ" data-pid="{!! $equipa[1]->id !!}"
                                                           data-gaiola="B" type="checkbox" data-toggle="toggle"
                                                           data-on="NJ" data-off="NJ" data-onstyle="warning">
                                                    <input name="zcdq_2" style="margin-right: 5px;"
                                                           data-tipo="DQ" data-pid="{!! $equipa[1]->id !!}"
                                                           data-gaiola="B" type="checkbox" data-toggle="toggle"
                                                           data-on="DQ" data-off="DQ" data-onstyle="warning">
                                                    <input name="zcdc_2" style="margin-right: 5px;"
                                                           data-tipo="DC" data-pid="{!! $equipa[1]->id !!}"
                                                           data-gaiola="B" type="checkbox" data-toggle="toggle"
                                                           data-on="DC" data-off="DC" data-onstyle="warning">

                                                </div>
                                            </td>
                                            <td>
                                                <div class="mt-checkbox-inline">
                                                    <input name="zcnj_3" style="margin-right: 5px;"
                                                           data-tipo="NJ" data-pid="{!! $equipa[2]->id !!}"
                                                           data-gaiola="C" type="checkbox" data-toggle="toggle"
                                                           data-on="NJ" data-off="NJ" data-onstyle="warning">
                                                    <input name="zcdq_3" style="margin-right: 5px;"
                                                           data-tipo="DQ" data-pid="{!! $equipa[2]->id !!}"
                                                           data-gaiola="C" type="checkbox" data-toggle="toggle"
                                                           data-on="DQ" data-off="DQ" data-onstyle="warning">
                                                    <input name="zcdc_3" style="margin-right: 5px;"
                                                           data-tipo="DC" data-pid="{!! $equipa[2]->id !!}"
                                                           data-gaiola="C" type="checkbox" data-toggle="toggle"
                                                           data-on="DC" data-off="DC" data-onstyle="warning">

                                                </div>
                                            </td>
                                            <td>
                                                <div class="mt-checkbox-inline">
                                                    <input name="zcnj_4" style="margin-right: 5px;"
                                                           data-tipo="NJ" data-pid="{!! $equipa[3]->id !!}"
                                                           data-gaiola="D" type="checkbox" data-toggle="toggle"
                                                           data-on="NJ" data-off="NJ" data-onstyle="warning">
                                                    <input name="zcdq_4" style="margin-right: 5px;"
                                                           data-tipo="DQ" data-pid="{!! $equipa[3]->id !!}"
                                                           data-gaiola="D" type="checkbox" data-toggle="toggle"
                                                           data-on="DQ" data-off="DQ" data-onstyle="warning">
                                                    <input name="zcdc_4" style="margin-right: 5px;"
                                                           data-tipo="DC" data-pid="{!! $equipa[3]->id !!}"
                                                           data-gaiola="D" type="checkbox" data-toggle="toggle"
                                                           data-on="DC" data-off="DC" data-onstyle="warning">

                                                </div>
                                            </td>


                                        </tr>
                                        <tr>
                                            <td colspan="2">

                                                <button name="est4" style="margin-right: 20px;"
                                                        class="btn btn-default btn-lg" data-tipo="--" type="button">
                                                    Reset
                                                </button>
                                            </td>
                                            <td colspan="2" style="text-align: right">{{trans('general.harmonia')}}:
                                                <input style="font-size:20px;border: 0" class="input-lg" type="text"
                                                       size=6 name="harmonia" readonly value="0"></td>
                                            <td colspan="2" style="text-align: right;font-weight: bold">TOTAL: <input
                                                        style="font-size:20px;border: 0" class="input-lg" type="text"
                                                        size=6 name="totalequipa" readonly value="0"></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3"><input type="text" size="10" class="form-control"
                                                                   name="observacao_julgamento"
                                                                   placeholder="Observação"></td>
                                            <td colspan="2"><label
                                                        for="estado">{{trans('general.estado')}}</label><input
                                                        type="text" size=6 name="estado" readonly value=""></td>

                                            <td colspan="2" style="text-align: center;"><input type="submit"
                                                                                               class="btn btn-primary btn-lg"
                                                                                               value="{{trans('forms.gravar')}}">
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>

                                </form>
                            </div>

                        </div>

                    </div>

                </div>

            </div>
        </div>

    </div>
    <!-- Main content -->

    <br>
    <br>

@endsection

@section('scripts')
    <script>
        function calculaTotais() {

            var totalf = 0;

            var gs = ['A', 'B', 'C', 'D'];
            var maximo = 0;
            var minimo = 300;
            for (var i = 0; i < gs.length; i++) {
                var total = [];
                total[i] = 0;
                var valores = $(":input[name^='val-'][name$='" + gs[i] + "']").each(function () {

                    total[i] = total[i] + parseInt($(this).val());
                });
                if (total[i] > maximo) {
                    maximo = total[i];
                }
                if (total[i] < minimo) {
                    minimo = total[i];
                }
                //console.log(i + "..." + total[i]);
                $("form input[name='total" + gs[i] + "']").val(total[i]);

                totalf += total[i];

            }

            if($("form input[name='estado']").val()==''){

                var harm = 6 - (maximo - minimo);
                if(harm<0){
                    harm = 0;
                }

                $("form input[name='harmonia']").val(harm);


                totalf = totalf + harm;

                //console.log("max: " + maximo);

                $("form input[name='totalequipa']").val(totalf);



            }




        }

        function limparPontos() {
            $("input[name^='val-']").val(0);


        }

        function limparPontosGaiola(gaiola) {
            $("input[name^='val-'][name $='"+gaiola+"']").val(0);


        }

        function limparTotalHarmonia(){
            $("form input[name='totalequipa']").val(0);
            $("form input[name='harmonia']").val(0);

        }

        function reporValoresReferencia() {
            @for($i=0;$i<count($rubricas);$i++)
            $("form input[name='val-{!! $rubricas[$i]->id !!}A']").val('{!! $rubricas[$i]->valor_ref !!}');
            $("form input[name='val-{!! $rubricas[$i]->id !!}B']").val('{!! $rubricas[$i]->valor_ref !!}');
            $("form input[name='val-{!! $rubricas[$i]->id !!}C']").val('{!! $rubricas[$i]->valor_ref !!}');
            $("form input[name='val-{!! $rubricas[$i]->id !!}D']").val('{!! $rubricas[$i]->valor_ref !!}');
            @endfor




            $("form input[name^='zcnj_']").each(function () {
                $(this).bootstrapToggle('off');
            });
            $("form input[name^='zcdq_']").each(function () {
                $(this).bootstrapToggle('off');
            });
            $("form input[name^='zcdc_']").each(function () {
                $(this).bootstrapToggle('off');
            });

            //console.log("repor");


            //$("form input[name^='est1']").bootstrapToggle('off');
            // $("form input[name^='est2']").bootstrapToggle('off');
            //$("form input[name^='est3']").bootstrapToggle('off');
            $("form input[name='estado']").val("");
            $("form input[name='observacao_julgamento']").val("");

            calculaTotais();

        }

        @if($pontuacoes!=0)
        function reporValoresGravados() {

            @for($i=0;$i<count($rubricas);$i++)

            $("form input[name='val-{!! $rubricas[$i]->id !!}A']").val('{!! $pontuacoes[$numg."A"][$i]->pontos !!}');
            $("form input[name='val-{!! $rubricas[$i]->id !!}B']").val('{!! $pontuacoes[$numg."B"][$i]->pontos !!}');
            $("form input[name='val-{!! $rubricas[$i]->id !!}C']").val('{!! $pontuacoes[$numg."C"][$i]->pontos !!}');
            $("form input[name='val-{!! $rubricas[$i]->id !!}D']").val('{!! $pontuacoes[$numg."D"][$i]->pontos !!}');


            @endfor

            $("form input[name='estado']").val("{!! $passaro->estado !!}");
            $("form input[name='observacao_julgamento']").val("{!! $passaro->observacao_julgamento !!}");


            // activacao dos toggles de cada passaro
            @for($i=0;$i<4;$i++)

            @if($equipa[$i]->nj==1)
            $("form input[name^='zcnj_{!! $i+1 !!}']").bootstrapToggle('on');
            @else
            $("form input[name^='zcnj_{!! $i+1 !!}']").bootstrapToggle('off');
            @endif
            @if($equipa[$i]->dq==1)
            $("form input[name^='zcdq_{!! $i+1 !!}']").bootstrapToggle('on');
            @else
            $("form input[name^='zcdq_{!! $i+1 !!}']").bootstrapToggle('off');
            @endif
            @if($equipa[$i]->fc==1)
            $("form input[name^='zcdc_{!! $i+1 !!}']").bootstrapToggle('on');
            @else
            $("form input[name^='zcdc_{!! $i+1 !!}']").bootstrapToggle('off');
            @endif

            @endfor
            // fim activacao dos toggles de cada passaro



        }

        @endif

        $(document).ready(function () {

            @if($pontuacoes==0)
            reporValoresReferencia();
            @else

            reporValoresGravados();
            @endif



            $("form button[name^='est4']").click(function () {
                var result = confirm("Tem a certeza que pretende reverter todas as alterações para os valores de referência?");
                if (result) {
                    reporValoresReferencia();

                }


            });

            $("form input[name^='zc']").change(function () {

                var gaiola = $(this).data("gaiola");
                var tipo = $(this).data("tipo");
                var pid = $(this).data("pid");

                if (tipo == 'NJ' || tipo == 'DQ') {
                    if (this.checked) {

                        $("form input[name='estado']").val(tipo);
                        limparPontosGaiola(gaiola);
                        limparTotalHarmonia();

                    } else {
                        $("form input[name='estado']").val("");

                    }
                    return;

                }
                if (tipo == 'DC') {
                    if (this.checked) {
                        $("form input[name='estado']").val(tipo);
                        limparTotalHarmonia();

                    }

                }

                //console.log("gaiola: " + gaiola + " - tipo: " + tipo + " - " + pid);

            });


            $("form input[name^='est']").change(function () {


                var tipo = $(this).data("tipo");


                if (tipo == '--') {


                }
                if (tipo == 'NJ') {
                    if (this.checked) {
                        $("form input[name='estado']").val("NJ");
                    } else {
                        $("form input[name='estado']").val("");

                    }


                }
                if (tipo == 'DQ') {
                    if (this.checked) {
                        limparPontos();
                        $("form input[name='estado']").val("DQ");
                    } else {
                        $("form input[name='estado']").val("");

                    }
                }
                if (tipo == 'FC') {
                    if (this.checked) {
                        $("form input[name='estado']").val("DC");
                    } else {
                        $("form input[name='estado']").val("");

                    }
                }


                calculaTotais();


            });
            var spinner = $("input[name^='val-']").spinner({
                icons: {down: "ui-icon-caret-1-s", up: "ui-icon-caret-1-n"},
                _buttonHtml: 'horizontal',
                stop: function (event, ui) {

                    calculaTotais();


                }
            });
           
            $("input[name^='val-']").spinner("option", "min", 0);
            // atribuição de maximos
            @for($i=0;$i<count($rubricas);$i++)

            $("input[name='val-{!! $rubricas[$i]->id !!}A']").spinner("option", "max", {!! $rubricas[$i]->valor_max !!} );
            $("input[name='val-{!! $rubricas[$i]->id !!}B']").spinner("option", "max", {!! $rubricas[$i]->valor_max !!} );
            $("input[name='val-{!! $rubricas[$i]->id !!}C']").spinner("option", "max", {!! $rubricas[$i]->valor_max !!} );
            $("input[name='val-{!! $rubricas[$i]->id !!}D']").spinner("option", "max", {!! $rubricas[$i]->valor_max !!} );


            @endfor

            calculaTotais();

        });


    </script>

@endsection