@extends('layouts.app')

@section('titulo','Gestão Fichas Julgamento')
@section('subtitulo','')

@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> {{trans ('clubes.inicio')}}</a></li>
        <li><a href="<?php echo route('eventohome', [$evento->id]); ?>"> {{trans ('eventos.evento')}}</a></li>
        <li class="active">{{trans ('forms.gestao_fichas_julgamento')}}</li>
    </ol>
@endsection
@section('content')
    <div class="content body">
        <div class="row">
            <div class="col-sm-10">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{$evento->nome}}</h3>
                    </div>
                    <div class="box-body">

                        <div class="box box-default box-solid">
                            <div class="box-header with-border">
                                <h3 class="box-title">{{trans ('general.importar_tabela_fichas_julgamento')}}</h3>

                            </div>

                            <div class="box-body">
                                <form action="{{ URL::to('importfichasjulgamentoclasses') }}"
                                      class="form-horizontal" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="evento_id" value="{{ $evento->id }}">
                                    <div class="form-group col-xm-12 col-md-2">

                                        <input type="file" name="import_file"/><br>
                                        <button class="btn btn-primary">{{trans ('general.importar_fichas_julgamento')}}</button>
                                    </div>


                                </form>
                                <br>
                                @if(Session::has('flash_message_success'))
                                    <div class="alert alert-success col-sm-12">

                                        {{Session::get('flash_message_success')}}
                                        <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;</button>
                                    </div>
                                @endif
                                @if(Session::has('flash_message'))
                                    <div class="alert alert-danger col-sm-12">

                                        {{Session::get('flash_message')}}
                                        <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;</button>
                                    </div>
                                @endif
                            </div>

                        </div>


                        <br>
                        <div class="box box-default box-solid">
                            <div class="box-header with-border">
                                <h3 class="box-title">{{trans ('general.exportar_tabela')}}</h3>

                            </div>

                            <div class="box-body">
                                <a href="">
                                    <button class="btn btn-default">{{trans ('general.download_julgamento_seccoes_classes')}}  (<i style="color: #207245" class="fa fa-table"></i>))</button>
                                </a>

                                {{--<a href="{{ URL::to('downloadExcel/xls') }}">
                                    <button class="btn btn-success">{{trans ('general.download_excel')}}</button>
                                </a>

                                <a href="{{ URL::to('downloadExcel/csv') }}">
                                    <button class="btn btn-success">{{trans ('general.download_csv')}}</button>
                                </a>--}}
                            </div>

                        </div>


                    </div>
                </div>

            </div>
        </div>
    </div>

@endsection

@section('scripts')
    <script>

        $(document).ready(function () {


        });

    </script>

@endsection
