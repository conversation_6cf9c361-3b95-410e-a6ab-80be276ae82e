<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3>{{ trans('forms.editar_evento') }}</h3>
</div>
<div class="modal-body">
    <div class="row">

        <div class="form-group col-md-12" id="erros">
        </div>
        <div class="col-sm-12">
            {!!  Form::model($evento, ['id'=>'formevtedit', 'method'=>'PATCH', 'route'=>['admineventoupdate',$evento->id]]) !!}

            @include('eventos._formgeral', ['tipo'=>'edit'])

            <div class="form-group col-md-12 pull-right">
                <button type="button" data-dismiss="modal" class="btn">{{ trans('forms.cancelar') }}</button>
                <button type="submit" class="btn btn-primary">{{ trans('forms.gravar') }}</button>
                <br>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</div>
<script>
    let locaisrecolha = [
            @if( isset($locaisrecolha))
            @foreach($locaisrecolha as $lr)
        {id: {{ $lr->id }}, pais_id: {{ $lr->pais_id }}, pais: '{{ $lr->paisnome }}', local_nome: '{{ $lr->local_nome }}', local_morada: '{{ $lr->local_morada}}', deleted: 0},
        @endforeach
        @endif];
    var clubesassociados = [
        @if( isset($clubesorganizadores))
        @foreach($clubesorganizadores as $c){{ $c->clube_id }},
        @endforeach
        @endif];
    var paisessobretaxas = [
            @if( isset($sobretaxaspaises))
            @foreach($sobretaxaspaises as $st)
        {
            psid: {{ $st->pais_id }}, sobretaxa: {{ $st->taxa }} },
        @endforeach
        @endif];
    var evtid = {{$evento->id}};

    function removeLocalRecolha(id) {


        var url = '<?php echo url('ajax_check_local_recolha') ?>/' + evtid + '/' + id;
        $.ajax({
            type: "GET",
            url: url,
            success: function (e) {

                if(e>0){

                    swal("Erro", "Existem criadores que têm esse local marcado como local de recolha. Impossível apagar.", "error");
                }else{

                    var idxlocal = -1;
                    for (var i=0; i < locaisrecolha.length; i++) {
                        if (locaisrecolha[i].id === Number(id)) {
                            idxlocal = i;
                        }
                    }
                    if(idxlocal>=0){
                        $("#lr" + id).remove();
                        //locaisrecolha.splice(idxlocal, 1);
                        locaisrecolha[idxlocal].deleted=1;
                    }

                }

            }
        });

    }

    function removeClube(id) {
        var idxclube = -1;
        for (var i=0; i < clubesassociados.length; i++) {
            if (clubesassociados[i] === Number(id)) {
                idxclube = i;
            }
        }
        if(idxclube>=0){
            $("#c" + id).remove();
            clubesassociados.splice(idxclube, 1);
        }


    }

    function removePaisSobretaxa(pais_id) {

        $("#ps" + pais_id).remove();
        var indice = findPaisSobretaxa(pais_id);
        //console.log("encontrei "+pais_id+" no indice "+indice);
        paisessobretaxas.splice(indice, 1);

    }

    function findPaisSobretaxa(pais_id) {
        var index = -1;
        for (var i = 0; i < paisessobretaxas.length; i++) {
            //console.log(i + " = " + paisessobretaxas[i]["psid"]);
            if (paisessobretaxas[i]["psid"] == pais_id) {
                index = i;
                break;
            }

        }
        return index;
    }

    $(document).ready(function () {

        var lr_count = -1;

        $("#adicionarlocalrecolhabt").click(function (evt) {

            var lr_paisid = $("#lrpaiseslista").val();
            var lr_nome = $("#local_nome").val();
            var lr_morada = $("#local_morada").val();
            var lr_paisnome =  $("#lrpaiseslista option:selected").text();

            $.each(locaisrecolha, function (i, v) {
                if (v.id >= lr_count) {
                    lr_count = v.id+1;
                }
            });
            //console.log(lr_count);
            if (lr_nome === '') {

                bootbox.alert({
                    title: "Hmm..",
                    message: "Não pode adicionar locais vazios.",
                    backdrop: true
                });

            } else {
                let temp = {id: lr_count, pais_id: lr_paisid, local_nome: lr_nome, local_morada: lr_morada, deleted: 0};
                locaisrecolha.push(temp);
                //console.log(paisessobretaxas);
                $("#locaisrecolha").append("<span class=\"col-md-6\" id=\"lr" + lr_count + "\"><span class=\"col-md-9\">" + lr_nome + "("+lr_paisnome+")</span><span class=\"col-md-3\"><a onclick=\"removeLocalRecolha('" + lr_count + "')\" href=\"#\" id=\"btlr" + lr_count + "\" class=\"btn\"><i class=\"fa fa-trash font-red\"></i></a></span></span>");
                lr_count++;
                $("#local_nome").val('');
                $("#local_morada").val('');

            }
        });

        $("#adicionarsobretaxapaisbt").click(function (evt) {
            var psid = $("#paiseslista").val();
            var psvalorsobretaxa = $("#valor_sobretaxa_pais").val();
            var psnome = $("#paiseslista option:selected").text();
            if (paisessobretaxas.length <= 999) {
                if (findPaisSobretaxa(psid) != -1) {

                    bootbox.alert({
                        title: "Hmm..",
                        message: "O país que selecionou já tem sobretaxa no evento.",
                        backdrop: true
                    });

                } else {
                    if (psvalorsobretaxa <= 0) {
                        bootbox.alert({
                            title: "Hmm..",
                            message: "O valor da sobretaxa tem de ser superior a 0 (zero).",
                            backdrop: true
                        });
                    } else {
                        let temp = {psid: psid, sobretaxa: psvalorsobretaxa};

                        paisessobretaxas.push(temp);
                        $("#paisesescolhas").append("<span class=\"col-md-6\" id=\"ps" + psid + "\"><span class=\"col-md-6\" style=\"height: 34px;padding-top: 5px\">" + psnome + "</span><span class=\"col-md-6\">" + psvalorsobretaxa + "<a onclick=\"removePaisSobretaxa('" + psid + "')\" href=\"#\" id=\"btps" + psid + "\" class=\"btn\"><i class=\"fa fa-trash font-red\"></i></a></span></span>");
                    }

                }

            } else {

                bootbox.alert({
                    title: "Hmm..",
                    message: "O máximo de sobretaxas que pode adicionar são 999.",
                    backdrop: true
                });

            }

            $("#paiseslista")[0].selectedIndex = 0;
        });

        $("#associarclubebt").click(function (evt) {
            var id = $("#clubeslista").val();
            var nome = $("#clubeslista option:selected").text();
            if (clubesassociados.length <= 6) {
                if ($.inArray(id, clubesassociados) != -1) {

                    bootbox.alert({
                        title: "Hmm..",
                        message: "O clube que tentou associar já existe no evento.",
                        backdrop: true
                    });


                } else {


                    clubesassociados.push(id);
                    $("#clubesescolhas").append("<span class=\"col-md-12\" id=\"c" + id + "\"><span class=\"col-md-9\">" + nome + "</span><span class=\"col-md-3\"><a onclick=\"removeClube('" + id + "')\" href=\"#\" id=\"bt" + id + "\" class=\"btn\"><i class=\"fa fa-trash font-red\"></i></a></span></span>");
                    //console.log(clubesassociados);


                }

            } else {

                bootbox.alert({
                    title: "Hmm..",
                    message: "O máximo de clubes que pode associar a um evento são 7.",
                    backdrop: true
                });

            }


            //var evtform = $("#evtformconteudo");

            //evtform.css('height',) = 0;


        });


        $('#data_inicio').daterangepicker({
            singleDatePicker: true,
            "locale": {
                "format": "YYYY/MM/DD",
                "separator": " - ",
                "applyLabel": "Apply",
                "cancelLabel": "Cancel",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "right",
        }, function (start, end, label) {
            //$("input[name='data_inicio']").val(start.format('YYYY-MM-DD'));
            //$("input[name='data_fim']").val(start.format('YYYY-MM-DD'));
//console.log('New date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD') + ' (predefined range: ' + label + ')');
        });
        $('#data_fim').daterangepicker({
            singleDatePicker: true,
            "locale": {
                "format": "YYYY/MM/DD",
                "separator": " - ",
                "applyLabel": "Apply",
                "cancelLabel": "Cancel",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "right",
        }, function (start, end, label) {
            //$("input[name='data_inicio']").val(start.format('YYYY-MM-DD'));
            //$("input[name='data_fim']").val(start.format('YYYY-MM-DD'));
//console.log('New date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD') + ' (predefined range: ' + label + ')');
        });
        $('#data_inicio_inscricoes').daterangepicker({
            singleDatePicker: true,
            "locale": {
                "format": "YYYY/MM/DD",
                "separator": " - ",
                "applyLabel": "Apply",
                "cancelLabel": "Cancel",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "center",
        }, function (start, end, label) {
            //$("input[name='data_inicio_inscricoes']").val(start.format('YYYY-MM-DD'));
            //$("input[name='data_fim_inscricoes']").val(start.format('YYYY-MM-DD'));


            //console.log('New date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD') + ' (predefined range: ' + label + ')');
        });
        $('#data_fim_inscricoes').daterangepicker({
            singleDatePicker: true,
            "locale": {
                "format": "YYYY/MM/DD",
                "separator": " - ",
                "applyLabel": "Apply",
                "cancelLabel": "Cancel",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "center",
        }, function (start, end, label) {
            //$("input[name='data_inicio_inscricoes']").val(start.format('YYYY-MM-DD'));
            //$("input[name='data_fim_inscricoes']").val(start.format('YYYY-MM-DD'));


            //console.log('New date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD') + ' (predefined range: ' + label + ')');
        });
        $('#data_inicio_julg').daterangepicker({
            singleDatePicker: true,
            "timePicker": true,
            "timePicker24Hour": true,
            "timePickerIncrement": 15,
            "dateLimit": {
                "days": 7
            },
            "locale": {
                "format": "YYYY/MM/DD HH:mm",
                "separator": " - ",
                "applyLabel": "Gravar",
                "cancelLabel": "Cancelar",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "left",
        }, function (start, end, label) {
            //$("input[name='data_inicio_julg']").val(start.format('YYYY-MM-DD hh:mm'));
            //$("input[name='data_fim_julg']").val(start.format('YYYY-MM-DD hh:mm'));


            //console.log('New date range selected: ' + start.format('YYYY-MM-DD hh:mm') + ' to ' + end.format('YYYY-MM-DD hh:mm') + ' (predefined range: ' + label + ')');
        });
        $('#data_fim_julg').daterangepicker({
            singleDatePicker: true,
            "timePicker": true,
            "timePicker24Hour": true,
            "timePickerIncrement": 15,
            "dateLimit": {
                "days": 7
            },
            "locale": {
                "format": "YYYY/MM/DD HH:mm",
                "separator": " - ",
                "applyLabel": "Gravar",
                "cancelLabel": "Cancelar",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "left",
        }, function (start, end, label) {
            //$("input[name='data_inicio_julg']").val(start.format('YYYY-MM-DD hh:mm'));
            //$("input[name='data_fim_julg']").val(start.format('YYYY-MM-DD hh:mm'));


            //console.log('New date range selected: ' + start.format('YYYY-MM-DD hh:mm') + ' to ' + end.format('YYYY-MM-DD hh:mm') + ' (predefined range: ' + label + ')');
        });


    });


    $('#formevtedit').submit(function (e) {
        e.preventDefault();
        //console.log(clubesassociados);
        var numclubesassociados = 0;
        $.each(clubesassociados, function (i, v) {
            var input = $("<input>").attr({"type": "hidden", "name": "clubeslista[]"}).val(v);
            $('#formevtedit').append(input);
            //console.log(i + " - " + v);
            numclubesassociados++;
        });
        if (numclubesassociados == 0) {
            //console.log("erros");
            $('#erros').empty();
            $('#erros').append("<div class=\"note note-danger\" id=\"errosi\"></div>");
            $('#errosi').append("<p>{{ trans('forms.associar_pelo_menos_um_clube') }}</p>");
            return false;
        }
        $.each(paisessobretaxas, function (i, v) {
            //console.log("a adicionar: " + v.psid + " com " + v.sobretaxa)

            var inputpaises = $("<input>").attr({"type": "hidden", "name": "stpaiseslista[]"}).val(v.psid);
            var inputtaxas = $("<input>").attr({"type": "hidden", "name": "sttaxaslista[]"}).val(v.sobretaxa);
            $('#formevtedit').append(inputpaises);
            $('#formevtedit').append(inputtaxas);
        });
        $.each(locaisrecolha, function (i, v) {
            //console.log("a adicionar: " + v.pais_id + " com " + v.local_nome);
            var inputlocaisnome = $("<input>").attr({"type": "hidden", "name": "locaisnomelista[]"}).val(v.local_nome);
            var inputlocaismorada = $("<input>").attr({"type": "hidden", "name": "locaismoradalista[]"}).val(v.local_morada);
            var inputlocaispaises = $("<input>").attr({"type": "hidden", "name": "locaispaiseslista[]"}).val(v.pais_id);
            var inputlocaisdeleted = $("<input>").attr({"type": "hidden", "name": "locaisdeletedlista[]"}).val(v.deleted);
            var inputlocaisids = $("<input>").attr({"type": "hidden", "name": "locaisidslista[]"}).val(v.id);

            $('#formevtedit').append(inputlocaisnome);
            $('#formevtedit').append(inputlocaismorada);
            $('#formevtedit').append(inputlocaispaises);
            $('#formevtedit').append(inputlocaisdeleted);
            $('#formevtedit').append(inputlocaisids);
        });


        //throw new Error("Something went badly wrong!");
        var $form = $(this);
        var urlpost = $form.attr('action');

        $.ajax({
            type: "POST",
            url: urlpost,
            data: $(this).serialize(),
            success: function (response) {
                //console.log("entrou no fim do criar");

                $('#ajaxeditevt').modal('toggle');
                swal({
                        title: "Evento Editado",
                        text: "O evento foi editado com sucesso",
                        type: "success",
                        confirmButtonText: 'OK',
                    },
                    function () {
                        window.open('{{URL::route('eventohome',['id_evento'=>$evento->id])}}', '_self');
                    }
                );


            },
            error: function (response) {
                if (response.status == 422) {
                    $('#erros').empty();
                    $('#erros').append("<div class=\"note note-danger\" id=\"errosi\"></div>");
                    $.each(response.responseJSON, function (key, value) {
                        $('#errosi').append("<h6><p>" + value + "</p></h6>");
                        //console.log(key + " - " + value);
                    });
                    //console.log(response.responseJSON);
                }
            },
            statusCode: {
                422: function () {
                }
            }
        }).fail(function (response) {
        });
    });
</script>
