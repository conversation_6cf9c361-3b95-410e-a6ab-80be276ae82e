<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3>{{ trans('forms.alterar_classe_passaro') }}</h3>
</div>
<div class="modal-body">
    <div class="row">

        <div class="form-group col-md-12" id="erros">
        </div>

        <form id="form_alterar_classe_passaro" action="{!! route('gravaralteracaoclassepassaro') !!}" method="post">
            <input type="hidden" name="eventoid" value="{{$evento->id}}">
            <input type="hidden" name="passaroid" value="{{$passaro->id}}">
            <input type="hidden" name="rdt" value="ajax">
            <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
            <div class="form-group">
                <label for="listaseccoespa">{{trans('general.seccao_classe')}}</label>
                <select name="seccaoclassepaid" id="listaseccoespa" style="width: 100%"
                        class="listaseccoespa col-sm-12 col-md-12 col-lg-12"
                        data-live-search="true">
                    @foreach($classes as $ci)
                        <option value="{{ $ci->id }}">{{$ci->seccao}}.{{$ci->classe}}
                            - {{$ci->nome}}</option>
                    @endforeach
                </select>

            </div>
            <div class="form-group">
                <input type="submit" class="btn btn-success" value="Alterar Classe">

                <button type="button" class="btn default" data-dismiss="modal">{{trans('clubes.cancelar')}}</button>

            </div>
        </form>

    </div>
</div>
<script>

    function selectClass(rescid) {
        //console.log('select: '+rescid);
        $('.listaseccoespa').selectpicker('val', rescid);
    }


    $(document).ready(function () {
        $('form#form_alterar_classe_passaro').submit(function (e) {
            e.preventDefault();
            var $form = $(this);
            var urlpost = $form.attr('action');
            $.ajax({
                type: "POST",
                url: urlpost,
                data: $(this).serialize(),
                success: function (response) {
                    //console.log("entrou no fim do criar");
                    $('#edit_classe_passaro').modal('toggle');
                    grid_passaros.reload();
                    swal({
                            title: "Ok",
                            text: "Pássaro editado com sucesso",
                            type: "success",
                            confirmButtonText: 'OK',
                        },
                        function () {

                        }
                    );


                },
                error: function (response) {
                    console.log('error');
                },
                statusCode: {
                    422: function () {
                    }
                }
            }).fail(function (response) {
            });

            return false;

        });

    });

</script>
