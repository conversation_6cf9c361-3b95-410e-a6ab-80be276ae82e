<div class="modal fade" id="editpassaro" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="vertical-alignment-helper">
        <div class="modal-dialog modal-lg vertical-align-center" style="width: 80%;">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span>

                    </button>
                    <h4 class="modal-title" id="myModalLabel">{{trans ('eventos.edicao_passaros')}}</h4>

                </div>
                <div class="modal-body">
                    <form id="form_editar_passaro" action="<?php echo route('editarinscricaopassaroevento'); ?>" method="post">
                        <input type="hidden" name="epassaroid" value="0">
                        <input type="hidden" name="enumgaiola" value="0">
                        <input type="hidden" name="eeventoid" value="{{$evento->id}}">
                        <input type="hidden" name="einscricaoid" value="0">
                        <input type="hidden" name="rdt" value="{{Route::getCurrentRoute()->getName()}}">
                        <input type="hidden" name="eseccaoclasseidold" id="eselclasseold">
                        <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                        <table class="table table-condensed">
                            <thead>
                            <tr>
                                <th>{{trans ('general.seccao_classe')}}</th>
                                <th>{{trans ('general.preco')}}</th>
                                <th>{{trans ('general.anilha')}}</th>
                                <th>{{trans ('general.ano')}}</th>
                                <th>{{trans ('general.vendido')}}</th>
                                <th>{{trans ('general.ausente')}}</th>
                                <th>{{trans ('general.gaiola_propria')}}</th>
                                <th>{{trans ('general.obs')}}</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td><select name="eseccaoclasseid" id="eselclasse" class="selectpicker" data-live-search="true">
                                        @foreach($classes as $ci)
                                            <option value="{{ $ci->id }}">{{$ci->seccao}} {{$ci->classe}} - {{$ci->nome}}</option>
                                        @endforeach
                                    </select></td>
                                <td><input type="text" size="10" class="form-control" name="epreco" placeholder="Preço"></td>
                                <td><input type="text" size="10" class="form-control" name="eanilha" placeholder="Anilha"></td>
                                <td><input type="text" size="10" class="form-control" name="eano" placeholder="Ano"></td>
                                <td><select name="evendido" id="evendido">
                                        <option value="0">Não</option>
                                        <option value="1">Sim</option>
                                    </select></td>
                                <td><select name="eausente" id="eausente">

                                        <option value="0">Não</option>
                                        <option value="1">Sim</option>
                                    </select></td>
                                <td><select name="gpropria" id="gpropria">

                                        <option value="0">Não</option>
                                        <option value="1">Sim</option>
                                    </select></td>
                                <td><input type="text" size="10" class="form-control" name="eobservacao" placeholder="Observação"></td>
                                <td><input type="submit" class="btn btn-info" value="Gravar"></td>

                            </tr>
                            </tbody>
                        </table>
                    </form>




                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Cancelar</button>


                </div>
            </div>
        </div>
    </div>
</div>