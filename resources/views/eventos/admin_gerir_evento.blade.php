@extends('layouts.app')

@section('page_css')
    <link href="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.css')}}" rel="stylesheet"
          type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css') }}" rel="stylesheet"
          type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css')}}"
          rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css')}}"
          rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css')}}"
          rel="stylesheet" type="text/css"/>
    {{--<link href="{{ asset('assets/global/plugins/select2/css/select2.min.css')}}"--}}
    {{--rel="stylesheet" type="text/css"/>--}}
    {{--<link href="{{ asset('assets/global/plugins/select2/css/select2-bootstrap.min.css')}}"--}}
    {{--rel="stylesheet" type="text/css"/>--}}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" rel="stylesheet"/>

@endsection
@section('titulo','Gestão de Evento - Admin')
@section('subtitulo',$evento->nome)

@section('breadcrumbs')
    @role('admin')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casaadmin'); ?>">{{ trans('menus.homeadmin') }}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="<?php echo route('eventosadminlist'); ?>">{{ trans('general.gestao_eventos') }}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{$evento->nome}}</span>
        </li>
    </ul>
    @else
        <ul class="page-breadcrumb">
            <li>
                <a href="<?php echo route('casa'); ?>">{{trans('clubes.inicio')}}</a>
                <i class="fa fa-angle-right"></i>
            </li>
            <li>
                <span>{{trans('eventos.evento')}}</span>
            </li>
        </ul>


        @endrole


        @endsection

        @section('content')

            <div class="content body">
                <div class="row">
                    <div class="col-sm-12">

                        <div class="portlet box blue">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="fa fa-gift"></i>{{$evento->nome}}
                                </div>

                            </div>
                            <div class="portlet-body">
                                <div class="tabbable-custom">
                                    <ul class="nav nav-tabs ">
                                        <li class="active">
                                            <a href="#tabgeral" data-toggle="tab"
                                               aria-expanded="false">{{ trans('general.geral') }} </a>
                                        </li>
                                        <li class="">
                                            <a onclick="refreshInscricoes()" href="#tabinscricoes" data-toggle="tab"
                                               aria-expanded="true">{{ trans('general.inscricoes') }}<span
                                                        class="badge badge-success"> {{$num_inscricoes}} </span></a>
                                        </li>
                                        <li class="">
                                            <a href="#tablistagens" data-toggle="tab"
                                               aria-expanded="false">{{ trans('general.listagens') }}</a>
                                        </li>
                                        @if(Auth::check() && Auth::user()->isRole('admin'))
                                            <li class="">
                                                <a onclick="refreshJuizesSistema()" href="#tabjuizes" data-toggle="tab"
                                                   aria-expanded="false"> {{ trans('general.juizes') }} </a>
                                            </li>
                                            <li class="">
                                                <a href="#tabfichasjulgamento" data-toggle="tab"
                                                   aria-expanded="false"> {{ trans('general.fichas_julgamento') }} <span
                                                            class="badge badge-info"> {{$num_fichas_julgamento}} </span></a>
                                            </li>
                                            <li class="">
                                                <a onclick="refreshSeccoes()" href="#tabseccoes" data-toggle="tab"
                                                   aria-expanded="false"> {{ trans('general.seccoes_classes') }} <span
                                                            class="badge badge-info"> {{$num_seccoes_classes}} </span></a>
                                            </li>
                                            <li class="">
                                                <a href="#tabgaiolas" data-toggle="tab"
                                                   aria-expanded="false"> {{ trans('general.gaiolas') }} <span
                                                            class="badge badge-info"></span></a>
                                            </li>
                                        @endif
                                    </ul>
                                    <div class="tab-content">
                                        <div class="tab-pane active" id="tabgeral">
                                            <h3>{{ trans('general.dados_gerais_eventos') }} <a
                                                        class="btn btn-circle btn-icon-only btn-default"
                                                        data-toggle="modal"
                                                        href="{{URL::route('admineventoedit',['id_evento'=>$evento->id])}}"
                                                        data-target="#ajaxeditevt"><i class="fa fa-edit"></i></a>
                                                {{--<a class="pull-right font-red btn btn-circle btn-default"--}}
                                                {{--data-toggle="modal"--}}
                                                {{--href="{{URL::route('admineventoedit',['id_evento'=>$evento->id])}}"--}}
                                                {{--data-target="#"><i--}}
                                                {{--class="fa fa-calendar"></i></i> {{ trans('general.atribuir_gaiolas') }}--}}
                                                {{--</a>--}}
                                            </h3>
                                            <hr>
                                            @include("eventos.evento_view")
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <hr>
                                                    <h4>Imagens</h4>
                                                    <hr>
                                                    <div class="col-md-4">
                                                        <form method="post" id="formPatrocinador"
                                                              action="{{ URL::route('patrocinadorUpload',['id_evento'=>$evento->id]) }}"
                                                              enctype="multipart/form-data">

                                                            <input type="hidden" name="_token"
                                                                   value="{{csrf_token()}}"/>
                                                            <input class="btn btn-default" type="file" name="image"
                                                                   id="image"/>
                                                            <br>
                                                            <input class="btn btn-info" type="submit"
                                                                   value="Enviar Imagem"/>
                                                        </form>
                                                        @if (count($errors) > 0)
                                                            <div class="alert alert-danger">
                                                                <strong>Erro!</strong>A imagem que enviou tem alguns
                                                                problemas:<br><br>
                                                                <ul>
                                                                    @foreach ($errors->all() as $error)
                                                                        <li>{{ $error }}</li>
                                                                    @endforeach
                                                                </ul>
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div class="col-md-8 left">
                                                        @if($evento->img_patrocinador != "")
                                                            <img width="200" style="margin-bottom: 10px" src="{{ asset('/imgs_eventos/' . $evento->img_patrocinador)}}"/>
                                                            <br>
                                                            <form method="post" id="formRemovePatrocinador"
                                                                  action="{{ URL::route('patrocinadorRemove',['id_evento'=>$evento->id]) }}">
                                                                <input type="hidden" name="_token"
                                                                       value="{{csrf_token()}}"/>
                                                                <input class="btn btn-danger" type="Submit"
                                                                       value="Remover imagem patrocinador"/>
                                                            </form>
                                                        @else
                                                            Sem imagem de patrocinador
                                                        @endif
                                                    </div>


                                                </div>

                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <hr>
                                                    <h4>{{ trans('general.clubes_organizadores') }}</h4>
                                                    <hr>
                                                    @foreach($clubesorganizadores as $c)
                                                        <div class="col-md-6">
                                                    <span style="padding: 10px;"><i
                                                                class="fa fa-dot-circle-o"></i> {{$c->nome}}</span>

                                                        </div>

                                                    @endforeach
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <hr>
                                                    <h4>{{ trans('general.sobretaxas_paises') }}</h4>
                                                    <hr>
                                                    @foreach($sobretaxaspaises as $sp)
                                                        <div class="col-md-4">
                                                    <span style="padding: 10px;"><i
                                                                class="fa fa-dot-circle-o"></i> {{$sp->pais}}</span>

                                                        </div>
                                                        <div class="col-md-2">
                                                            <span style="padding: 10px;"> {{$sp->taxa}}</span>

                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <hr>
                                                    <h4>{{ trans('general.locais_recolha') }}</h4>
                                                    <hr>
                                                    @foreach($locaisrecolha as $lr)
                                                        <div class="col-md-6">
                                                    <span style="padding: 10px;"><i
                                                                class="fa fa-dot-circle-o"></i> {{$lr->local_nome}} ({{$lr->paisnome}})</span>

                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>

                                        </div>
                                        <div class="tab-pane" id="tabinscricoes">
                                            <h3>{{ trans('general.inscricoes') }} <a
                                                        class="btn btn-circle btn-icon-only btn-default"
                                                        data-toggle="modal"
                                                        href="{{ url('eventos',[$evento->id,'inscrever'])}}"
                                                        data-target="#ajaxinscreverc"><i class="fa fa-plus"></i></a>
                                            </h3>
                                            <hr>
                                            <div class="form-group col-md-12" id="erros">
                                            </div>
                                            @include("eventos.admin_gerir_inscricoes_inc")
                                        </div>
                                        <div class="tab-pane" id="tablistagens">
                                            <h3>{{ trans('general.listagens') }}</h3>
                                            <hr>
                                            @if((int)$evento->tipo_evento_id === 7)
                                                @include("eventos.listagens_comparativos_inc")
                                            @else
                                                @include("eventos.admin_gerir_listagens_inc")
                                            @endif
                                        </div>
                                        @if(Auth::check() && Auth::user()->isRole('admin'))
                                            <div class="tab-pane" id="tabjuizes">
                                                <h3>{{ trans('general.juizes') }}</h3>
                                                <hr>
                                                @include("eventos.admin_gerir_juizes_inc")

                                            </div>
                                            <div class="tab-pane" id="tabfichasjulgamento">
                                                <h3>{{ trans('general.fichas_julgamento') }}</h3>
                                                <hr>
                                                @include("eventos.admin_gerir_fichasJulgamento_inc")
                                            </div>
                                            <div class="tab-pane" id="tabseccoes">
                                                <h3>{{ trans('general.import_export_seccoes_classes') }}</h3>
                                                <hr>
                                                @include("eventos.admin_gerir_seccoesClasses_inc")
                                            </div>
                                            <div class="tab-pane" id="tabgaiolas">
                                                <h3>{{ trans('general.gaiolas') }}</h3>
                                                <hr>
                                                @include("eventos.admin_gerir_gaiolas_inc")
                                            </div>
                                        @endif
                                    </div>
                                </div>


                            </div>

                        </div>

                    </div>

                </div>

            </div>


            <div class="modal fade bs-modal-ls" id="ajaxeditevt" role="basic" aria-hidden="true">
                <div class="modal-dialog modal-lg" style="width: 1100px;">
                    <div class="modal-content">
                        <div class="modal-body">
                            <img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">
                            <span> &nbsp;&nbsp;{{trans('general.loading')}}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade bs-modal-ls" id="edit_classe_passaro" role="basic" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-body" id="modaleditclasse">
                            <img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">
                            <span> &nbsp;&nbsp;{{trans('general.loading')}}</span>
                        </div>
                    </div>
                </div>
            </div>

        @endsection


        @section('scripts')
            <!-- BEGIN PAGE LEVEL SCRIPTS -->

            <script src="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.min.js')}}"
                    type="text/javascript"></script>
            <script src="{{ asset('assets/pages/scripts/ui-sweetalert.min.js')}}" type="text/javascript"></script>



            <script src="{{ asset('assets/global/plugins/moment.min.js') }}" type="text/javascript"></script>
            <script src="{{ asset('assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.js') }}"
                    type="text/javascript"></script>
            <script src="{{ asset('assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"
                    type="text/javascript"></script>
            <script src="{{ asset('assets/global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js') }}"
                    type="text/javascript"></script>
            <script src="{{asset('assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js')}}"
                    type="text/javascript"></script>
            <script src="{{asset('assets/global/plugins/bootbox/bootbox.min.js')}}"
                    type="text/javascript"></script>
            {{--<script src="{{asset('assets/global/plugins/select2/js/select2.full.min.js')}}"--}}
            {{--type="text/javascript"></script>--}}

            <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>

            <script src="{{asset('assets/global/scripts/app.min.js')}}" type="text/javascript"></script>
            {{--<script src="{{asset('assets/pages/scripts/components-select2.min.js')}}" type="text/javascript"></script>--}}

            <!-- END PAGE LEVEL SCRIPTS -->
            <script>

                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                let locaisrecolha = [{id: 0, text: 'EVT'},
                        @if( isset($locaisrecolha))
                        @foreach($locaisrecolha as $lr)
                    {
                        id: {{ $lr->id }}, text: '{{ $lr->local_nome }}'
                    },
                    @endforeach
                    @endif];

                function showChanged() {

                    this.dados = w2ui['grid_passaros'].getChanges();

                    $.ajax({
                        type: 'POST',
                        url: '<?php echo LaravelLocalization::getLocalizedURL(LaravelLocalization::getCurrentLocale(),
                            url('ajax_gravar_passaros')); ?>',
                        data: event.changes,
                        contentType: 'application/json; charset=utf-8',
                        async: false,
                        success: function (response) {

                            //console.log("regressei com " + response);
                            swal("{{ trans('general.alteracao_gravada') }}", "{{ trans('general.alteracao_gravada_sucesso') }}", "success");
                        },
                        error: function (response) {
                            if (response.status == 422) {
                                $('#erros').empty();
                                $('#erros').append("<div class=\"note note-danger\" id=\"errosi\"></div>");
                                $.each(response.responseJSON, function (key, value) {
                                    $('#errosi').append("<p>" + value + "</p>");
                                    //console.log(key + " - " + value);
                                });
                                //console.log(response.responseJSON);
                            }
                        },
                        statusCode: {
                            422: function () {

                            }
                        }
                    }).fail(function (response) {
                        //console.log("fail");
                    }).done(function (data) {

                        //console.log("done");
                    });
                }

                function refreshSeccoes() {
                    //console.log("ggurl: " + gg.url);
                    if (gg.url == '') {
                        //console.log("entrei na url gg");
                        gg.url = "{{URL::route('ajaxseccoesclassesadmin',['id_evento'=>$evento->id])}}";

                    } else {
                        // console.log("nao entrei na gg url");
                    }


                    gg.reload();


                }

                function refreshInscricoes(url = null, reload = true) {

                    if (grid_inscricoes != null) {
                        if (url == null) {
                            grid_inscricoes.url = "{{URL::route('ajaxusersinscritos',['id_evento'=>$evento->id])}}";
                        } else {
                            grid_inscricoes.url = url;
                        }

                        //grid_inscricoes.load("{{URL::route('ajaxusersinscritos',['id_evento'=>$evento->id])}}");
                        if (reload) {
                            grid_inscricoes.reload();
                        }
                        grid_inscricoes.select(0);
                    }

                }

                function refreshPassaros(inscricao_id) {

                    grid_passaros.url = '<?php echo LaravelLocalization::getLocalizedURL(LaravelLocalization::getCurrentLocale(),
                        url('ajax_passaros')); ?>/' + inscricao_id;

                    grid_passaros.reload();

                }

                function refreshJuizesSistema() {

                    grid_juizes_sistema.load("{{URL::route('getjuizessistema')}}");
                    refreshJuizesAtribuidos();

                }

                function refreshJuizesAtribuidos() {
                    //console.log("refresh atribuidos");
                    grid_juizes_atribuidos.load("{{URL::route('getjuizesatribuidos',['id_evento'=>$evento->id])}}");

                }

                var gg;
                var grid_inscricoes;
                var grid_passaros;
                var grid_juizes_sistema;
                var grid_juizes_atribuidos;
                $(document).ready(function () {

                    $('#ficha_inscricao').click(function () {
                        let et = grid_inscricoes.getSelection();
                        let etp = et[0];

                        if (!etp) {
                            swal("Erro", "Tem de seleccionar um criador inscrito", "warning");

                        } else {
                            let evento_id = {!! $evento->id !!};

                            var win = window.open('<?php echo url('pdf_ficha_inscricao'); ?>/' + evento_id + '/' + etp, '_blank');
                            if (win) {
                                //Browser has allowed it to be opened
                                win.focus();
                            } else {
                                //Browser has blocked it
                                alert('Please allow popups for this website');
                            }
                        }


                    });

                    $('#calcular_gaiolas_m2').click(function () {

                        swal({
                                title: "Atribuição de Numeração de Gaiolas",
                                text: "A atribuição de numeração vai eliminar numeração já existente!",
                                type: "warning",
                                showCancelButton: true,
                                showConfirmButton: true,
                                cancelButtonText: "Cancelar",
                                confirmButtonClass:
                                    'btn-danger',
                                confirmButtonText:
                                    'ATRIBUIR',
                                closeOnConfirm:
                                    true,
                                //closeOnCancel: false
                            },

                            function () {

                                let data = "evtid=" + {!! $evento->id !!};
                                $("#calcular_gaiolas_m2").attr("disabled", "disabled");
                                $("#resultadosgaiolasm2").html("<strong>A atribuir Gaiolas. Pode demorar alguns segundos..</strong>");
                                $.ajax({
                                    type: "POST",
                                    url: "{{URL::route('atribuirnumeracaogaiolasm2')}}",
                                    data: data,
                                    success: function (e) {

                                        $("#resultadosgaiolasm2").html("");

                                        swal("Numeração de Gaiolas atribuída.", "Por favor verifique.", "success");
                                        $("#calcular_gaiolas_m2").attr("disabled", false);
                                    }
                                });
                                return false;

                            });


                    });

                    $('#calcular_gaiolas_m3').click(function () {

                        swal({
                                title: "Atribuição de Numeração de Gaiolas",
                                text: "A atribuição de numeração vai eliminar numeração já existente!",
                                type: "warning",
                                showCancelButton: true,
                                showConfirmButton: true,
                                cancelButtonText: "Cancelar",
                                confirmButtonClass:
                                    'btn-danger',
                                confirmButtonText:
                                    'ATRIBUIR',
                                closeOnConfirm:
                                    true,
                                //closeOnCancel: false
                            },

                            function () {

                                let data = "evtid=" + {!! $evento->id !!};
                                $("#calcular_gaiolas_m3").attr("disabled", "disabled");
                                $("#resultadosgaiolasm3").html("<strong>A atribuir Gaiolas. Pode demorar alguns segundos..</strong>");
                                $.ajax({
                                    type: "POST",
                                    url: "{{URL::route('atribuirnumeracaogaiolasm3')}}",
                                    data: data,
                                    success: function (e) {

                                        $("#resultadosgaiolasm3").html("");

                                        swal("Numeração de Gaiolas atribuída.", "Por favor verifique.", "success");
                                        $("#calcular_gaiolas_m3").attr("disabled", false);
                                    }
                                });
                                return false;

                            });


                    });


                    $('#calcular_gaiolas').click(function () {

                        swal({
                                title: "Atribuição de Numeração de Gaiolas",
                                text: "A atribuição de numeração vai eliminar numeração já existente!",
                                type: "warning",
                                showCancelButton: true,
                                showConfirmButton: true,
                                cancelButtonText: "Cancelar",
                                confirmButtonClass:
                                    'btn-danger',
                                confirmButtonText:
                                    'ATRIBUIR',
                                closeOnConfirm:
                                    true,
                                //closeOnCancel: false
                            },

                            function () {

                                let data = "evtid=" + {!! $evento->id !!};
                                $("#calcular_gaiolas").attr("disabled", true);
                                $("#resultadosgaiolas").html("<strong>A atribuir Gaiolas. Pode demorar alguns segundos..</strong>");
                                $.ajax({
                                    type: "POST",
                                    url: "{{URL::route('atribuirnumeracaogaiolas')}}",
                                    data: data,
                                    success: function (e) {

                                        $("#resultadosgaiolas").html("");

                                        swal("Numeração de Gaiolas atribuída.", "Por favor verifique.", "success");
                                        $("#calcular_gaiolas").attr("disabled", false);
                                    }
                                });
                                return false;

                            });


                    });

                    $('#gerar_qrcodes').click(function () {

                        swal({
                                title: "Gerar QR Codes",
                                text: "Apenas vou gerar os qrcodes ainda em falta...",
                                type: "warning",
                                showCancelButton: true,
                                showConfirmButton: true,
                                cancelButtonText: "Cancelar",
                                confirmButtonClass:
                                    'btn-danger',
                                confirmButtonText:
                                    'GERAR',
                                closeOnConfirm:
                                    true,
                                //closeOnCancel: false
                            },

                            function () {

                                let data = "evtid=" + {!! $evento->id !!};
                                $("#gerar_qrcodes").attr("disabled", true);
                                $("#resultadosqrcodes").html("<strong>A gerar QR Codes. Pode demorar alguns segundos..minutos</strong>");
                                $.ajax({
                                    type: "POST",
                                    url: "{{URL::route('gerarqrcodes')}}",
                                    data: data,
                                    success: function (e) {
                                        $("#resultadosqrcodes").html("");

                                        swal("QR codes gerados", "Faltam " + e, "success");
                                        $("#gerar_qrcodes").attr("disabled", false);
                                    }
                                });
                                return false;

                            });


                    });

                    $('#etiquetas_provisorias').click(function () {
                        let et = grid_inscricoes.getSelection();
                        let etp = et[0];

                        if (!etp) {
                            swal("Erro", "Tem de seleccionar um criador inscrito", "warning");

                        } else {
                            let evento_id = {!! $evento->id !!};

                            var win = window.open('<?php echo url('pdf_etiquetas_provisorias'); ?>/' + evento_id + '/' + etp, '_blank');
                            if (win) {
                                //Browser has allowed it to be opened
                                win.focus();
                            } else {
                                //Browser has blocked it
                                alert('Please allow popups for this website');
                            }
                        }


                    });


                    $('#edit_classe_passaro').on('shown.bs.modal', function (e) {
                        $('.listaseccoespa').selectpicker({
                            size: 10
                        });
                        let pp = grid_passaros.getSelection();
                        //console.log(pp);
                        selectClass(grid_passaros.get(pp)[0]['rescid']);
                        //console.log(grid_passaros.get(pp)[0]);

                    });


                    $('#listaseccoes').selectpicker({
                        size: 10
                    });

                    $('#listaseccoesp').selectpicker({
                        size: 10
                    });

                    $('#selclasse').selectpicker({
                        size: 10
                    });

                    $("#form_atribuir_juiz").submit(function (e) {
                        if ($("#listaseccoes").val() != '' && $("#selclasse").val() != '') {
                            alert("{{ trans('forms.apenas_seccao_classe') }}");
                            return false;
                        }
                        if ($("#listaseccoes").val() == '' && $("#selclasse").val() == '') {
                            alert("{{ trans('forms.erro_seccao_classe') }}");
                            return false;
                        }
                        if (grid_juizes_sistema.getSelection().length == 0) {
                            alert("{{ trans('forms.erro_falta_juiz') }}");
                            return false;
                        }
                        var data = $(this).serialize();
                        var juizid = grid_juizes_sistema.getSelection()[0];
                        data = "juizid=" + juizid + "&" + data;
                        $.ajax({
                            type: "POST",
                            url: "{{URL::route('gravaratribuirjuizesclasses')}}",
                            data: data,
                            success: function (e) {
                                refreshJuizesAtribuidos();
                            }
                        });
                        return false;
                    });

                    $("#form_inscrever_passaro").submit(function (e) {
                        if ($("#listaseccoesp").val() == '') {
                            alert("{{ trans('forms.erro_escolher_seccao_classe') }}");
                            return false;
                        }

                        if (grid_inscricoes.getSelection().length == 0) {
                            alert("{{ trans('forms.erro_falta_criador') }}");
                            return false;
                        }

                        var data = $(this).serialize();
                        var inscricaoid = grid_inscricoes.getSelection()[0];
                        $("#form_inscrever_passaro input[name=anilha]").val("");
                        data = "inscricaoid=" + inscricaoid + "&" + data;
                        $.ajax({
                            type: "POST",
                            url: "{{URL::route('gravarinscricaopassaroevento')}}",
                            data: data,
                            success: function (e) {
                                refreshPassaros(grid_inscricoes.getSelection()[0]);
                            }
                        });
                        return false;
                    });


                    $("#listaseccoes").change(function () {
                        $("#selclasse").selectpicker('val', '');
                    });

                    $("#selclasse").change(function () {
                        $("#listaseccoes").selectpicker('val', '');
                    });

                    w2utils.settings.dataType = 'RESTFULL';
                    //w2utils.locale('https://raw.githubusercontent.com/vitmalina/w2ui/master/src/locale/pt-br.json');
                    $(".date-picker").datepicker();

                    /*
                                $("button[name^='femodal']").click(function () {

                                    $("#femodal").modal('show');
                                });
                    */

                    /*
                    JAVASCRIPT RELACIONADO COM INSCRIÇÕES
                     */

                    grid_inscricoes = $('#grid_inscricoes').w2grid({
                        name: 'grid_inscricoes',
                        header: 'Inscrições',
                        recid: "id",
                        show: {
                            toolbar: true,
                            footer: false,
                            toolbarEdit: false,
                            toolbarSave: true,
                            toolbarDelete: true,
                        },
                        multiSearch: true,
                        multiSelect: false,
                        searches: [
                            {field: 'stam', caption: 'STAM ', type: 'text', operator: 'contains'},
                            {field: 'name', caption: '{{trans("general.nome")}}', type: 'text', operator: 'contains'},
                            {
                                field: 'estado_inscricao',
                                caption: '{{trans("general.estado")}}',
                                type: 'text',
                                operator: 'contains'
                            },
                        ],
                        columns: [
                            {field: 'id', caption: 'ID', size: '50px', hidden: true},
                            {field: 'stam', caption: 'STAM', size: '50px', sortable: true},
                            {field: 'name', caption: '{{trans("general.nome")}}', size: '30%'},
                            {field: 'nump', caption: '# {{trans("general.birds")}}', size: '70px'},
                            {
                                field: 'tipo_caixa', caption: 'C. Fed.', size: '70px', style: 'text-align: center',
                                //  editable: {type: 'checkbox', style: 'text-align: center'}
                                editable: {type: 'text'}
                            },
                            {field: 'estado', caption: '{{trans("general.estado")}}', size: '70px'},
                            {
                                field: 'local_recolha_id',
                                caption: '{{trans("general.local_recolha")}}',
                                size: '100px',
                                sortable: true,
                                resizable: true,
                                editable: {type: 'select', items: locaisrecolha},
                                render: function (record, index, col_index) {
                                    var html = '';
                                    //debugger;
                                    for (var p in locaisrecolha) {

                                        if (locaisrecolha[p].id == this.getCellValue(index, col_index)) html = locaisrecolha[p].text;
                                    }
                                    return html;
                                }
                            },
                            {
                                field: 'num_jantares',
                                caption: '{{trans("general.jantares")}}',
                                size: '60px',
                                sortable: false,
                                editable: {type: 'text'}
                            },
                            {
                                field: 'num_mesas_venda',
                                caption: '{{trans("general.mesas")}}',
                                size: '70px',
                                sortable: false,
                                editable: {type: 'text'}
                            },
                            {
                                field: 'num_stands',
                                caption: 'Stands',
                                size: '50px',
                                sortable: false,
                                editable: {type: 'text'}
                            },
                            {
                                field: 'prioridade_devolucao',
                                caption: 'Prioridade',
                                size: '80px',
                                sortable: false,
                                editable: {type: 'text'}
                            },

                        ],
                        onEdit: function (event) {
                            alert("funcionalidade ainda não implementada.")


                        },
                        onLoad: function (event) {

                            var response = [];
                            var responseText = event.xhr.responseText;
                            response = responseText.match(/{.*(.*)\}/);
                            if (response != null) {
                                var finale = response[0];
                                var arrayFinale = JSON.parse(finale);

                                var totais = arrayFinale.totais;
                                console.log(totais["total_inscritos"]);
                                console.log(totais);

                                $("#tinscritos").text(totais["total_inscritos"]);
                                $("#tinscritosa").text(totais["total_inscritos_a"]);
                                $("#tinscritosp").text(totais["total_inscritos_p"]);
                                $("#tpassaros").text(totais["total_passaros"]);

                                $(totais).each(function (event) {


                                });
                            }


                        },
                        onReload: function (event) {
                            //console.log("reload");
                            refreshInscricoes();

                        },
                        onSelect: function (event) {

                            //console.log(event.recid);
                            refreshPassaros(event.recid);
                        },
                        onClick: function (event) {


                        },
                        onSearch: function (target, data) {
                            // for (var s in data.searchData) {
                            //     data.searchData[s].operator = 'contains';
                            // }
                            //
                            // refreshInscricoes(null, true);
                        },
                        onSave: function (event) {
                            grid_inscricoes.url = "{{URL::route('ajaxusersinscritos',['id_evento'=>$evento->id])}}";

                            swal("Alterações gravadas", "As sua alterações foram gravadas com sucesso", "success");
                            //refreshPassaros(grid_inscricoes.getSelection())
                            //console.log('save'+grid_inscricoes.getSelection());

                        },
                        onDelete: function (event) {
                            event.preventDefault();

                            swal({
                                    title: "Posso remover a inscrição deste criador?",
                                    text: "A eliminação desta inscrição vai remover todos os pássaros já inscritos deste criador",
                                    type: "warning",
                                    showCancelButton: true,
                                    showConfirmButton: true,
                                    cancelButtonText: "Cancelar",
                                    confirmButtonClass:
                                        'btn-danger',
                                    confirmButtonText:
                                        'Sim, apagar.',
                                    closeOnConfirm:
                                        false,
                                    //closeOnCancel: false
                                },

                                function () {

                                    var inscriadorid = grid_inscricoes.getSelection()[0];
                                    var url = '<?php echo url('ajax_remover_inscricao_criador_admin') ?>/' + {!! $evento->id !!} + '/' + inscriadorid;
                                    $.ajax({
                                        type: "GET",
                                        url: url,
                                        success: function (e) {
                                            swal("Apagado", "Apaguei a inscrição que pretendia", "success");
                                            refreshInscricoes();


                                        }
                                    });

                                });


                        },


                    });

                    grid_inscricoes.onSearch = function (target, info) {
                        delete grid_inscricoes.url;
                        grid_inscricoes.load("{{URL::route('ajaxusersinscritos',['id_evento'=>$evento->id])}}");
                        console.log("antes de search");

                    };


                    grid_passaros = $('#grid_passaros').w2grid({
                        name: 'grid_passaros',
                        header: 'Pássaros',
                        recid: "id",
                        show: {
                            toolbar: true,
                            footer: false,
                            toolbarEdit: false,
                            toolbarSave: true,
                            toolbarDelete: true,
                            toolbarSearch: false,
                            toolbarInput: false,
                        },
                        toolbar: {
                            items: [
                                {type: 'break'},
                                {
                                    type: 'button',
                                    id: 'edit_birdclass',
                                    caption: 'Alterar classe equipa/pássaro',
                                    img: 'w2ui-icon-pencil',
                                    disabled: true
                                }
                            ],
                            onClick: function (target, event) {


                                event.onComplete = function () {
                                    if (target === 'edit_birdclass') {
                                        let p = grid_passaros.getSelection();
                                        let idp = p[0];
                                        let evento_id = {!! $evento->id !!};
                                        $('#modaleditclasse').load('<?php echo url('editclassepassaroajax'); ?>/' + evento_id + '/' + idp, function () {
                                            $('#edit_classe_passaro').modal({show: true});
                                        });

                                    }

                                }
                            }
                        },
                        multiSearch: false,
                        multiSelect: false,
                        columns: [
                            {field: 'id', caption: 'ID', size: '50px', hidden: true},
                            {field: 'seccao', caption: '{{trans("general.seccao")}}', size: '40px', sortable: true},
                            {field: 'classe', caption: '{{trans("general.classe")}}', size: '40px', sortable: false},
                            {field: 'nome', caption: '{{trans("general.nome")}}', size: '30%', sortable: true},
                            {
                                field: 'numgaiola',
                                caption: '# {{trans("general.gaiola")}}',
                                size: '60px',
                                sortable: true
                            },
                            {
                                field: 'anilha',
                                caption: '{{trans("general.anilha")}}',
                                size: '50px',
                                editable: {type: 'text'}
                            },
                            {field: 'ano', caption: '{{trans("general.ano")}}', size: '50px', editable: {type: 'text'}},
                            {
                                field: 'preco',
                                caption: '{{trans("general.preco")}}',
                                size: '60px',
                                sortable: false,
                                editable: {type: 'text'}
                            },
                            {
                                field: 'vendido',
                                caption: '{{trans("general.vendido")}}',
                                size: '70px',
                                sortable: false,
                                editable: {type: 'checkbox', style: 'text-align: center'}
                            },
                            {
                                field: 'gaiola_propria',
                                caption: '{{trans("general.gaiola_propria")}}',
                                size: '50px',
                                sortable: false,
                                editable: {type: 'checkbox', style: 'text-align: center'}
                            },
                            {
                                field: 'ausente',
                                caption: '{{trans("general.ausente")}}',
                                size: '80px',
                                sortable: false,
                                editable: {type: 'checkbox', style: 'text-align: center'}
                            },

                        ],
                        onEdit: function (event) {
                            alert("funcionalidade ainda não implementada.")
                        },
                        onLoad: function (event) {

                        },
                        onSelect: function (event) {

                            this.toolbar.enable('edit_birdclass');

                            //this.toolbar.enable('deleteInscPassaro');
                        },
                        onUnselect: function (event) {
                            this.toolbar.disable('edit_birdclass');
                            //this.toolbar.disable('deleteInscPassaro');
                        },
                        onSubmit: function (event) {

                        },
                        onDelete: function (event) {
                            event.preventDefault();
                            if (confirm("Posso remover este pássaro/equipa?")) {
                                var inscpassaroid = grid_passaros.getSelection()[0];
                                var url = '<?php echo url('ajax_remover_inscricao_passaro') ?>/' + {{ $evento->id }} + '/' + inscpassaroid;
                                $.ajax({
                                    type: "GET",
                                    url: url,
                                    success: function (e) {
                                        swal("Apagado", "Apaguei o pássaro/equipa que pretendia", "success");
                                        refreshPassaros(grid_inscricoes.getSelection()[0]);

                                    }
                                });

                            } else {
                                return false;
                            }

                        },
                        onSave: function (event) {

                            swal("Alterações gravadas", "As sua alterações foram gravadas com sucesso", "success");
                            //refreshPassaros(grid_inscricoes.getSelection())
                            //console.log('save'+grid_inscricoes.getSelection());

                        },
                        onChange: function (event) {

                            //this.reload();
                            //console.log('change');

                        },
                        onRefresh: function (e) {
                            let totalp = this.total;
                            $("#total_passaros").text(totalp);

                        }


                    });


                    /*
                    FIM JAVASCRIPT RELACIONADO COM INSCRIÇÕES
                     */

                    /*
                    JAVASCRIPT RELACIONADO COM JUIZES
                    */

                    grid_juizes_sistema = $('#grid_juizes_sistema').w2grid({
                        name: 'grid_juizes_sistema',
                        header: 'Juizes no sistema',
                        recid: "id",
                        show: {
                            toolbarSearch: false,     // hides search button on the toolbar
                            toolbar: true,
                            footer: false,
                        },
                        multiSearch: true,
                        multiSelect: false,
                        searches: [
                            {field: 'stam', caption: 'STAM ', type: 'text'},
                            {field: 'name', caption: 'Nome', type: 'text'},
                        ],
                        columns: [
                            {field: 'id', caption: 'ID', size: '50px', hidden: true},
                            {field: 'stam', caption: 'STAM', size: '80px', sortable: true},
                            {field: 'pais_abrev', caption: '{{trans("general.pais")}}', size: '60px', sortable: false},
                            {field: 'name', caption: '{{trans("general.nome")}}', size: '30%'}


                        ],
                        onEdit: function (event) {
                            alert("funcionalidade ainda não implementada.")
                        },
                        onLoad: function (event) {
                            //console.log("onload");
                        },
                        onReload: function (event) {
                            refreshJuizesSistema();
                        },
                        onSelect: function (event) {
                            //console.log("inscrição escolhida");
                        },
                        onClick: function (event) {

                        },
                        onSearch: function (target, data) {
                            for (var s in data.searchData) {
                                data.searchData[s].operator = 'contains';
                            }
                        },
                    });
                    grid_juizes_atribuidos = $('#grid_juizes_atribuidos').w2grid({
                        name: 'grid_juizes_atribuidos',
                        header: 'Juizes Atribuidos',
                        recid: "id",
                        show: {
                            toolbarSearch: false,     // hides search button on the toolbar
                            toolbar: true,
                            footer: false,
                        },
                        buttons: {
                            'edit': {
                                type: 'button',
                                id: 'edit',
                                caption: w2utils.lang('Edit'),
                                hint: w2utils.lang('Edit selected record'),
                                img: 'icon-edit',
                                disabled: true
                            },
                        },
                        toolbar: {
                            items: [
                                {
                                    type: 'button',
                                    id: 'deleteAtrib',
                                    caption: 'Remover Atribuição',
                                    img: 'icon-trash',
                                    disabled: true
                                },

                            ],
                            onClick: function (target, event) {
                                event.onComplete = function () {
                                    if (target === 'deleteAtrib') {
                                        if (confirm("Posso remover este Juiz?")) {
                                            var juizclasseid = grid_juizes_atribuidos.getSelection()[0];
                                            var url = '<?php echo url('ajax_remover_juiz_evento_classe') ?>/' + juizclasseid;
                                            $.ajax({
                                                type: "GET",
                                                url: url,
                                                success: function (e) {
                                                    swal("Apagado", "Apaguei a atribuição do juiz que pretendia.", "success");
                                                    refreshJuizesAtribuidos();

                                                }
                                            });

                                        } else {
                                            return false;
                                        }
                                    }
                                }


                            }
                        },
                        multiSearch: true,
                        multiSelect: false,
                        searches: [
                            {field: 'stam', caption: 'STAM ', type: 'text'},
                            {field: 'juiz', caption: 'Nome', type: 'text'},
                        ],
                        columns: [
                            {field: 'id', caption: 'ID', size: '50px', hidden: true},
                            {field: 'stam', caption: 'STAM', size: '50px', sortable: true},
                            {field: 'juiz', caption: 'Nome', size: '200px'},
                            {field: 'pais_abrev', caption: 'Pais', size: '60px', sortable: false},
                            {field: 'seccao', caption: 'Secção', size: '60px', sortable: false},
                            {field: 'seccaoclasse', caption: 'Secção/Classe', size: '20%', sortable: false},

                        ],
                        onEdit: function (event) {
                            alert("funcionalidade ainda não implementada.")
                        },
                        onLoad: function (event) {
                            this.toolbar.disable('edit');
                        },
                        onReload: function (event) {
                            refreshJuizesSistema();
                        },
                        onSelect: function (event) {
                            this.toolbar.enable('deleteAtrib');
                        },
                        onUnselect: function (event) {
                            this.toolbar.disable('deleteAtrib');
                        },
                        onClick: function (event) {

                        },
                        onSearch: function (target, data) {
                            for (var s in data.searchData) {
                                data.searchData[s].operator = 'contains';
                            }
                        },
                    });


                    /*
                    FIM JAVASCRIPT RELACIONADO COM JUIZES
                     */

                    /*
                    JAVASCRIPT RELACIONADO COM SECÇÕES CLASSES IMPORT
                    */


                    gg = $('#grid_classes').w2grid({
                        name: 'grid_classes',
                        header: 'Secções / Classes',
                        recid: "id",
                        show: {
                            toolbar: true,
                            footer: true,
                            toolbarEdit: true,
                            toolbarSearch: false,
                            toolbarInput: false,
                        },
                        multiSearch: true,
                        multiSelect: false,
                        searches: [
                            {field: 'seccao', caption: 'Secção ', type: 'text'},
                            {field: 'classe', caption: 'Classe', type: 'text'},
                            {field: 'nome', caption: 'Nome', type: 'text'},
                        ],
                        columns: [
                            {field: 'id', caption: 'ID', size: '50px', hidden: true},
                            {field: 'seccao', caption: 'Secção', size: '30%', sortable: true},
                            {field: 'classe', caption: 'Classe', size: '30%'},
                            {field: 'nome', caption: 'Nome', size: '50%', sortable: true},

                        ],
                        onEdit: function (event) {
                            alert("funcionalidade ainda não implementada.")

                            //var link = '<?php echo url('users') ?>/' + event['recid'] + '/edit';
                            //window.open(link, "_self");
                        },


                    });

//console.dir(gg);

                    var ie = '{{ $evento->id }}';


                    $("#pesquisaf").submit(function (e) {

                        gg.searchReset();
                        e.preventDefault();
                    });

                    $("#reset_btn").click(function (e) {
                        e.preventDefault();
                        $("#pais_id").val(0);
                        $('#pais_id').selectpicker('refresh');
                        refreshInscricoes();
                    });
                    $("#pesquisainsc").submit(function (e) {

                        e.preventDefault();
                        var dados = $(this).serializeArray();
                        //console.log(dados);

                        var pesquisa = [];
                        let evento_id = {!! $evento->id !!};
                        var indice = 0;

                        var pais_id = 0;

                        if (dados[0].value != "") {
                            pais_id = dados[0].value;
                            pesquisa[indice] = {field: 'pais_id', value: pais_id, operator: 'is'};
                            indice++;

                        }

                        // verificar se apenas a pesquisa livre está activa

                        var linka = '<?php echo url('ajaxusersinscritos') ?>/' + evento_id + '/' + pais_id;

                        refreshInscricoes(linka);

                    });

                    /*
                    FIM JAVASCRIPT RELACIONADO COM SECÇÕES CLASSES IMPORT
                    */


                });

            </script>

        @endsection