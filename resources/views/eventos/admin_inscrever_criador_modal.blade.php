<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3>{{trans ('forms.bt_inscrever_criador')}}</h3>
</div>
<div class="modal-body">
    <div class="row">

        <div class="form-group col-md-12" id="erros">
        </div>

        {!! Form::open(['route' => 'gravarinscricaocriadoreventoadmin','id'=>'forminsc']) !!}
        @include('eventos.admin_formInscreverCriador')

        {{--<div class="form-group col-md-12 pull-right">--}}
            {{--<button type="button" data-dismiss="modal" class="btn">{{ trans('forms.cancelar') }}</button>--}}
            {{--<button class="btn btn-primary">{{ trans('forms.bt_inscrever_criador') }}</button>--}}
            {{--<br>--}}
        {{--</div>--}}
        {!! Form::close() !!}
    </div>

</div>
<script>



    $('#forminsc').submit(function (e) {
        e.preventDefault();
        var $form = $(this);
        var urlpost = $form.attr('action');
        //console.log(urlpost);
        $.ajax({
            type: "POST",
            url: urlpost,
            data: $(this).serialize(),
            success: function (response) {
                $('#ajaxinscreverc').modal('toggle');
                console.log("inscrito");
                //swal("Federação Criada", "A nova Federação foi criada com sucesso", "success");
                refreshInscricoes();
                //console.log("success:" + response);
                //window.location.href = "{{URL::route('adminfederacoeslist')}}";
            },
            error: function (response) {
                if (response.status == 422) {
                    $('#erros').empty();
                    $('#erros').append("<div class=\"note note-danger\" id=\"errosi\"></div>");
                    $.each(response.responseJSON, function (key, value) {
                        $('#errosi').append("<p>" + value + "</p>");
                        //console.log(key + " - " + value);
                    });
                    //console.log(response.responseJSON);
                }
            },
            statusCode: {
                422: function () {
                }
            }
        }).fail(function (response) {
        });
    });




    $(document).ready(function () {

        $("#id_criador_select").select2({
            minimumInputLength: 3,
            theme: "classic",
            ajax: {
                url: '{{ url('users/notinevento',[$evento->id])}}',
                dataType: 'json',
                delay: 250,
                minimumInputLength: 3,
                processResults: function (data) {
                    //console.log("entrou com: "+data);

                    return {
                        results: data
                    };
                }
            }


        });





    });


</script>
