<div class="row">
    <div class="col-md-12">


        <div class="col-md-6">


            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-upload"></i>
                        <span class="caption-subject bold font-yellow-crusta uppercase">{{trans('general.importar')}}</span>
                        <span class="caption-helper">{{trans('general.tabela_fichas_julgamento')}}</span>
                    </div>

                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="col-md-12">
                            <form action="{{ route('importfichasjulgamentoclasses') }}"
                                  class="form-horizontal" method="post" enctype="multipart/form-data">
                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                <input type="hidden" name="evento_id" value="{{ $evento->id }}">
                                <div class="form-group col-xm-12 col-md-2">

                                    <input type="file" name="import_file"/><br>
                                    <button class="btn btn-primary">{{trans('general.importar_fichas_julgamento')}}</button>
                                </div>


                            </form>
                            <br>
                            @if(Session::has('flash_message_success'))
                                <div class="alert alert-success col-sm-12">

                                    {{Session::get('flash_message_success')}}
                                    <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;</button>
                                </div>
                            @endif
                            @if(Session::has('flash_message'))
                                <div class="alert alert-danger col-sm-12">

                                    {{Session::get('flash_message')}}
                                    <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;</button>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>


            </div>


        </div>
        <div class="col-md-6">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-upload"></i>
                        <span class="caption-subject bold font-green-dark uppercase">{{trans('general.exportar')}}</span>
                        <span class="caption-helper">{{trans('general.tabela_fichas_julgamento')}}</span>
                    </div>

                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="col-md-12">
                            <a href="{{ route('downloadexcelclasses',[$evento->id,'xlsx']) }}">
                                <button class="btn btn-default">{{trans('general.download_fichas_julgamento')}} (<i style="color: #207245"
                                                                                             class="fa fa-table"></i>)
                                </button>
                            </a>


                        </div>
                    </div>
                </div>
            </div>


        </div>

        <div class="col-md-12">


        </div>


    </div>
</div>

