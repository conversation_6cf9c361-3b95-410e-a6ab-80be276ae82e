<div class="col-md-6 col-sm-12 col-xs-12">
    <div class="box box-solid box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">{{trans ('general.listagens')}}</h3>
        </div>
        <div class="box-body">
            <div class="list-group">
                <a class="list-group-item" target="_blank" href="<?php echo route('pdffichasinscricaoadmin', [$evento->id]); ?>">{{trans ('general.ficha_engaiolamento')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <button name="femodal" data-id="99" class="list-group-item">{{trans ('general.ficha_engaiolamento_individual')}}...</button>
                <a class="list-group-item" target="_blank" href="<?php echo route('pdfetiquetasprovisorias', [$evento->id]); ?>">Etiquetas Provisórias (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" href="<?php echo route('etiquetasprovisoriasindividuais', [$evento->id]); ?>">{{trans ('clubes.etiquetas_provisorias_individuais')}} ...</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('pdfetiquetasdefinitivas', [$evento->id]); ?>">{{trans ('clubes.etiquetas_definitivas')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" href="<?php echo route('etiquetasdefinitivasindividuais', [$evento->id]); ?>">{{trans ('clubes.etiquetas_definitivas_individuais')}} ...</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('pdffichascontrolojulgamento', [$evento->id]); ?>">{{trans ('clubes.fichas_de_Controlo_de_Julgamento')}}(<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('pdfcatalogoavesclasse', [$evento->id]); ?>">{{trans ('clubes.catalogo_aves_por_classe')}}(<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('pdfcatalogoavesclassecondensado', [$evento->id]); ?>">{{trans ('clubes.catalogo_aves_por_classe_condensado')}}(<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('pdffichasdevolucao', [$evento->id]); ?>">{{trans ('clubes.fichas_de_devolução_e_pagamentos')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('exportanumgaiolastipo', [$evento->id,'pdf']); ?>">{{trans ('clubes.quantidade_gaiolas_por_tipo')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('exportanumgaiolastipo', [$evento->id,'excel']); ?>">{{trans ('clubes.quantidade_gaiolas_por_tipo')}} (<i style="color: #207245" class="fa fa-table"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('exportanumgaiolastiposeccao', [$evento->id]); ?>">{{trans ('clubes.quantidade_gaiolas_seccao_tipo')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('exportaexpositores', [$evento->id,'1']); ?>">{{trans ('clubes.Expositores_ordenada_por_nome')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('exportaexpositores', [$evento->id,'2']); ?>">{{trans ('clubes.Expositores_ordenada_por_num_expositores')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
   <a class="list-group-item" target="_blank" href="<?php echo route('exportaexpositorescontactos', [$evento->id]); ?>">{{trans ('clubes.Expositores_contacto')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('exportanumextrasexpositores', [$evento->id]); ?>">{{trans ('clubes.mesas_jantares_e_ministands')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('exportanumpassarosclasse', [$evento->id]); ?>">{{trans ('clubes.numero_de_passaros_seccao_classe')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

                <a class="list-group-item" target="_blank" href="<?php echo route('exportaexpositoresdespesas', [$evento->id]); ?>">{{trans ('clubes.Expositores_despesas_totais')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('exportaexpositoresdespesas', [$evento->id,'excel']); ?>">{{trans ('clubes.Expositores_despesas_totais')}} (<i style="color: #207245" class="fa fa-table"></i>)</a>

                <a class="list-group-item" target="_blank" href="<?php echo route('exportacontroloanilhas', [$evento->id]); ?>">{{trans ('clubes.controlo_de_anilhas')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

                <a class="list-group-item" target="_blank" href="<?php echo route('exportapremioscriador', [$evento->id]); ?>">{{trans ('clubes.premios_por_criador')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

                <a class="list-group-item" target="_blank" href="<?php echo route('exportadiplomas', [$evento->id,'vencedores']); ?>">{{trans ('clubes.diplomas_de_vencedores')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

                <a class="list-group-item" target="_blank" href="<?php echo route('exportadiplomas', [$evento->id,'participacao']); ?>">{{trans ('clubes.diplomas_de_participacao')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

                <a class="list-group-item" target="_blank" href="<?php echo route('exportadiplomas', [$evento->id,'vencedores',0]); ?>">Diplomas de Vencedores (sem logos) (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>
                <a class="list-group-item" target="_blank" href="<?php echo route('exportadiplomas', [$evento->id,'participacao',0]); ?>">Diplomas de Participação (sem logos) (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>



                <a class="list-group-item" target="_blank" href="<?php echo route('exportapremiosprestigio', [$evento->id]); ?>">{{trans ('clubes.premios_prestigio')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>


                <a class="list-group-item" href="<?php echo route('fichasjulgamentodetalhadas', [$evento->id]); ?>">{{trans ('clubes.fichas_de_julgamneto_detalhadas')}} ... (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>

                <a class="list-group-item" target="_blank" href="<?php echo route('exportaexpositoresclassificacoes', [$evento->id]); ?>">{{trans ('clubes.listagem_expositores_passaros_Classificacoes')}} (<i style="color: #BB0706" class="fa fa-table"></i>)</a>

                <a class="list-group-item" target="_blank" href="<?php echo route('pdffichasdevolucaopais', [$evento->id]); ?>">51. {{trans ('clubes.fichas_de_devolução_e_pagamentos_pais')}} (<i style="color: #BB0706" class="fa fa-file-pdf-o"></i>)</a>



            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-3 col-xs-12">
    <form onsubmit="return validarF()" id="form_procurar_gaiola" action="{!! route('procurar_gaiola') !!}" method="post">
        <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
        <input type="hidden" name="evento_id" value="<?php echo $evento->id; ?>">
        <label for="numgaiola">{{trans ('clubes.num_gaiola')}}</label>
        <input type="text" size="4" class="form-control" name="numgaiola" placeholder="Nº Gaiola">
        <input type="submit" class="btn btn-info" value="Procurar">
    </form>
    <br><br>

</div>
<div class="col-md-3 col-sm-3 col-xs-12">

    <a href="{!! route('gerirfichasjulgamento',[$evento->id]) !!}" class="btn btn-primary">{{trans ('general.fichas_julgamento')}}</a>



</div>
@role('admin')
<div class="col-md-3 col-sm-3 col-xs-12">

    <a href="{!! route('recalcularclassificacoes',[$evento->id]) !!}" class="btn btn-primary">{{trans ('general.recalcular_classificacoes')}}</a>



</div>
@endrole