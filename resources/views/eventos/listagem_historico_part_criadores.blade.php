@extends('layouts.app')


@section('content')

@section('titulo', 'Histórico de Eventos')
<section id="tralha">
    <br>

    @if (count($eventos_inactivos) > 0)
        <div class="row">
            <div class="col-md-12">
                <div class="box box-solid box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">Histórico de Participações</h3>
                    </div>
                    <div class="box-body">
                        <table class="table table-striped task-table">

                            <tbody>
                            @foreach ($eventos_inactivos as $evt)
                                <tr>

                                    <td class="table-text">
                                        <div><h4>{{ $evt->nome }}</h4></div>

                                        <div><strong>Data de Início:</strong> {{ $evt->data_inicio }}</div>
                                        <div><strong>Data de Fim:</strong> {{ $evt->data_fim }}</div>
                                        <div style="margin-top: 10px;">Inscrições de <strong>{{ $evt->data_inicio_inscricoes }}</strong> a <strong>{{ $evt->data_fim_inscricoes }}</strong></div>
                                    </td>

                                    <td>
                                        @if($evt->inscricao==null)

                                            <a ref="inscricao/{{$evt->id}}"><button class="btn btn-primary">Ver Estatísticas</button></a>

                                        @endif

                                    </td>

                                </tr>

                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    @endif





</section>






@endsection
