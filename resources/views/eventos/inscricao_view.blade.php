<div class="form-group row">
    <label class="col-sm-2 bold">{{trans ('eventos.inscricao')}}:</label>
    <div class="col-sm-3">
        {{$inscricao->data_inscricao}}
    </div>
    <label class="col-sm-2 bold">N. {{trans ('general.expositor')}}:</label>
    <div class="col-sm-3">
        {{$inscricao->num_expositor}}
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-2 bold">{{trans ('general.jantares')}}:</label>
    <div class="col-sm-3">
        {{$inscricao->num_jantares}}
    </div>

</div>
@if((int)$evento->tipo_evento_id !== 6)
<div class="form-group row">
    <label class="col-sm-2 bold">{{trans ('general.mesas_de_venda')}}:</label>
    <div class="col-sm-3">
        {{$inscricao->num_mesas_venda}}
    </div>
    <label class="col-sm-2 bold">{{trans ('general.numero_de_stands')}}:</label>
    <div class="col-sm-3">
        {{$inscricao->num_stands}}
    </div>
    <label class="col-sm-2 bold"></label>
    <div class="col-sm-3">

    </div>
</div>
@endif

<div class="form-group row">
    <label class="col-sm-2 bold">{{trans('general.local_recolha')}}</label>
    <div class="col-sm-3">
        {{$inscricao->local_recolha_nome}}<br>
        {{$inscricao->local_recolha_morada}}
    </div>
    <label class="col-sm-2 bold"></label>
    <div class="col-sm-3">

    </div>

</div>

<div class="form-group row">
    @if($evento->editavelcriador)
        <div class="col-md-3">
            <a href="{!! route('editarinscricaocriador',[$inscricao->id]) !!}" class="btn btn-primary" data-target="#ajaxeditar" data-toggle="modal">{{trans ('general.detalhes_inscricao')}}</a>


        </div>
        <div class="modal fade bs-modal-sm" id="ajaxeditar" role="basic" style="display: none">
            <div class="modal-dialog modal-sm">
                <div class="modal-content" style="width: 400px">
                    <div class="modal-body">
                        <span> &nbsp;&nbsp;Loading... </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <form id="form_remover" action="<?php echo route('removerinscricaocriadorevento'); ?>" method="post">
                <input type="hidden" name="eventoid" value="{{$evento->id}}">
                <input type="hidden" name="inscricaoid" value="{{$inscricao->id}}">
                <input type="hidden" name="rdp" value="eventohomecriador">
                <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                <button type="submit" class="btn btn-danger" data-title="Tem a certeza que pretende remover a sua inscrição?" data-btn-cancel-label="Não!" data-btn-ok-label="Sim" data-toggle="confirmation" data-popout="true" data-original-title="" title="" aria-describedby="confirmation57493">{{trans ('eventos.remover_inscricao')}}</button>
            </form>

        </div>
    @endif

        <div class="col-md-3">
            <a href="{!! route('pdffichainscricao', [$evento->id]) !!}" class="btn btn-default"
               target="_listagem">{{trans ('general.ficha_inscricao')}}</a>

        </div>
    <div class="col-md-3">
        @if($hoje>=$data_abertura_listagens)
            <a href="{!! route('pdffichasjulgamentoexpositor', [$evento->id]) !!}" class="btn btn-default" target="_listagem">{{trans ('general.fichas_de_julgamento')}}</a>
        @endif
    </div>
</div>

