@extends('layouts.app')

@section('titulo','Import Export de Secções/Classes')
@section('subtitulo','')

@section('content')
    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{$evento->nome}}
                        </div>

                    </div>
                    <div class="portlet-body">
                        <div class="note note-warning">

                            <h4><i class="icon fa fa-ban"></i> Cuidado</h4>
                            A importação de uma nova tabela de secções/classes removerá:<br>
                            - Todos as secções/classes anteriores;<br>
                            - Todos os pássaros inscritos (as inscrições de criadores serão mantidas).
                        </div>


                    </div>
                </div>




                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{$evento->nome}}</h3>
                    </div>
                    <div class="box-body">


                        <div class="box box-default box-solid">
                            <div class="box-header with-border">
                                <h3 class="box-title">Importar Tabela</h3>

                            </div>

                            <div class="box-body">
                                <form action="{{ URL::to('importExcel') }}"
                                      class="form-horizontal" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="evento_id" value="{{ $evento->id }}">
                                    <div class="form-group col-xm-12 col-md-2">

                                        <input type="file" name="import_file"/><br>
                                        <button class="btn btn-primary">Importar Ficheiro</button>
                                    </div>


                                </form>
                            </div>

                        </div>


                        <div id="grid_classes" style="width: 100%; height: 450px;"></div>
                        <br>
                        <div class="box box-default box-solid">
                            <div class="box-header with-border">
                                <h3 class="box-title">Exportar Tabela</h3>

                            </div>

                            <div class="box-body">
                                <a href="{{ route('downloadexcelclasses',[$evento->id,'xlsx']) }}">
                                    <button class="btn btn-default">Download Secções/Classes  (<i style="color: #207245" class="fa fa-table"></i>)</button>
                                </a>

                                {{--<a href="{{ URL::to('downloadExcel/xls') }}">
                                    <button class="btn btn-success">Download Excel xls</button>
                                </a>

                                <a href="{{ URL::to('downloadExcel/csv') }}">
                                    <button class="btn btn-success">Download CSV</button>
                                </a>--}}
                            </div>

                        </div>


                    </div>
                </div>

            </div>
        </div>
    </div>

@endsection

@section('scripts')
    <script>

        $(document).ready(function () {
            w2utils.settings.dataType = 'RESTFULL';
            w2utils.locale('https://raw.githubusercontent.com/vitmalina/w2ui/master/src/locale/pt-br.json')

            var gg = $('#grid_classes').w2grid({
                name: 'grid_classes',
                header: 'Secções / Classes',
                url: "{{URL::route('ajaxseccoesclassesadmin',['id_evento'=>$evento->id])}}",
                recid: "id",
                show: {
                    toolbar: true,
                    footer: true,
                    toolbarEdit: true,
                },
                multiSearch: true,
                multiSelect: false,
                searches: [
                    {field: 'seccao', caption: 'Secção ', type: 'text'},
                    {field: 'classe', caption: 'Classe', type: 'text'},
                    {field: 'nome', caption: 'Nome', type: 'text'},
                ],
                columns: [
                    {field: 'id', caption: 'ID', size: '50px', hidden: true},
                    {field: 'seccao', caption: 'Secção', size: '30%', sortable: true},
                    {field: 'classe', caption: 'Classe', size: '30%'},
                    {field: 'nome', caption: 'Nome', size: '50%', sortable: true},

                ],
                onEdit: function (event) {
                    alert("funcionalidade ainda não implementada.")

                    //var link = '<?php echo url('users') ?>/' + event['recid'] + '/edit';
                    //window.open(link, "_self");
                },


            });

//console.dir(gg);

            var ie = '{{ $evento->id }}';

            //gg.refresh();
           // gg.request('get-records', [], '{{URL::route('ajaxseccoesclassesadmin',['id_evento'=>$evento->id])}}');


            $("#pesquisaf").submit(function (e) {

                gg.searchReset()
                e.preventDefault();
            });


        });

    </script>

@endsection