@extends('layouts.app')


@section('titulo',trans('general.julgamento_evento'))
@section('subtitulo',$evento->nome)


@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('homecriador'); ?>">{{trans('menus.home')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('julgareventohome',[$evento->id]) !!}">{{trans('general.julgamento')}} {{$evento->nome}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{trans('general.julgamento_seccao')}}  {!! $seccao !!}</span>
        </li>
    </ul>

@endsection

@section('content')
    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{trans('general.classes_na_seccao')}}  {!! $seccao !!}
                        </div>

                    </div>
                    <div class="portlet-body">
                        <div class="col-sm-12" style="margin-bottom: 10px;">

                            <a href="{!! route('julgareventohome',[$evento->id]) !!}" class="col-sm-3 btn btn-primary"
                               role="button">< {{trans('general.regressar_seccoes')}} </a>


                        </div>
                        <div class="form-group">
                            <div class="table-scrollable">
                                <table class="table table-striped table-hover">
                                    <thead>
                                    <tr>
                                        <th> {{trans('general.classes_a_julgamento')}} </th>
                                        <th> {{trans('general.progresso')}} </th>
                                        <th> {{trans('general.bloqueado')}} </th>

                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($classes as $c)
                                        <tr>
                                            <td> @if($c->juiz_user_id!=0 && $c->juiz_user_id != $useractivo_id)
                                                    <a disabled style="text-align: left"
                                                       class="btn btn-lg btn-default col-sm-12"><b>{!! $c->seccao !!} {!! $c->classe !!}</b><br><span
                                                                style="font-size: 12px">{!! $c->nome !!}</span></a>

                                                @else
                                                    <a href="{!! route('julgareventogaiolas',[$evento->id,$c->resc_id]) !!}"
                                                       style="text-align: left"
                                                       class="btn btn-lg btn-default col-sm-12"><b>{!! $c->seccao !!} {!! $c->classe !!}</b><br><span
                                                                style="font-size: 12px">{!! $c->nome !!}</span></a>

                                                @endif

                                            </td>
                                            <td>

                                                <div class="progress-info">
                                                    <div class="status">
                                                        <div class="status-title"> Progress - {!! $c->njulgados !!} out of {!! $c->ntotal !!}</div>
                                                        <div class="status-number"> {!! $c->percentage !!}%</div>
                                                    </div>
                                                    <div class="progress">
                                            <span style="width: {!! $c->njulgados*100/$c->ntotal !!}%;"
                                                  class="progress-bar progress-bar-{!! $c->progress_bar_type !!}">
                                                <span class="sr-only">{!! $c->percentage!!}% progress</span>
                                            </span>
                                                    </div>

                                                </div>


                                                </div>
                                            </td>
                                            <td style="text-align: center;vertical-align: middle">
                                                @if($c->juiz_user_id!=0 && $c->juiz_user_id != $useractivo_id)
                                                    {{trans('general.bloqueado')}}

                                                @else

                                                    --
                                                @endif


                                            </td>

                                        </tr>
                                    @endforeach


                                    </tbody>
                                </table>
                            </div>


                        </div>
                    </div>

                </div>


            </div>
        </div>

    </div>
    <!-- Main content -->

    <br>
    <br>




@endsection
