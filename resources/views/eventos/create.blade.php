<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3>{{ trans('forms.criar_evento') }}</h3>
</div>
<div class="modal-body">
    <div class="row">

        <div class="form-group col-md-12" id="erros">
        </div>
        <div class="col-sm-12">
            {!! Form::open(['route' => 'admineventostore','id'=>'formevt']) !!}


            @include('eventos._formgeral', ['tipo'=>'create'])


            <div class="form-group col-md-12 pull-right">
                <button type="button" data-dismiss="modal" class="btn">{{ trans('forms.cancelar') }}</button>
                <button class="btn btn-primary">{{ trans('forms.criar') }}</button>
                <br>
            </div>
            {!! Form::close() !!}
        </div>


    </div>

</div>
<script>


    var clubesassociados = [];
    var paisessobretaxas = [];

    function removeClube(id) {

        $("#c" + id).remove();

        clubesassociados.splice(clubesassociados.indexOf(id), 1);

    }

    function removePaisSobretaxa(id) {

        $("#ps" + id).remove();
        var indice = findPaisSobretaxa(id);
        paisessobretaxas.splice(indice, 1);

    }

    function findPaisSobretaxa(id){
        var index = -1;
        for(var i=0;i< paisessobretaxas.length;i++){
            console.log(i+" = "+paisessobretaxas["psid"]);
            if(paisessobretaxas[i]["psid"]==id){
               index=i;
               break;
            }

        }
        return index;
    }


    $(document).ready(function () {

        // popular clubes organizadores

        //TODO: popular clubes organizadores ou adicionar uma tab no gerir para isso


        $("#adicionarsobretaxapaisbt").click(function (evt) {
            console.log("adding country surcharge: "+$("#paiseslista").val() )
            var psid = $("#paiseslista").val();
            var psvalorsobretaxa = $("#valor_sobretaxa_pais").val();
            var psnome = $("#paiseslista option:selected").text();

            if (paisessobretaxas.length <= 999) {
                if (findPaisSobretaxa(psid) != -1) {

                    bootbox.alert({
                        title: "Hmm..",
                        message: "O país que selecionou já tem sobretaxa no evento.",
                        backdrop: true
                    });

                } else {

                    let temp = {psid: psid, sobretaxa: psvalorsobretaxa};

                    paisessobretaxas.push(temp);
                    $("#paisesescolhas").append("<span class=\"col-md-6\" id=\"ps" + psid + "\"><span class=\"col-md-6\" style=\"height: 34px;padding-top: 5px\">" + psnome + "</span><span class=\"col-md-6\">"+psvalorsobretaxa+"<a onclick=\"removePaisSobretaxa('" + psid + "')\" href=\"#\" id=\"btps" + psid + "\" class=\"btn\"><i class=\"fa fa-trash font-red\"></i></a></span></span>");

                }

            } else {

                bootbox.alert({
                    title: "Hmm..",
                    message: "O máximo de sobretaxas que pode adicionar são 999.",
                    backdrop: true
                });

            }


            $("#paiseslista")[0].selectedIndex = 0;

        });



        $("#associarclubebt").click(function (evt) {

            var id = $("#clubeslista").val();
            var nome = $("#clubeslista option:selected").text();

            if (clubesassociados.length <= 6) {
                if ($.inArray(id, clubesassociados) != -1) {


                    bootbox.alert({
                        title: "Hmm..",
                        message: "O clube que tentou associar já existe no evento.",
                        backdrop: true
                    });


                } else {


                    clubesassociados.push(id);
                    $("#clubesescolhas").append("<span class=\"col-md-12\" id=\"c" + id + "\"><span class=\"col-md-9\">" + nome + "</span><span class=\"col-md-3\"><a onclick=\"removeClube('" + id + "')\" href=\"#\" id=\"bt" + id + "\" class=\"btn\"><i class=\"fa fa-trash font-red\"></i></a></span></span>");
                    //console.log(clubesassociados);


                }

            } else {

                bootbox.alert({
                    title: "Hmm..",
                    message: "O máximo de clubes que pode associar a um evento são 7.",
                    backdrop: true
                });

            }


            $("#clubeslista")[0].selectedIndex = 0;


        });


        $('#data_inicio').daterangepicker({
            singleDatePicker: true,
            "locale": {
                "format": "YYYY/MM/DD",
                "separator": " - ",
                "applyLabel": "Apply",
                "cancelLabel": "Cancel",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "right",
        }, function (start, end, label) {
            //$("input[name='data_inicio']").val(start.format('YYYY-MM-DD'));
            //$("input[name='data_fim']").val(start.format('YYYY-MM-DD'));
//console.log('New date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD') + ' (predefined range: ' + label + ')');
        });
        $('#data_fim').daterangepicker({
            singleDatePicker: true,
            "locale": {
                "format": "YYYY/MM/DD",
                "separator": " - ",
                "applyLabel": "Apply",
                "cancelLabel": "Cancel",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "right",
        }, function (start, end, label) {
            //$("input[name='data_inicio']").val(start.format('YYYY-MM-DD'));
            //$("input[name='data_fim']").val(start.format('YYYY-MM-DD'));
//console.log('New date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD') + ' (predefined range: ' + label + ')');
        });
        $('#data_inicio_inscricoes').daterangepicker({
            singleDatePicker: true,
            "locale": {
                "format": "YYYY/MM/DD",
                "separator": " - ",
                "applyLabel": "Apply",
                "cancelLabel": "Cancel",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "center",
        }, function (start, end, label) {
            //$("input[name='data_inicio_inscricoes']").val(start.format('YYYY-MM-DD'));
            //$("input[name='data_fim_inscricoes']").val(start.format('YYYY-MM-DD'));


            //console.log('New date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD') + ' (predefined range: ' + label + ')');
        });
        $('#data_fim_inscricoes').daterangepicker({
            singleDatePicker: true,
            "locale": {
                "format": "YYYY/MM/DD",
                "separator": " - ",
                "applyLabel": "Apply",
                "cancelLabel": "Cancel",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "center",
        }, function (start, end, label) {
            //$("input[name='data_inicio_inscricoes']").val(start.format('YYYY-MM-DD'));
            //$("input[name='data_fim_inscricoes']").val(start.format('YYYY-MM-DD'));


            //console.log('New date range selected: ' + start.format('YYYY-MM-DD') + ' to ' + end.format('YYYY-MM-DD') + ' (predefined range: ' + label + ')');
        });
        $('#data_inicio_julg').daterangepicker({
            singleDatePicker: true,
            "timePicker": true,
            "timePicker24Hour": true,
            "timePickerIncrement": 15,
            "dateLimit": {
                "days": 7
            },
            "locale": {
                "format": "YYYY/MM/DD HH:mm",
                "separator": " - ",
                "applyLabel": "Gravar",
                "cancelLabel": "Cancelar",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "left",
        }, function (start, end, label) {
            //$("input[name='data_inicio_julg']").val(start.format('YYYY-MM-DD hh:mm'));
            //$("input[name='data_fim_julg']").val(start.format('YYYY-MM-DD hh:mm'));


            //console.log('New date range selected: ' + start.format('YYYY-MM-DD hh:mm') + ' to ' + end.format('YYYY-MM-DD hh:mm') + ' (predefined range: ' + label + ')');
        });
        $('#data_fim_julg').daterangepicker({
            singleDatePicker: true,
            "timePicker": true,
            "timePicker24Hour": true,
            "timePickerIncrement": 15,
            "dateLimit": {
                "days": 7
            },
            "locale": {
                "format": "YYYY/MM/DD HH:mm",
                "separator": " - ",
                "applyLabel": "Gravar",
                "cancelLabel": "Cancelar",
                "fromLabel": "From",
                "toLabel": "To",
                "customRangeLabel": "Custom",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Su",
                    "Mo",
                    "Tu",
                    "We",
                    "Th",
                    "Fr",
                    "Sa"
                ],
                "monthNames": [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December"
                ],
                "firstDay": 1
            },
            "alwaysShowCalendars": true,
            "opens": "left",
        }, function (start, end, label) {
            //$("input[name='data_inicio_julg']").val(start.format('YYYY-MM-DD hh:mm'));
            //$("input[name='data_fim_julg']").val(start.format('YYYY-MM-DD hh:mm'));


            //console.log('New date range selected: ' + start.format('YYYY-MM-DD hh:mm') + ' to ' + end.format('YYYY-MM-DD hh:mm') + ' (predefined range: ' + label + ')');
        });


    });



    $('#formevt').submit(function (e) {
        e.preventDefault();
        var numclubesassociados = 0;
        $.each(clubesassociados, function (i, v) {
            var input = $("<input>").attr({"type": "hidden", "name": "clubeslista[]"}).val(v);
            $('#formevt').append(input);
            //console.log(i + " - " + v);
            numclubesassociados++;
        });
console.log(paisessobretaxas);
        $.each(paisessobretaxas, function (i, v) {
console.log("a adicionar: "+v.psid+" com "+v.sobretaxa)
            var inputpaises = $("<input>").attr({"type": "hidden", "name": "stpaiseslista[]"}).val(v.psid);
            var inputtaxas = $("<input>").attr({"type": "hidden", "name": "sttaxaslista[]"}).val(v.sobretaxa);
            $('#formevt').append(inputpaises);
            $('#formevt').append(inputtaxas);
        });

        if(numclubesassociados==0){

            $('#erros').empty();
            $('#erros').append("<div class=\"note note-danger\" id=\"errosi\"></div>");
            $('#errosi').append("<p>{{ trans('forms.associar_pelo_menos_um_clube') }}</p>");
            return false;
        }

        var $form = $(this);
        var urlpost = $form.attr('action');
        //console.log($(this).serialize());
        $.ajax({
            type: "POST",
            url: urlpost,
            data: $(this).serialize(),
            success: function (response) {
                //console.log("entrou no fim do criar");
                $('#ajaxcreateevt').modal('toggle');
                swal("Evento Criado", "O novo evento foi criado com sucesso", "success");
                //gg.reload();
                gg.load('{{URL::route('ajaxeventsadmin')}}');
            },
            error: function (response) {
                if (response.status == 422) {
                    $('#erros').empty();
                    $('#erros').append("<div class=\"note note-danger\" id=\"errosi\"></div>");
                    $.each(response.responseJSON, function (key, value) {
                        $('#errosi').append("<h6><p>" + value + "</p></h6>");
                        //console.log(key + " - " + value);
                    });
                    //console.log(response.responseJSON);
                }
            },
            statusCode: {
                422: function () {
                }
            }
        }).fail(function (response) {
        });
    });
</script>
