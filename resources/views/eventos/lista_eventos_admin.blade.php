@extends('layouts.app')

@section('page_css')
    <link href="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.css')}}" rel="stylesheet"
          type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css') }}" rel="stylesheet"
          type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css')}}"
          rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css')}}"
          rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css')}}"
          rel="stylesheet" type="text/css"/>

@endsection
@section('titulo', 'Gestão de Eventos')
@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="{{URL::route('casaadmin')}}">{{trans ('forms.inicio_admin')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{trans ('general.gestao_eventos')}}</span>
        </li>
    </ul>
@endsection
@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-12">


                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class=" icon-layers font-green"></i>
                            <span class="caption-subject bold uppercase"> {{trans ('menus.eventos')}}</span>
                        </div>

                    </div>

                    <div class="portlet-body">
                        @if(Session::has('flash_message'))
                            <div class="alert alert-success">

                                {{Session::get('flash_message')}}
                                <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;
                                </button>
                            </div>
                        @endif
                        <div style="text-align: left;margin-top: 10px;margin-bottom: 15px;" class="form-group">
                            <a class="demo btn btn-primary btn-large" data-toggle="modal"
                               href="{{URL::route('admineventocreate')}}"
                               data-target="#ajaxcreateevt">{{trans('forms.criar_evento')}}</a>

                        </div>


                        <div id="eventsgrid" style="width: 100%; height: 500px;"></div>


                    </div>


                </div>


            </div>
        </div>
    </div>
    <div class="modal fade bs-modal-ls" id="ajaxcreateevt" role="basic" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">
                    <span> &nbsp;&nbsp;{{trans('general.loading')}}</span>
                </div>
            </div>
        </div>
    </div>

@endsection
@section('scripts')

    <!-- BEGIN PAGE LEVEL SCRIPTS -->

    <script src="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.min.js')}}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/pages/scripts/ui-sweetalert.min.js')}}" type="text/javascript"></script>



    <script src="{{ asset('assets/global/plugins/moment.min.js') }}" type="text/javascript"></script>
    <script src="{{ asset('assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.js') }}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js') }}"
            type="text/javascript"></script>
    <script src="{{asset('assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js')}}"
            type="text/javascript"></script>
    <script src="{{asset('assets/global/plugins/bootbox/bootbox.min.js')}}"
            type="text/javascript"></script>


    <script src="{{asset('assets/global/scripts/app.min.js')}}" type="text/javascript"></script>


    <!-- END PAGE LEVEL SCRIPTS -->
    <script>
        var gg;
        $(document).ready(function () {


            $(".date-picker").datepicker();

            w2utils.settings.dataType = 'RESTFULL';
            w2utils.locale('https://raw.githubusercontent.com/vitmalina/w2ui/master/src/locale/pt-br.json')
// TODO: alterar o locale logo acima para a lingua escolhida
            gg = $('#eventsgrid').w2grid({
                name: 'eventsgrid',
                recordHeight: 40,
                header: 'Eventos',
                multiSelect: false,
                multiSearch: true,
                recid: "id",
                show: {
                    toolbarSearch: true,     // hides search button on the toolbar
                    toolbarInput: true,     // hides search input on the toolbar
                    toolbar: true,
                    footer: true,
                    toolbarEdit: true,
                    toolbarDelete: true,
                },
                toolbar: {
                    onClick: function (event) {
                        // console.log(event);
                        /*
                                                event.onComplete = function () {
                                                    //console.log(event);
                                                    if (event.target == 'editPerm') {
                                                        var sel = this.owner.getSelection('recid');
                                                        //TODO: ver se este url está correcto
                                                        var link = '<?php echo url('evento') ?>/' + this.owner.getSelection('recid') + '/editperm';
                                window.open(link, "_self");
                            }

                        }
*/
                    }
                },
                searches: [
                    {field: 'nome', caption: 'Nome', type: 'text'},
                ],
                columns: [
                    {field: 'id', caption: 'ID', size: '50px', hidden: true},
                    {field: 'nome', caption: 'Nome', size: '30%', sortable: true},
                    {field: 'estado', caption: 'Estado', size: '55px', sortable: true},
                    {field: 'tipo_evento', caption: 'Tipo', size: '60px'},
                    {field: 'data_inicio', caption: 'Data Início', size: '80px'},
                    {field: 'data_fim', caption: 'Data Fim', size: '80px'},
                    {field: 'datainsc', caption: 'Inscrições', size: '80px'},


                ],
                onEdit: function (event) {
                    //TODO: ver se este url está correcto
                    var link = '<?php echo url('evento') ?>/' + event['recid'];
                    window.open(link, "_self");
                },
                onUnselect: function (event) {

                    var sel = this.getSelection();
                    //console.log(sel);
                    //if (sel.length == 1) this.toolbar.disable('editPerm');
                },
                onReload: function (event) {

                    gg.load('{{URL::route('ajaxeventsadmin')}}');

                },
                onSearch: function (target, data) {

                    //gg.load('{{URL::route('ajaxeventsadmin')}}');
                    //console.log("search");

                },
                onRender: function () {
                    //gg.load('{{URL::route('ajaxeventsadmin')}}');
                },
                onDelete: function (event) {
                    event.preventDefault();
                    var sel = this.getSelection();
                    var nome = gg.get(sel)[0]['nome'];
                    var id = gg.get(sel)[0]['id'];

                    var link = '<?php echo url('ajax_verificaeventoeliminacao_admin') ?>/' + id;
                    $.ajax({
                        type: "GET",
                        url: link,
                        success: function (response) {

                            //console.log(response);
                            var apagavel = false;
                            var mensagem1;
                            var mensagem2;
                            if (response == '0') { // se false, evento tem tralha


                                mensagem1 = "Este Evento tem inscrições, secções, ou outros conteúdos."
                                mensagem2 = 'Remova todo o conteúdo antes de tentar eliminar.';

                            } else {

                                apagavel = true;
                                mensagem1 = "Posso eliminar o Evento?";
                                mensagem2 = nome;

                            }

                            swal({
                                    title: mensagem1,
                                    text: mensagem2,
                                    type: "warning",
                                    showCancelButton: true,
                                    showConfirmButton: apagavel,
                                    cancelButtonText: "Cancelar",
                                    confirmButtonClass:
                                        'btn-danger',
                                    confirmButtonText:
                                        'Sim, apagar.',
                                    closeOnConfirm:
                                        false,
                                    //closeOnCancel: false
                                },

                                function () {


                                    $.ajax({
                                        type: "GET",
                                        url: '<?php echo url('ajax_deleteevento_admin') ?>/' + id,
                                        success: function (response) {
                                            swal("Apagado", "Apaguei o Evento que pretendia", "success");
                                            //gg.reload();
                                            gg.load('{{URL::route('ajaxeventsadmin')}}');
                                        },
                                        error: function (response) {


                                        }
                                    });


                                });


                        },
                        error: function (response) {


                        }
                    }).fail(function (response) {

                    });


                    //console.log('selection:', gg.get(sel));


                }


            });

            gg.load('{{URL::route('ajaxeventsadmin')}}');
        });


    </script>

@endsection
