@extends('layouts.app')


@section('titulo',trans('general.julgamento_evento'))
@section('subtitulo',$evento->nome)


@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('homecriador'); ?>">{{trans('menus.home')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('julgareventohome',[$evento->id]) !!}">{{trans('general.julgamento')}} {{$evento->nome}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('julgareventoseccao',[$evento->id,$seccaoclasse->seccao]) !!}">{{trans('general.julgamento_seccao')}} {!! $seccaoclasse->seccao !!}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{trans('general.gaiolas_em')}} {!! $seccaoclasse->seccao !!}</span>
        </li>
    </ul>

@endsection

@section('content')
    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{trans('general.gaiolas_em')}} {!! $seccaoclasse->seccao." ".$seccaoclasse->classe !!} {!! $seccaoclasse->nome !!}
                        </div>

                    </div>
                    <div class="portlet-body">
                        <div class="col-sm-8 pull-left" style="margin-bottom: 10px;">

                            <a href="{!! route('julgareventoseccao',[$evento->id,$seccaoclasse->seccao]) !!}" class="col-sm-3 btn btn-primary"
                               role="button">< {{trans('general.regressar_classes')}}</a>


                        </div>
                        <div class="col-sm-4" style="margin-bottom: 10px;">

                            <a href="" class="col-sm-4 btn btn-warning pull-right"
                               role="button">{{trans('general.fechar_classe')}}</a>


                        </div>
                        <div class="form-group">
                            <div class="table-scrollable">
                                <table class="table table-striped table-hover">
                                    <thead>
                                    <th></th>
                                    <th style="text-align: center">{{trans('general.pontos')}}</th>
                                    <th style="text-align: center">{{trans('general.estado')}}</th>
                                    <th style="text-align: center">{{trans('general.classificacao')}}</th>

                                    </thead>
                                    <tbody>
                                    @foreach($gaiolas as $g)
                                        <tr>
                                            <td style="">
                                                <a href="{!! route('julgargaiolaficha',[$evento->id,$g->id]) !!}"
                                                   style="font-size: 20px;margin-left: 5px;{!! $g->dup_color !!}" class="list-group-item"
                                                   role="button">{!! (substr($g->numgaiola, -1)=='A')?substr($g->numgaiola,0,strlen($g->numgaiola)-1)." A B C D (".trans('general.equipa').")": $g->numgaiola !!}</a>


                                            </td>
                                            <td style="text-align: center">
                                                @if($seccaoclasse->classe%2 == 0)
                                                    {!! $g->pontos !!}

                                                @else
                                                    {!! $g->pontosequipa !!}
                                                @endif

                                            </td>
                                            <td style="text-align: center">
                                                {!! $g->estado !!}


                                            </td>
                                            <td style="text-align: center;font-weight: bold;font-size: 14px;">

                                                {!! $g->classificacao !!}

                                            </td>

                                        </tr>

                                    @endforeach


                                    </tbody>


                                </table>

                            </div>


                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>














    <!-- Main content -->

    <br>
    <br>




@endsection
