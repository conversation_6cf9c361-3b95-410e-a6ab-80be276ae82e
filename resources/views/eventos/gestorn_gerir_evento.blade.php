@extends('layouts.app')

@section('page_css')
    <link href="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.css')}}" rel="stylesheet"
          type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css') }}" rel="stylesheet"
          type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css')}}"
          rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css')}}"
          rel="stylesheet" type="text/css"/>
    <link href="{{ asset('assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css')}}"
          rel="stylesheet" type="text/css"/>
    {{--<link href="{{ asset('assets/global/plugins/select2/css/select2.min.css')}}"--}}
    {{--rel="stylesheet" type="text/css"/>--}}
    {{--<link href="{{ asset('assets/global/plugins/select2/css/select2-bootstrap.min.css')}}"--}}
    {{--rel="stylesheet" type="text/css"/>--}}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" rel="stylesheet"/>


@endsection
@section('titulo','Gestão de Evento - Admin')
@section('subtitulo',$evento->nome)

@section('breadcrumbs')
    @role('admin')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('homegestornacional'); ?>">{{ trans('menus.homegestorn') }}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{$evento->nome}}</span>
        </li>
    </ul>
    @else
        <ul class="page-breadcrumb">
            <li>
                <a href="<?php echo route('casa'); ?>">{{trans('clubes.inicio')}}</a>
                <i class="fa fa-angle-right"></i>
            </li>
            <li>
                <span>{{trans('eventos.evento')}}</span>
            </li>
        </ul>


        @endrole


@endsection

@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-12">

                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{$evento->nome}}
                        </div>

                    </div>
                    <div class="portlet-body">
                        <div class="tabbable-custom">
                            <ul class="nav nav-tabs ">
                                <li class="active">
                                    <a href="#tabgeral" data-toggle="tab"
                                       aria-expanded="false">{{ trans('general.geral') }} </a>
                                </li>
                                <li class="">
                                    <a onclick="refreshInscricoes()" href="#tabinscricoes" data-toggle="tab"
                                       aria-expanded="true">{{ trans('general.inscricoes') }}<span
                                                class="badge badge-success"> {{$num_inscricoes}} </span></a>
                                </li>
                                <li class="">
                                    <a href="#tablistagens" data-toggle="tab"
                                       aria-expanded="false">{{ trans('general.listagens') }} </a>
                                </li>

                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="tabgeral">
                                    <h3>{{ trans('general.dados_gerais_eventos') }}
                                    </h3>
                                    <hr>
                                    @include("eventos.evento_view")
                                    <div class="row">
                                        <div class="col-md-12">
                                            <hr>
                                            <h4>{{ trans('general.sobretaxa_pais') }}</h4>
                                            <hr>
                                            <div class="form-group row">
                                                <label class="col-sm-2 bold">{{trans('general.sobretaxa')}}</label>
                                                <div class="col-sm-3">
                                                    {{$sobretaxa_pais->taxa}}€
                                                </div>


                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <hr>
                                            <h4>{{ trans('general.clubes_organizadores') }}</h4>
                                            <hr>
                                            @foreach($clubesorganizadores as $c)
                                                <div class="col-md-6">
                                                    <span style="padding: 10px;"><i
                                                                class="fa fa-dot-circle-o"></i> {{$c->nome}}</span>

                                                </div>

                                            @endforeach
                                        </div>
                                    </div>

                                </div>
                                <div class="tab-pane" id="tabinscricoes">
                                    <h3>{{ trans('general.inscricoes') }} <a
                                                class="btn btn-circle btn-icon-only btn-default"
                                                data-toggle="modal"
                                                href="{{ url('eventos',[$evento->id,'inscrevergestorn'])}}"
                                                data-target="#ajaxinscreverc"><i class="fa fa-plus"></i></a></h3>
                                    <hr>
                                    <div class="form-group col-md-12" id="erros">
                                    </div>
                                    @include("eventos.gestorn_gerir_inscricoes_inc")
                                </div>
                                <div class="tab-pane" id="tablistagens">
                                    <h3>{{ trans('general.listagens') }}</h3>
                                    <hr>
                                    <div class="form-group col-md-12" id="erros">
                                    </div>
                                    @include("eventos.gestorn_gerir_listagens_inc")
                                </div>


                            </div>
                        </div>


                    </div>

                </div>

            </div>

        </div>

    </div>


    {{--<div class="modal fade bs-modal-ls" id="ajaxeditevt" role="basic" aria-hidden="true">--}}
    {{--<div class="modal-dialog modal-lg">--}}
    {{--<div class="modal-content">--}}
    {{--<div class="modal-body">--}}
    {{--<img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">--}}
    {{--<span> &nbsp;&nbsp;{{trans('general.loading')}}</span>--}}
    {{--</div>--}}
    {{--</div>--}}
    {{--</div>--}}
    {{--</div>--}}

    <div class="modal fade bs-modal-ls" id="edit_classe_passaro" role="basic" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body" id="modaleditclasse">
                    <img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">
                    <span> &nbsp;&nbsp;{{trans('general.loading')}}</span>
                </div>
            </div>
        </div>
    </div>


@endsection


@section('scripts')
    <!-- BEGIN PAGE LEVEL SCRIPTS -->

    <script src="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.min.js')}}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/pages/scripts/ui-sweetalert.min.js')}}" type="text/javascript"></script>



    <script src="{{ asset('assets/global/plugins/moment.min.js') }}" type="text/javascript"></script>
    <script src="{{ asset('assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.js') }}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js') }}"
            type="text/javascript"></script>
    <script src="{{asset('assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js')}}"
            type="text/javascript"></script>
    <script src="{{asset('assets/global/plugins/bootbox/bootbox.min.js')}}"
            type="text/javascript"></script>
    {{--<script src="{{asset('assets/global/plugins/select2/js/select2.full.min.js')}}"--}}
    {{--type="text/javascript"></script>--}}

    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>

    <script src="{{asset('assets/global/scripts/app.min.js')}}" type="text/javascript"></script>
    {{--<script src="{{asset('assets/pages/scripts/components-select2.min.js')}}" type="text/javascript"></script>--}}

    <!-- END PAGE LEVEL SCRIPTS -->
    <script>

        let url_inscricoes_base = '{{URL::route('getusersinscritosgestorn',['id_evento'=>$evento->id])}}';
        let url_inscricoes = '{{URL::route('getusersinscritosgestorn',['id_evento'=>$evento->id])}}';

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        let locaisrecolha = [{id: 0, text: 'EVT'},
                @if( isset($locaisrecolha))
                @foreach($locaisrecolha as $lr)
            {
                id: {{ $lr->id }}, text: '{{ $lr->local_nome }}'
            },
            @endforeach
            @endif];


        function showChanged() {

            this.dados = w2ui['grid_passaros'].getChanges();

            $.ajax({
                type: 'POST',
                url: '<?php echo LaravelLocalization::getLocalizedURL(LaravelLocalization::getCurrentLocale(), url('ajax_gravar_passaros')); ?>',
                data: event.changes,
                contentType: 'application/json; charset=utf-8',
                async: false,
                success: function (response) {

                    //console.log("regressei com " + response);
                    swal("{{ trans('general.alteracao_gravada') }}", "{{ trans('general.alteracao_gravada_sucesso') }}", "success");
                },
                error: function (response) {
                    if (response.status == 422) {
                        $('#erros').empty();
                        $('#erros').append("<div class=\"note note-danger\" id=\"errosi\"></div>");
                        $.each(response.responseJSON, function (key, value) {
                            $('#errosi').append("<p>" + value + "</p>");
                            //console.log(key + " - " + value);
                        });
                        //console.log(response.responseJSON);
                    }
                },
                statusCode: {
                    422: function () {

                    }
                }
            }).fail(function (response) {
                console.log("fail");
            }).done(function (data) {

                //console.log("done");
            });
        }

        function refreshInscricoes(url = null, reload = true) {

            if (url == null) {
                grid_inscricoes.url = "{{URL::route('getusersinscritosgestorn',['id_evento'=>$evento->id])}}";

            } else {
                grid_inscricoes.url = url;
            }

            //grid_inscricoes.load("{{URL::route('ajaxusersinscritos',['id_evento'=>$evento->id])}}");
            if (reload) {
                grid_inscricoes.reload();
            }
            grid_inscricoes.select(0);


            //grid_inscricoes.load(url_inscricoes);
            //grid_inscricoes.select(0);

        }

        function refreshPassaros(inscricao_id) {

            grid_passaros.url = '<?php echo LaravelLocalization::getLocalizedURL(LaravelLocalization::getCurrentLocale(), url('ajax_passaros')); ?>/' + inscricao_id;


            grid_passaros.reload();


        }


        var gg;
        var grid_inscricoes;
        var grid_passaros;


        $(document).ready(function () {

            $('input:radio[name="filtro"]').change(function () {
                console.log("all");
                if ($(this).val() != 'all') {
                    url_inscricoes = url_inscricoes_base + '/' + $(this).val();
                } else {
                    url_inscricoes = url_inscricoes_base;
                }

                refreshInscricoes(url_inscricoes);
            });


            $('#ficha_inscricao').click(function () {
                let et = grid_inscricoes.getSelection();
                let etp = et[0];

                if (!etp) {
                    swal("Erro", "Tem de seleccionar um criador inscrito", "warning");

                } else {
                    let evento_id = {!! $evento->id !!};

                    var win = window.open('<?php echo url('pdf_ficha_inscricao'); ?>/' + evento_id + '/' + etp, '_blank');
                    if (win) {
                        //Browser has allowed it to be opened
                        win.focus();
                    } else {
                        //Browser has blocked it
                        alert('Please allow popups for this website');
                    }
                }


            });

            $('#etiquetas_provisorias').click(function () {
                let et = grid_inscricoes.getSelection();
                let etp = et[0];

                if (!etp) {
                    swal("Erro", "Tem de seleccionar um criador inscrito", "warning");

                } else {
                    let evento_id = {!! $evento->id !!};

                    var win = window.open('<?php echo url('pdf_etiquetas_provisorias'); ?>/' + evento_id + '/' + etp, '_blank');
                    if (win) {
                        //Browser has allowed it to be opened
                        win.focus();
                    } else {
                        //Browser has blocked it
                        alert('Please allow popups for this website');
                    }
                }


            });


            $('#edit_classe_passaro').on('shown.bs.modal', function (e) {
                $('.listaseccoespa').selectpicker({
                    size: 10
                });
                let pp = grid_passaros.getSelection();
                //console.log(pp);
                selectClass(grid_passaros.get(pp)[0]['rescid']);
                //console.log(grid_passaros.get(pp)[0]);

            });


            $('#listaseccoes').selectpicker({
                size: 10
            });

            $('#listaseccoesp').selectpicker({
                size: 10
            });

            $('#selclasse').selectpicker({
                size: 10
            });


            $("#form_inscrever_passaro").submit(function (e) {
                if ($("#listaseccoesp").val() == '') {
                    alert("{{ trans('forms.erro_escolher_seccao_classe') }}");
                    return false;
                }

                if (grid_inscricoes.getSelection().length == 0) {
                    alert("{{ trans('forms.erro_falta_criador') }}");
                    return false;
                }

                var data = $(this).serialize();
                var inscricaoid = grid_inscricoes.getSelection()[0];
                $("#form_inscrever_passaro input[name=anilha]").val("");
                data = "inscricaoid=" + inscricaoid + "&" + data;
                $.ajax({
                    type: "POST",
                    url: "{{URL::route('gravarinscricaopassaroevento')}}",
                    data: data,
                    success: function (e) {
                        refreshPassaros(grid_inscricoes.getSelection()[0]);
                    }
                });
                return false;
            });


            $("#listaseccoes").change(function () {
                $("#selclasse").selectpicker('val', '');
            });

            $("#selclasse").change(function () {
                $("#listaseccoes").selectpicker('val', '');
            });

            w2utils.settings.dataType = 'RESTFULL';
            //w2utils.locale('https://raw.githubusercontent.com/vitmalina/w2ui/master/src/locale/pt-br.json');
            $(".date-picker").datepicker();

            /*
                        $("button[name^='femodal']").click(function () {

                            $("#femodal").modal('show');
                        });
            */

            /*
            JAVASCRIPT RELACIONADO COM INSCRIÇÕES
             */

            grid_inscricoes = $('#grid_inscricoes').w2grid({
                name: 'grid_inscricoes',
                header: 'Inscrições',
                recid: "id",
                show: {
                    toolbar: true,
                    footer: false,
                    toolbarEdit: false,
                    toolbarSave: true,
                    toolbarDelete: true,
                },
                toolbar: {
                    items: [
                        {type: 'break'},
                        {
                            type: 'button',
                            id: 'change_state',
                            caption: 'Alterar estado',
                            img: 'w2ui-icon-pencil',
                            disabled: true
                        }
                    ],
                    onClick: function (target, event) {


                        event.onComplete = function () {
                            if (target === 'change_state') {
                                console.log("alterar estado");
                                let inscricaoid = grid_inscricoes.getSelection()[0];
                                console.log(inscricaoid);

                                data = "inscricaoid=" + inscricaoid;
                                $.ajax({
                                    type: "POST",
                                    url: "{{URL::route('ajaxalterarestadoinscricaocriadorgestorn')}}",
                                    data: data,
                                    success: function (e) {
                                        refreshInscricoes();
                                    }
                                });

                            }


                        }
                    }
                },
                multiSearch: true,
                multiSelect: false,
                searches: [
                    {field: 'stam', caption: 'STAM ', type: 'text'},
                    {field: 'name', caption: '{{trans("general.nome")}}', type: 'text'},
                ],
                columns: [
                    {field: 'id', caption: 'ID', size: '50px', hidden: true},
                    {field: 'stam', caption: 'STAM', size: '50px', sortable: true},
                    {field: 'name', caption: '{{trans("general.nome")}}', size: '30%', sortable: true},
                    {field: 'nump', caption: '# {{trans("general.birds")}}', size: '70px', sortable: true},
                    {field: 'estado', caption: '{{trans("general.estado")}}', size: '50px', sortable: true},
                    {
                        field: 'local_recolha_id', caption: '{{trans("general.local_recolha")}}', size: '100px', sortable: true, resizable: true,
                        editable: {type: 'select', items: locaisrecolha},
                        render: function (record, index, col_index) {
                            var html = '';
                            //debugger;
                            for (var p in locaisrecolha) {

                                if (locaisrecolha[p].id == this.getCellValue(index, col_index)) html = locaisrecolha[p].text;
                            }
                            return html;
                        }
                    },
                    {
                        field: 'num_jantares',
                        caption: '{{trans("general.jantares")}}',
                        size: '60px',
                        sortable: false,
                        editable: {type: 'text'}
                    },
                    {
                        field: 'tipo_caixa', caption: 'C. Fed.', size: '70px', style: 'text-align: center',
                        editable: {type: 'checkbox', style: 'text-align: center'}
                    },
                    {field: 'prioridade_devolucao', caption: 'Prioridade', size: '80px', sortable: false},

                ],
                onEdit: function (event) {
                    alert("funcionalidade ainda não implementada.")


                },
                onLoad: function (event) {
                    this.toolbar.disable('change_state');
                    var response = [];
                    var responseText = event.xhr.responseText;
                    response = responseText.match(/{.*(.*)\}/);
                    if (response != null) {
                        var finale = response[0];
                        var arrayFinale = JSON.parse(finale);

                        var totais = arrayFinale.totais;
                        console.log(totais["total_inscritos"]);
                        console.log(totais);

                        $("#tinscritos").text(totais["total_inscritos"]);
                        $("#tinscritosa").text(totais["total_inscritos_a"]);
                        $("#tinscritosp").text(totais["total_inscritos_p"]);
                        $("#tpassaros").text(totais["total_passaros"]);

                        $(totais).each(function (event) {


                        });
                    }

                },
                onReload: function (event) {

                    refreshInscricoes();

                },
                onSelect: function (event) {

                    this.toolbar.enable('change_state');
                    refreshPassaros(event.recid);
                },
                onClick: function (event) {


                },
                onUnselect: function (event) {
                    this.toolbar.disable('change_state');
                    //this.toolbar.disable('deleteInscPassaro');
                },
                onSearch: function (target, data) {
                    for (var s in data.searchData) {
                        data.searchData[s].operator = 'contains';
                    }
                    refreshInscricoes(null, true);
                },
                onSave: function (event) {
                    grid_inscricoes.url = "{{URL::route('getusersinscritosgestorn',['id_evento'=>$evento->id])}}";
                    swal("Alterações gravadas", "As sua alterações foram gravadas com sucesso", "success");
                    //refreshPassaros(grid_inscricoes.getSelection())
                    //console.log('save'+grid_inscricoes.getSelection());
                },
                onDelete: function (event) {
                    event.preventDefault();

                    swal({
                            title: "Posso remover a inscrição deste criador?",
                            text: "A eliminação desta inscrição vai remover todos os pássaros já inscritos deste criador",
                            type: "warning",
                            showCancelButton: true,
                            showConfirmButton: true,
                            cancelButtonText: "Cancelar",
                            confirmButtonClass:
                                'btn-danger',
                            confirmButtonText:
                                'Sim, apagar.',
                            closeOnConfirm:
                                false,
                            //closeOnCancel: false
                        },

                        function () {

                            var insccriadorid = grid_inscricoes.getSelection()[0];
                            var url = '<?php echo url('ajax_remover_inscricao_criador_admin') ?>/' + {!! $evento->id !!} + '/' + insccriadorid;
                            $.ajax({
                                type: "GET",
                                url: url,
                                success: function (e) {
                                    swal("Apagado", "Apaguei a inscrição que pretendia", "success");
                                    refreshInscricoes();


                                }
                            });

                        });


                },


            });
            grid_inscricoes.onSearch = function (target, info) {
                delete grid_inscricoes.url;
                grid_inscricoes.load("{{URL::route('getusersinscritosgestorn',['id_evento'=>$evento->id])}}");
                //console.log("antes de search");

            };
            grid_passaros = $('#grid_passaros').w2grid({
                name: 'grid_passaros',
                header: 'Pássaros',
                recid: "id",
                show: {
                    toolbar: true,
                    footer: false,
                    toolbarEdit: false,
                    toolbarSave: true,
                    toolbarDelete: true,
                    toolbarSearch: false,
                    toolbarInput: false,
                },
                toolbar: {
                    items: [
                        {type: 'break'},
                        {
                            type: 'button',
                            id: 'edit_birdclass',
                            caption: 'Alterar classe equipa/pássaro',
                            img: 'w2ui-icon-pencil',
                            disabled: true
                        }
                    ],
                    onClick: function (target, event) {


                        event.onComplete = function () {
                            if (target === 'edit_birdclass') {
                                let p = grid_passaros.getSelection();
                                let idp = p[0];
                                let evento_id = {!! $evento->id !!};
                                $('#modaleditclasse').load('<?php echo url('editclassepassaroajax'); ?>/' + evento_id + '/' + idp, function () {
                                    $('#edit_classe_passaro').modal({show: true});
                                });

                            }


                        }
                    }
                },
                multiSearch: false,
                multiSelect: false,
                columns: [
                    {field: 'id', caption: 'ID', size: '50px', hidden: true},
                    {field: 'seccao', caption: '{{trans("general.seccao")}}', size: '40px', sortable: true},
                    {field: 'classe', caption: '{{trans("general.classe")}}', size: '40px', sortable: false},
                    {field: 'nome', caption: 'Nome', size: '30%', sortable: true},
                    {field: 'numgaiola', caption: '# {{trans("general.gaiola")}}', size: '60px', sortable: true},
                    {field: 'anilha', caption: '{{trans("general.anilha")}}', size: '50px', editable: {type: 'text'}},
                    {field: 'ano', caption: '{{trans("general.ano")}}', size: '50px', editable: {type: 'text'}},
                    {
                        field: 'preco',
                        caption: '{{trans("general.preco")}}',
                        size: '60px',
                        sortable: false,
                        editable: {type: 'text'}
                    },
                    {
                        field: 'vendido',
                        caption: 'Vendido',
                        size: '70px',
                        sortable: false,
                        editable: {type: 'checkbox', style: 'text-align: center'}
                    },
                    {
                        field: 'gaiola_propria',
                        caption: '{{trans("general.gaiola_propria")}}',
                        size: '50px',
                        sortable: false,
                        editable: {type: 'checkbox', style: 'text-align: center'}
                    },
                    {
                        field: 'ausente',
                        caption: 'Ausente',
                        size: '80px',
                        sortable: false,
                        editable: {type: 'checkbox', style: 'text-align: center'}
                    },

                ],
                onEdit: function (event) {
                    alert("funcionalidade ainda não implementada.")
                },
                onLoad: function (event) {

                },
                onSelect: function (event) {

                    this.toolbar.enable('edit_birdclass');

                    //this.toolbar.enable('deleteInscPassaro');
                },
                onUnselect: function (event) {
                    this.toolbar.disable('edit_birdclass');
                    //this.toolbar.disable('deleteInscPassaro');
                },
                onSubmit: function (event) {

                },
                onDelete: function (event) {
                    event.preventDefault();
                    if (confirm("Posso remover este pássaro/equipa?")) {
                        var inscpassaroid = grid_passaros.getSelection()[0];
                        var url = '<?php echo url('ajax_remover_inscricao_passaro') ?>/' + {{ $evento->id }} + '/' + inscpassaroid;
                        $.ajax({
                            type: "GET",
                            url: url,
                            success: function (e) {
                                swal("Apagado", "Apaguei o pássaro/equipa que pretendia", "success");
                                refreshPassaros(grid_inscricoes.getSelection()[0]);
                            }
                        });

                    } else {
                        return false;
                    }

                },
                onSave: function (event) {

                    swal("Alterações gravadas", "As sua alterações foram gravadas com sucesso", "success");
                    //refreshPassaros(grid_inscricoes.getSelection())
                    //console.log('save'+grid_inscricoes.getSelection());

                },
                onChange: function (event) {

                    //this.reload();
                    //console.log('change');

                },
                onRefresh: function (e) {
                    let totalp = this.total;
                    $("#total_passaros").text(totalp);

                }


            });


            /*
            FIM JAVASCRIPT RELACIONADO COM INSCRIÇÕES
             */


        });

    </script>

@endsection