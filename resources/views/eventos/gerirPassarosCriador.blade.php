@extends('layouts.app')


@section('titulo','Inscrição de Pássaros')
@section('subtitulo',$evento->nome)

@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="{{URL::route('homecriador')}}">{{trans ('clubes.inicio')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="{!! route('eventohomecriador',[$evento->id]) !!}">{{ $evento->nome }}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{trans ('general.inscricao_passaro')}}</span>
        </li>
    </ul>


@endsection

@section('content')
    <div class="row">
        <div class="col-sm-12">

            <div class="portlet box blue">
                <div class="portlet-title">
                    <div class="caption">
                        <i class=" icon-layers font-green"></i>
                        <span class="caption-subject bold uppercase">{{ trans('eventos.inscrever') }}</span>
                    </div>

                </div>

                <div class="portlet-body">
                    <form id="form_inscrever_passaro" method="post">
                        <input type="hidden" name="eventoid" value="{{$evento->id}}">
                        <input type="hidden" name="rdt" value="ajax">
                        <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                        <table class="table table-condensed">
                            <tr>
                                <td colspan="8"><strong>{{trans('general.seccao_classe')}}</strong></td>

                            </tr>
                            <tr>
                                <td colspan="8">
                                    <select name="seccaoclasseid" id="listaseccoesp" class="col-sm-12 selectpicker"
                                            data-live-search="true">
                                        @foreach($classes as $ci)
                                            <option value="{{ $ci->id }}">{{$ci->seccao}}.{{$ci->classe}}
                                                - {{$ci->nome}}</option>
                                        @endforeach
                                    </select></td>
                                </td>

                            </tr>
                            <tr>
                                <td><strong>{{trans('general.preco')}}</strong></td>
                                <td><strong>{{trans('general.anilha')}}</strong></td>
                                <td><strong>{{trans('general.ano')}}</strong></td>
                                <td><strong>{{trans('general.vendido')}}</strong></td>
                                <td><strong>{{trans('general.ausente')}}</strong></td>
                                <td><strong>{{trans('general.gaiola_propria')}}</strong></td>
                                <td><strong>Obs</strong></td>

                            </tr>
                            <tr>

                                <td><input type="text" size="10" class="form-control" name="preco" placeholder="Preço">
                                </td>
                                <td><input type="text" size="10" class="form-control" name="anilha"
                                           placeholder="Anilha"></td>
                                <td><input type="text" size="10" class="form-control" name="ano" placeholder="Ano"
                                           value="<?php echo date('Y'); ?>"></td>
                                <td><select class="form-control" name="vendido" id="vendido">
                                        <option selected value="0">{{trans('general.nao')}}</option>
                                        <option value="1">{{trans('general.sim')}}</option>
                                    </select></td>
                                <td><select class="form-control" name="ausente" id="ausente">

                                        <option selected value="0">{{trans('general.nao')}}</option>
                                        <option value="1">{{trans('general.sim')}}</option>
                                    </select></td>
                                <td><select class="form-control" name="gaiola_propria" id="gaiola_propria">
                                        <option selected value="0">{{trans('general.nao')}}</option>
                                        <option value="1">{{trans('general.sim')}}</option>
                                    </select></td>
                                <td><input type="text" size="10" class="form-control" name="observacao"
                                           placeholder="Observação"></td>

                            </tr>

                            <tr>
                                <td colspan="6">

                                </td>
                                <td>
                                    <strong>{{trans('general.multiplicador')}}</strong> <i class="fa fa-question-circle"
                                                                                           title="Este valor multiplica a criação de pássaro ou equipa."></i>
                                </td>

                            </tr>
                            <tr>
                                <td colspan="5"></td>

                                <td>
                                    <input type="submit" class="btn btn-info"
                                           value="{{trans('general.adicionar_passaro')}}">

                                </td>
                                <td>
                                    <input type="text" size="2" class="form-control" value="1" name="multiplicador">
                                </td>
                            </tr>

                        </table>
                    </form>
                    {{--<button id="adicionar_bt" type="button" class="btn btn-primary">{{trans('general.adicionar_passaro')}}</button>--}}


                </div>


            </div>

            <div class="portlet box blue">
                <div class="portlet-title">
                    <div class="caption">
                        <i class=" icon-layers font-green"></i>
                        <span class="caption-subject bold uppercase">{{ trans('eventos.passaros_inscritos') }}</span><span> ({{ trans('general.total') }}
                            : <span id="total_passaros">{{$num_passaros}}</span>)</span>
                    </div>

                </div>

                <div class="portlet-body">

                    <div id="grid_passaros_criador" style="height: 500px;"></div>


                </div>


            </div>


        </div>
    </div>



@endsection


@section('scripts')
    <!-- BEGIN PAGE LEVEL SCRIPTS -->

    <script src="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.min.js')}}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/pages/scripts/ui-sweetalert.min.js')}}" type="text/javascript"></script>



    <script>

        var grid_passaros_criador;
        var inscricaoid = {{ $inscricao->id }};
        let evento_id = {{ $evento->id }};

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function refreshPassaros() {

            grid_passaros_criador.url = '<?php echo LaravelLocalization::getLocalizedURL(LaravelLocalization::getCurrentLocale(), url('ajax_passaros')); ?>/' + inscricaoid;

            grid_passaros_criador.reload();

        }

        $(document).ready(function () {

            w2utils.settings.dataType = 'RESTFULL';

            $("#form_inscrever_passaro").submit(function (e) {
                if ($("#listaseccoesp").val() == '') {

                    alert("{{ trans('forms.erro_escolher_seccao_classe') }}");
                    return false;
                }

                var data = $(this).serialize();

                data = "inscricaoid=" + inscricaoid + "&" + data;
                $.ajax({
                    type: "POST",
                    url: "{{URL::route('gravarinscricaopassaroevento')}}",
                    data: data,
                    success: function (e) {

                        refreshPassaros();
                    }
                });
                return false;
            });


            grid_passaros_criador = $('#grid_passaros_criador').w2grid({
                name: 'grid_passaros_criador',
                header: 'Pássaros',
                recid: "id",
                show: {
                    toolbar: true,
                    footer: false,
                    toolbarEdit: false,
                    toolbarSave: true,
                    toolbarDelete: true
                },
                multiSearch: true,
                multiSelect: false,
                searches: [
                    {field: 'nome', caption: 'Secção/Classe', type: 'text'},
                ],
                columns: [
                    {field: 'id', caption: 'ID', size: '50px', hidden: true},
                    {field: 'seccao', caption: 'Secção', size: '60px', sortable: true},
                    {field: 'classe', caption: 'Classe', size: '60px', sortable: true},

                    {field: 'nome', caption: 'Nome', size: '30%', sortable: true},
                    {field: 'numgaiola', caption: '# gaiola', size: '60px', sortable: true},
                    {field: 'anilha', caption: 'Anilha', size: '50px', editable: {type: 'text'}},
                    {field: 'ano', caption: 'Ano', size: '50px', editable: {type: 'text'}},
                    {field: 'preco', caption: 'Preço', size: '60px', sortable: false, editable: {type: 'text'}},
                    {field: 'vendido', caption: 'Vendido', size: '70px', sortable: false},
                    {field: 'gaiola_propria', caption: 'Gaiola Própria', size: '50px', sortable: false},
                    {field: 'ausente', caption: 'Ausente', size: '80px', sortable: false},

                ],
                onEdit: function (event) {
                    alert("funcionalidade ainda não implementada.")
                },
                onLoad: function (event) {

                },
                onSelect: function (event) {
                    this.toolbar.enable('deleteInscPassaro');
                },
                onUnselect: function (event) {
                    this.toolbar.disable('deleteInscPassaro');
                },
                onSubmit: function (event) {

                },
                onDelete: function (event) {
                    event.preventDefault();
                    if (confirm("Posso remover este pássaro/equipa?")) {
                        var inscpassaroid = grid_passaros_criador.getSelection()[0];
                        var url = '<?php echo url('ajax_remover_inscricao_passaro_criador') ?>/'
                            + evento_id + '/'
                            + inscpassaroid;
                        $.ajax({
                            type: "GET",
                            url: url,
                            success: function (e) {
                                if(e != 0){
                                    alert("pássaro/equipa removido(s) com sucesso");
                                }else{
                                    alert("Problema ao remover pássaro/equipa. Contacte o seu administrador.");
                                }
                                refreshPassaros();
                            }
                        });

                    } else {
                        return false;
                    }

                },
                onSave: function (event) {

                    swal("Alterações gravadas", "As sua alterações foram gravadas com sucesso", "success");
                    //refreshPassaros(grid_inscricoes.getSelection())
                    //console.log('save'+grid_inscricoes.getSelection());

                },
                onChange: function (event) {


                    //this.reload();
                    //console.log('change');

                },
                onRefresh: function (e){
                    let totalp = this.total;
                    $("#total_passaros").text(totalp);
                }


            });

            refreshPassaros();

        });


    </script>

@endsection
