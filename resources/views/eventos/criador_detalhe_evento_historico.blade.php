@extends('layouts.app')


@section('titulo','Histórico de Evento - Criador')
@section('subtitulo',$evento->nome)

@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> {{trans ('clubes.inicio')}}</a></li>
        <li class="active">{{trans ('eventos.evento')}}</li>
    </ol>
@endsection
@section('content')
    <section id="evento">

        <div class="row">

            @include("eventos.evento_view")

        </div>
        @if($num_passaros==0)
            <div class="row">
                <div class="col-sm-12">
                    <div class="callout callout-danger">

                        <p>{{trans ('general.nao_inscreveu_passaros_neste_evento')}}.</p>
                    </div>

                </div>

            </div>
        @endif
        <div class="row">
            @if($mostra_fichas_julgamento)
                @include("eventos.listagens_historico_criador_inc")
            @endif
        </div>
    </section>
@endsection