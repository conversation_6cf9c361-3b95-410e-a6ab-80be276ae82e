@extends('layouts.app')

@section('titulo','Ferramentas')
@section('subtitulo','')
@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casaadmin'); ?>"><PERSON><PERSON><PERSON></a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>Gestão de Utilizadores</span>
        </li>
    </ul>

@endsection

@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>Criadores
                        </div>

                    </div>
                    <div class="portlet-body">
                        @if(Session::has('flash_message'))
                            <div class="alert alert-success">

                                {{Session::get('flash_message')}}
                                <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;
                                </button>
                            </div>
                        @endif
                        <div style="text-align: left;margin-top: 10px;margin-bottom: 15px;" class="form-group">
                            <a href="{!! route('adminusercreate') !!}" class="btn btn-primary">Novo Criador</a>
                        </div>
                        {!! Form::open(array('url' => 'users/list', 'id'=>'pesquisaf', 'method'=>'get')) !!}


                        <div class="form-group col-xm-12 col-md-2">

                            {!! Form::label('estado','Estado:') !!}<br/>
                            {!! Form::select('estado', $estados, 0, ['class' => 'form-control']) !!}

                        </div>
                        <div style="vertical-align: bottom" class="form-group col-xm-12 col-md-6">
                            {!! Form::label('pais_id','País:') !!}<br/>
                            <select name="pais_id" id="pais_id" data-userpaisid="{{ $loggedInUserPaisId }}" class="col-sm-6 selectpicker"
                                    data-live-search="true" title="--">
                                @foreach($paises as $p)
                                    <option value="{{ $p->id }}">{{$p->pais}}</option>
                                @endforeach
                            </select>

                        </div>
                        {{--<div class="form-group col-xm-12 col-md-5">

                            {!! Form::label('clube_id','Clubes e Associações:') !!}<br/>
                            {!! Form::select('clube_id', $clubes, 0, ['class' => 'form-control']) !!}

                        </div>--}}
                        <div style="text-align: right;margin-top: 24px;"
                             class="form-group col-xm-12 col-md-1">
                            <button class="btn btn-default" id="reset_btn">Reset</button>
                        </div>
                        <div style="text-align: right;margin-top: 24px;"
                             class="form-group col-xm-12 col-md-1">
                            {!! Form::submit('Filtrar',['class'=>'btn btn-primary']) !!}
                        </div>
                        {!! Form::close() !!}

                        <div id="grid2" style="width: 100%; height: 500px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">


                <div class="portlet light">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-upload"></i>
                            <span class="caption-subject bold font-yellow-crusta uppercase">{{trans('general.importar')}}</span>
                            <span class="caption-helper">{{trans('general.utilizadores')}}</span>
                        </div>

                    </div>
                    <div class="portlet-body">
                        <div class="row">
                            <div class="col-md-12">
                                <form action="{{ route('importusers') }}"
                                      class="form-horizontal" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">

                                    <div class="form-group col-xm-12 col-md-2">

                                        <input type="file" name="import_file"/><br>
                                        <button class="btn btn-primary">{{trans('general.importar_utilizadores')}}</button>
                                    </div>


                                </form>
                                <br>
                                @if(Session::has('flash_message_success'))
                                    <div class="alert alert-success col-sm-12">

                                        {{Session::get('flash_message_success')}}
                                        <button class="close" type="button" data-dismiss="alert" aria-hidden="true">
                                            &times;
                                        </button>
                                    </div>
                                @endif
                                @if(Session::has('flash_message_import_error'))
                                    <div class="alert alert-danger col-sm-12">

                                        {{Session::get('flash_message_import_error')}}
                                        <button class="close" type="button" data-dismiss="alert" aria-hidden="true">
                                            &times;
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>


                </div>


            </div>


        </div>


    </div>



@endsection

@section('scripts')
    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <!-- END PAGE LEVEL SCRIPTS -->
    <script>

        var gg;
        $(document).ready(function () {

            $('#pais_id').selectpicker({
                size: 10
            });

            //var loggedin_user_pais_id = {{Session::get('loggedin_user_pais_id')}};
            var loggedin_user_pais_id = $('#pais_id').data('userpaisid');
            var user_estado_id = 0;
            var pais_id = 0;

            if (sessionStorage.getItem('list_users_country_filter') != null) {
                pais_id = sessionStorage.getItem('list_users_country_filter');
            }else{
                pais_id = loggedin_user_pais_id;
            }

            $('#pais_id').val(pais_id);
            $('#pais_id').selectpicker('refresh');

            if (sessionStorage.getItem('list_users_state_filter') != null) {
                user_estado_id =sessionStorage.getItem('list_users_state_filter');
                    $('#estado').val(user_estado_id);
            }
            var initialLink = '<?php echo url('ajax_users_admin') ?>/' + user_estado_id + '/' + pais_id;

            w2utils.settings.dataType = 'RESTFULL';
            w2utils.locale('https://raw.githubusercontent.com/vitmalina/w2ui/master/src/locale/pt-br.json')

            gg = $('#grid2').w2grid({
                name: 'grid2',
                header: 'Criadores',
                multiSelect: false,
                multiSearch: true,
                recid: "id",
                show: {
                    toolbarSearch: false,     // hides search button on the toolbar
                    toolbarInput: true,     // hides search input on the toolbar
                    toolbar: true,
                    footer: true,
                    toolbarEdit: true,
                },
                toolbar: {
                    items: [
                        {
                            type: 'button',
                            id: 'editPerm',
                            caption: 'Editar Permissões',
                            img: 'w2ui-icon-plus',
                            disabled: true
                        },

                    ],
                    onClick: function (event) {
                        event.onComplete = function () {
                            //console.log(event);
                            if (event.target == 'editPerm') {
                                var sel = this.owner.getSelection('recid');

                                var link = '<?php echo url('users') ?>/' + this.owner.getSelection('recid') + '/editperm';
                                window.open(link, "_self");
                            }
                        }

                    }
                },
                searches: [
                    {field: 'stam', caption: 'stam ', type: 'text'},
                    {field: 'name', caption: 'Nome', type: 'text'},
                    {field: 'email', caption: 'E-mail', type: 'text'},
                ],
                columns: [
                    {field: 'id', caption: 'ID', size: '50px', hidden: true},
                    {field: 'pais_id', caption: 'pais_id', size: '50px', hidden: true},
                    {field: 'user_estado_id', caption: 'estado_id', size: '50px', hidden: true},
                    {field: 'stam', caption: 'STAM', size: '30%', sortable: true},
                    {field: 'estado', caption: 'Estado', size: '30%'},
                    {field: 'name', caption: 'Nome', size: '50%', sortable: true},
                    {field: 'email', caption: 'E-mail', size: '30%'},
                    {field: 'pais', caption: 'País', size: '30%'},
                ],
                onEdit: function (event) {
                    var link = '<?php echo url('users') ?>/' + event['recid'] + '/edit/adminuserslist';
                    window.open(link, "_self");
                },
                onRefresh: function (event) {

                    this.toolbar.disable('editPerm');

                },
                onSelect: function (event) {

                    this.toolbar.enable('editPerm');

                },
                onUnselect: function (event) {

                    var sel = this.getSelection();
                    //console.log(sel);
                    if (sel.length == 1) this.toolbar.disable('editPerm');
                },
                onSearch: function (target, data) {
                    for (var s in data.searchData) {
                        data.searchData[s].operator = 'contains';
                    }
                },

            });

            gg.load(initialLink);

            $("#reset_btn").click(function (e) {
                e.preventDefault();
                $("#estado").val('0');
                $("#pais_id").val('0');
                $('#pais_id').selectpicker('refresh');
                $('#estado').selectpicker('refresh');

                // clear session vars for the filters

                sessionStorage.removeItem('list_users_state_filter');
                sessionStorage.removeItem('list_users_country_filter');

                gg.load('{{URL::route('ajaxusersadmin')}}');

            });

            $("#pesquisaf").submit(function (e) {
                e.preventDefault();
                var dados = $(this).serializeArray();
                console.log(dados);

                var pesquisa = [];

                var indice = 0;

                var user_estado_id = 0;
                var pais_id = 0;

                if (dados[0].value != 0) {
                    user_estado_id = dados[0].value;
                    pesquisa[indice] = {field: 'user_estado_id', value: user_estado_id, operator: 'is'};
                    indice++;
                    sessionStorage.setItem('list_users_state_filter', user_estado_id);

                }
                if (dados[1].value != 0) {
                    pais_id = dados[1].value;
                    pesquisa[indice] = {field: 'pais_id', value: pais_id, operator: 'is'};
                    indice++;
                    sessionStorage.setItem('list_users_country_filter', pais_id);

                }

                // verificar se apenas a pesquisa livre está activa

                var linka = '<?php echo url('ajax_users_admin') ?>/' + user_estado_id + '/' + pais_id;


                //console.log(linka);
                gg.load(linka);


// TODO : ver esta tralha

                //console.log(pesquisa);

                //gg.search([{field: 'name', value: 'luis', operator: 'contains'}], 'AND');
                //gg.search([ {field: 'estado_id', value: '2', operator: 'contains'}],'AND');
                //gg.search([{field: 'pais_id', value: '13', operator: 'contains'}], 'AND');
                //gg.search(pesquisa, 'OR')


                //para pesquisa multiplas:
                //gg.search([ {field: 'name', value: 'aaa', operator: 'contains'}],'OR');


            });


        });

    </script>

@endsection