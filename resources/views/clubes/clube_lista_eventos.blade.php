@extends('layouts.app')

@section('titulo','Eventos geridos pelo Clube')
@section('subtitulo',$clube->nome)

@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casa'); ?>">{{trans('clubes.inicio')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{trans('eventos.evento')}}</span>
        </li>
    </ul>

@endsection

@section('content')
    <div class="portlet box blue">
        <div class="portlet-title">
            <div class="caption">
                <i class="fa fa-gift"></i>{{trans('general.eventos_activos')}}
            </div>

        </div>
        <div class="portlet-body">
            <div class="row">
                @if (count($eventos_activos) > 0)
                    @foreach ($eventos_activos as $evt)

                        <div class="table-text col-md-10 col-sm-12 col-xs-12">
                            <div><h4>{{ $evt->nome }}</h4></div>

                            <div><strong>{{trans('forms.data_inicio')}}:</strong> {{ $evt->data_inicio }}</div>
                            <div><strong>{{trans('forms.data_fim')}}:</strong> {{ $evt->data_fim }}</div>
                            <div style="margin-top: 10px;">{{trans('general.inscricoes')}}
                                <strong>{{ $evt->data_inicio_inscricoes }}</strong> -
                                <strong>{{ $evt->data_fim_inscricoes }}</strong></div>
                        </div>

                        <div class="col-md-2 col-sm-12 col-xs-12">


                            {{--<a href="{!! route('eventohome',[$evt->id]) !!}" class="btn btn-primary">{{trans('clubes.gerir')}}</a>--}}
                            <a href="{!! route('eventohome',[$evt->id]) !!}"
                               class="btn btn-primary">{{trans('clubes.gerir')}}</a>


                        </div>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <hr>
                        </div>
                    @endforeach
                @else

                    {{trans('general.nenhum_evento')}}

                @endif


            </div>

        </div>

    </div>
    <div class="portlet box blue">
        <div class="portlet-title">
            <div class="caption">
                <i class="fa fa-gift"></i>Ferramentas
            </div>

        </div>
        <div class="portlet-body">
            <div class="row">
                <div class="table-text col-md-8 col-sm-12 col-xs-12">
                    <br>
                    {{trans('clubes.eventos_submetidos')}}
                    <br>
                    <br>
                </div>

                <div class="col-md-4 col-sm-12 col-xs-12">

                    <a href="" class="btn btn-primary">{{trans('clubes.proposta_evento')}}</a>

                </div>

            </div>

        </div>

    </div>











@endsection