@extends('layouts.app')


@section('titulo','Inscrição de criadores em Evento')
@section('subtitulo',$evento->nome)

@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> {{trans('clubes.inicio')}}</a></li>
        <li><a href="<?php echo route('eventohome',[$evento->id]); ?>">{{trans('eventos.evento')}}</a></li>
        <li class="active">{{trans('general.inscricoes')}}</li>
    </ol>
@endsection
@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-12 col-xs-12">
                <h3>{{$evento->nome}}</h3>
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans('general.criadores')}}</h3>
                    </div>
                    <div class="box-body">

                        <table class="table table-striped table-bordered" cellspacing="0" id="dtable">
                            <thead>
                            <tr>
                                <th>STAM</th>
                                <th>Nome</th>
                                <th>Email</th>
                                <th>menu</th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>

                            </tbody>
                        </table>


                    </div>
                </div>
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans('general.inscritos')}}</h3>
                        <a class="btn btn-success pull-right" href="{!! route('clube_evento_inscrever_passaros',[$evento->id]) !!}"><i class="glyphicon glyphicon-plane"></i> -> {{trans('general.inscricao_passaros')}}</a>
                    </div>
                    <div class="box-body">
                        <table class="table table-striped table-bordered" cellspacing="0" id="itable">
                            <thead>
                            <tr>
                                <th>STAM</th>
                                <th>Nome</th>
                                <th>Email</th>
                                <th>menu</th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>

                            </tbody>
                        </table>


                    </div>
                </div>


            </div>
            <div class="row">
                <div class="col-sm-3">
                    <a href="<?php echo route('eventohome', [$evento->id]); ?>">
                        <button type="button" class="btn btn-danger">{{trans('clubes.sair')}}</button>
                    </a>
                    {{--<a href="clube_evento/inscrever/">
                        <button type="button" class="btn btn-primary">{{trans('clubes.inscrever_criadores')}}</button>
                    </a>--}}
                </div>

            </div>

        </div>

    </div>


@endsection

@section('scripts')

    <script>

        $(document).ready(function () {


            var tabela = $('#dtable').DataTable({
                paging: true,
                processing: true,
                serverSide: true,
                renderer: "bootstrap",
                ajax: '{{URL::route('ajaxusers',[$evento->id,2])}}',
                columns: [
                    {data: 'stam', name: 'stam'},
                    {data: 'name', name: 'name'},
                    {data: 'email', name: 'email'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],


            });

            $('#dtable').on('draw.dt', function () {
                $(".inscrever_pop").fancybox({
                    maxWidth: 800,
                    maxHeight: 600,
                    fitToView: false,
                    width: '800px',
                    height: '150px',
                    autoSize: true,
                    closeClick: false,
                    openEffect: 'none',
                    closeEffect: 'none'
                });
            });


            var tabelai = $('#itable').DataTable({
                processing: true,
                serverSide: true,
                searching: true,
                renderer: "bootstrap",
                ajax: '{{URL::route('ajaxusersi',[$evento->id])}}',
                columns: [
                    {data: 'stam', name: 'stam'},
                    {data: 'name', name: 'name'},
                    {data: 'email', name: 'email'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],


            });

            $('#itable').on('draw.dt', function () {
                $(".remover_pop").fancybox({
                    maxWidth: 800,
                    maxHeight: 200,
                    fitToView: false,
                    width: '550px',
                    height: '150px',
                    autoSize: false,
                    closeClick: false,
                    openEffect: 'none',
                    closeEffect: 'none'
                });
            });


        });


    </script>

@endsection