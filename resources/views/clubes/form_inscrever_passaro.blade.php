<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.10.0/css/bootstrap-select.min.css">

<div class="container-fluid">
    <div class="row text-center">
        <div class="col-sm-10">
            <p class="text-center"><h4>{trans('eventos.dados_passaro_equipa')}}:</h4></p>
        </div>
    </div>

    <div class="row text-center">

        <div class="col-md-10 col-xs-10">
            <form onsubmit="return validarForm()" id="form_inscrever_passaro" action="<?php echo route('gravarinscricaocriadorevento'); ?>" method="post">

                <input type="hidden" name="clubeid" value="{{$id_clube}}">
                <input type="hidden" name="eventoid" value="{{$id_evento}}">
                <input type="hidden" name="inscricaoid" value="{{$id_inscricao}}">
                <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                <br>

                <table class="table">
                    <thead>
                    <th>{{trans('general.seccao_classe')}}</th>
                    <th>{{trans('general.preco')}}</th>
                    <th>{{trans('general.anilha')}}</th>
                    <th>{{trans('general.ano')}}</th>
                    <th></th>
                    <th></th>


                    </thead>
                    <tbody>
                    <td><select id="selclasse" class="selectpicker" data-live-search="true">
                            @foreach($classes as $ci)
                                <option value="{{ $ci->id }}">{{$ci->seccao}} {{$ci->classe}} - {{$ci->nome}}</option>
                            @endforeach
                        </select></td>
                    <td>
                        <input type="text" size="10" class="form-control" id="preco" placeholder="{{trans('general.preco')}}"></td>
                    <td>
                        <input type="text" size="10" class="form-control" id="anilha" placeholder="{{trans('general.anilha')}}"></td>
                    <td>
                        <input type="text" size="10" class="form-control" id="ano" placeholder="{{trans('general.ano')}}"></td>
                    <td><input type="submit" class="btn btn-info" value="Inscrever">
                    </td>


                    <td><a href="javascript:parent.jQuery.fancybox.close();">

                            <button type="button" class="btn btn-default">{{trans('clubes.cancelar')}}</button>
                        </a></td>
                    </tbody>


                </table>


                <br><br>

            </form>


        </div>


    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.10.0/js/bootstrap-select.min.js"></script>
<script>

    $('.selectpicker').selectpicker({});

    function validarForm() {

        var selc = $('#selclasse').val();
        alert($('#selclasse').val());
        if (selc == null) {
            alert("Name must be filled out");
            return false;
        }

        return false;
    }


    $(document).ready(function () {


    });


</script>


