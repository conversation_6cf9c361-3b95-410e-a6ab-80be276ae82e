@extends('layouts.app')


@section('titulo','Inscrição de Pássaros em Evento')
@section('subtitulo',$evento->nome)

@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> {{trans('clubes.inicio')}}</a></li>
        <li><a href="<?php echo route('eventohome', [$evento->id]); ?>"><i class="fa fa-dashboard"></i> {{trans('general.gestao_eventos')}}</a></li>
        <li class="active">{{trans('general.inscricao_passaros')}}</li>
    </ol>
@endsection
@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <h3>{{$evento->nome}}</h3>
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans('general.criadores_inscritos_no_evento')}}</h3>
                        <a class="btn btn-success pull-right" href="{!! route('clube_evento_inscrever_criadores',[$evento->id]) !!}"><i class="glyphicon glyphicon-user"></i> -> {{trans('general.inscricao_criadores')}}</a>
                    </div>
                    <div class="box-body">
                        <select id="selcriadores" class="selectpicker col-sm-6" data-live-search="true">
                            @foreach($criadores_inscritos as $ci)
                                <option @if($selidinscricao==$ci->id) selected @endif value="{{ $ci->id }}">{{$ci->name}} ({{ $ci->stam }}) - {{ $ci->num_expositor }}</option>
                            @endforeach
                        </select>
                        <button id="imprimirficha" type="button" class="btn btn-default pull-right" data-dismiss="modal"><i class="glyphicon glyphicon-print"></i> {{trans('general.ficha_inscricao')}}</button>
<button id="iepbutton" type="button" class="btn btn-default pull-right" data-dismiss="modal"><i class="glyphicon glyphicon-print"></i> {{trans('clubes.etiquetas_provisorias')}}</button>


                    </div>
                </div>

            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans('general.accoes')}}</h3>
                    </div>
                    <div class="box-body">
                        @if(Session::has('flash_message'))
                            <div class="alert alert-danger">

                                {{Session::get('flash_message')}}
                                <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;</button>
                            </div>
                        @endif
                        {{-- <button class="btn btn-primary btn-lg" data-toggle="modal" data-target="#editpassaro">Launch demo modal</button>--}}
                        <form id="form_inscrever_passaro" action="<?php echo route('gravarinscricaopassaroevento'); ?>" method="post">
                            <input type="hidden" name="eventoid" value="{{$evento->id}}">
                            <input type="hidden" name="inscricaoid" value="0">
                            <input type="hidden" name="rdt" value="1912">
                            <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                            <table class="table table-condensed">
                                <tr>
                                    <td colspan="8"><strong>{{trans('general.seccao_classe')}}</strong></td>

                                </tr>
                                <tr>
                                    <td colspan="8">
                                        <select  name="seccaoclasseid" id="selclasse" class="col-sm-12 selectpicker" data-live-search="true">
                                            @foreach($classes as $ci)
                                                <option value="{{ $ci->id }}">{{$ci->seccao}}.{{$ci->classe}} - {{$ci->nome}}</option>
                                            @endforeach
                                        </select></td>
                                    </td>

                                </tr>
                                <tr>
                                    <td><strong>{{trans('general.preco')}}</strong></td>
                                    <td><strong>{{trans('general.anilha')}}</strong></td>
                                    <td><strong>{{trans('general.ano')}}</strong></td>
                                    <td><strong>{{trans('general.vendido')}}</strong></td>
                                    <td><strong>{{trans('general.ausente')}}</strong></td>
                                    <td><strong>{{trans('general.gaiola_propria')}}</strong></td>
                                    <td><strong>{{trans('general.obs')}}</strong></td>

                                </tr>
                                <tr>

                                    <td><input type="text" size="10" class="form-control" name="preco" placeholder="{{trans('general.preco')}}"></td>
                                    <td><input type="text" size="10" class="form-control" name="anilha" placeholder="{{trans('general.anilha')}}"></td>
                                    <td><input type="text" size="10" class="form-control" name="ano" placeholder="{{trans('general.ano')}}" value="<?php echo date('Y'); ?>"></td>
                                    <td><select class="form-control" name="vendido" id="vendido">
                                            <option selected value="0">{{trans('general.nao')}}</option>
                                            <option value="1">{{trans('general.sim')}}</option>
                                        </select></td>
                                    <td><select class="form-control" name="ausente" id="ausente">

                                            <option selected value="0">{{trans('general.nao')}}</option>
                                            <option value="1">{{trans('general.sim')}}</option>
                                        </select></td>
                                    <td><select class="form-control" name="gaiola_propria" id="gaiola_propria">
                                            <option selected value="0">{{trans('general.nao')}}</option>
                                            <option value="1">{{trans('general.sim')}}</option>
                                        </select></td>
                                    <td><input type="text" size="10" class="form-control" name="observacao" placeholder="{{trans('general.observacao')}}"></td>

                                </tr>

                                <tr>
                                    <td colspan="6">

                                    </td>
                                    <td>
                                        <strong>{{trans('general.multiplicador')}}</strong> <i class="fa fa-question-circle" title="{{trans('general.texto_multiplicador')}}"></i>
                                    </td>

                                </tr>
                                <tr>
                                    <td colspan="5"></td>

                                    <td>
                                        <input type="submit" class="btn btn-info" value="{{trans('general.adicionar_passaro')}}">

                                    </td>
                                    <td>
                                        <input type="text" size="2" class="form-control" value="1" name="multiplicador">
                                    </td>
                                </tr>

                            </table>
                        </form>
                        {{--<button id="adicionar_bt" type="button" class="btn btn-primary">{{trans('general.adicionar_passaro')}}</button>--}}


                    </div>
                </div>
            </div>
        </div>


        <div class="row">
            <div class="col-sm-12">


                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans('eventos.passaros_inscritos')}}</h3>
                    </div>
                    <div class="box-body">

                        <table class="table table-striped table-bordered" cellspacing="0" id="tabpassaros">
                            <thead>
                            <tr>
                                <th>{{trans('general.seccao_classe')}}</th>
                                <th>{{trans('clubes.num_gaiola')}}</th>
                                <th>{{trans('general.anilha')}}</th>
                                <th>{{trans('general.ano')}}</th>
                                <th>{{trans('general.preco')}}</th>
                                <th>{{trans('general.vendido')}}</th>
                                <th>{{trans('general.ausente')}}</th>
                                <th>{{trans('general.gaiola_propria')}}</th>
                                <th></th>
                            </tr>
                            </thead>

                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('eventos.formEditPassaro')

@endsection

@section('scripts')

    <script>

        $(document).ready(function () {

            $('#selcriadores').on('changed.bs.select', function (e) {
                //console.log($('#selcriadores').val());
                $('input[name="inscricaoid"]').val($('#selcriadores').val());
            });

            $('input[name="inscricaoid"]').val($('#selcriadores').val());

            var idinscricao = $('#selcriadores').val();
            var urlbase = '<?php echo url('ajax_passaros'); ?>';
            var popmebase = '<?php echo url('ajax_inscrever_passaro') . "/" . $evento->id; ?>';

            var urlgetpassaro = '<?php echo url('ajax_getpassaro'); ?>';

            if (idinscricao == null) return;

            $('#selcriadores').on('changed.bs.select', function (e) {
                var comp = urlbase + '/' + $('#selcriadores').val();

                tabp.ajax.url(comp).load();
                tabp.ajax.reload();

            });

            $("#adicionar_bt").click(function () {

                $.fancybox.open({href: popmebase + '/' + $('#selcriadores').val(), type: 'ajax'});
            });

            $(".adicionar_pop").fancybox({
                maxWidth: 800,
                maxHeight: 600,
                fitToView: false,
                width: '800px',
                height: '150px',
                autoSize: true,
                closeClick: false,
                openEffect: 'none',
                closeEffect: 'none'
            });

            $('#tabpassaros').on('draw.dt', function () {
                $("button[name^='remover-']").click(function () {

                    var idp = $(this).data("id");
                    var reg = $(this).data("regressa");
                    if (confirm("Posso remover este pássaro?")) {

                        $.ajax({
                            url: reg,
                            cache: false
                        })
                                .done(function (html) {
                                    location.reload(true);
                                });
                    } else {
                        return false;
                    }

                });
                $("button[name^='editar-']").click(function () {

                    var idp = $(this).data("id");

                    $.ajax({
                        url: urlgetpassaro + '/' + idp,
                        cache: false
                    })
                            .done(function (data) {

                                // atribuição de valores para edição






                                $("form#form_editar_passaro input[name='epassaroid']").val(data[0]['id']);
                                $("form#form_editar_passaro input[name='enumgaiola']").val(data[0]['numgaiola']);
                                $("form#form_editar_passaro input[name='einscricaoid']").val(data[0]['inscricao_id']);
                                //$("form#form_editar_passaro input[name='eseccaoclasseid']").val(data[0]['rel_evento_seccao_classe_id']);

                                $("form#form_editar_passaro input[name='eseccaoclasseidold']").val(data[0]['rel_evento_seccao_classe_id']);
                                $("form#form_editar_passaro select[name='eseccaoclasseid']").selectpicker('val', data[0]['rel_evento_seccao_classe_id']);







                                $("form#form_editar_passaro input[name='epreco']").val(data[0]['preco']);
                                $("form#form_editar_passaro input[name='eanilha']").val(data[0]['anilha']);
                                $("form#form_editar_passaro input[name='eano']").val(data[0]['ano']);


                                $("form#form_editar_passaro select[name='evendido']").val(data[0]['vendido']);
                                $("form#form_editar_passaro select[name='eausente']").val(data[0]['ausente']);
                                $("form#form_editar_passaro select[name='gpropria']").val(data[0]['gaiola_propria']);

                                $("form#form_editar_passaro input[name='eobservacao']").val(data[0]['observacao']);


                                //location.reload(true);
                            });


                    $("#editpassaro").modal('show');


                });
//                $("button#cancelar1").click(function () {
//                    console.log($("form#form_editar_passaro select[name='evendido']").val());
//                    $("form#form_editar_passaro select[name='eseccaoclasseid'] option[value='1140']").attr('selected', 'selected');
//                    // $("input[name='eseccaoclasseid']").selectpicker('val', 1140);
//                });

                $("button#imprimirficha").click(function () {
                    window.open('<?php echo url('pdf_ficha_inscricao_admin').'/'.$evento->id; ?>/'+$('#selcriadores').val());

                });
$("button#iepbutton").click(function () {
                    window.open('<?php echo url('pdf_etiquetas_provisorias').'/'.$evento->id; ?>/'+$('#selcriadores').val());

                });


                //


            });


            var tabp = $('#tabpassaros').DataTable({
                processing: true,
                serverSide: true,
                searching: false,
                renderer: "bootstrap",
                ajax: urlbase + '/' + idinscricao,
                columns: [
                    {data: 'nome', name: 'nome'},
                    {data: 'numgaiola', name: 'numgaiola'},
                    {data: 'anilha', name: 'anilha'},
                    {data: 'ano', name: 'ano'},
                    {data: 'preco', name: 'preco'},
                    {data: 'vendido', name: 'vendido'},
                    {data: 'ausente', name: 'ausente'},
                    {data: 'gaiola_propria', name: 'gaiola_propria'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],


            });
        });

    </script>

@endsection