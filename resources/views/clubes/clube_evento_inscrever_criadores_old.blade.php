@extends('layouts.app')


@section('titulo','Inscrição de criadores em Evento')
@section('subtitulo',$clube->nome)

@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> {{trans('clubes.inicio')}}</a></li>
        <li>Evento</li>
        <li class="active">{{trans('general.inscricoes')}}</li>
    </ol>
@endsection
@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-10">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{$evento->nome}}</h3>
                    </div>
                    <div class="box-body">


                        <form id="form_inscrever" action="<?php echo route('clube_evento_gravar_inscricoes_criadores'); ?>"
                              method="post">
                            <select multiple="multiple" size="10" name="criadores[]" class="multiplosi">
                                @foreach ($users_clube as $uc)
                                    <option value="{{ $uc->user_id }}">{{ $uc->name }} ({{$uc->stam}})</option>
                                @endforeach
                                @foreach ($inscricoes as $i)
                                    <option selected="selected" value="{{ $i->user_id }}">{{ $i->name }} ({{$i->stam}})</option>
                                @endforeach

                            </select>
                            <input type="hidden" name="clubeid" value="{{$clube->id}}">
                            <input type="hidden" name="eventoid" value="{{$evento->id}}">
                            <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                            <br>
                            <input type="submit" class="btn btn-info" value="Inscrever Criadores">
                        </form>
                        <a href="<?php echo route('clube_evento', [$clube->id, $evento->id]); ?>">
                            <button type="button" class="btn btn-default">{{trans('clubes.sair')}}</button>
                        </a>
                    </div>
                </div>

            </div>
            <div class="row">
                <div class="col-sm-3">
                    {{--<a href="clube_evento/inscrever/">
                        <button type="button" class="btn btn-primary">{{trans('clubes.inscrever_criadores')}}</button>
                    </a>--}}
                </div>
                <div class="col-sm-3">

                </div>
            </div>

        </div>

    </div>


@endsection

@section('scripts')

    <script src="{{URL::asset('js/jquery.bootstrap-duallistbox.js')}}"></script>
    <script>

        $(document).ready(function () {

            var multiplosi = $('.multiplosi').bootstrapDualListbox({
                nonSelectedListLabel: 'Criadores do Clube',
                selectedListLabel: 'Inscritos no Evento',
                preserveSelectionOnMove: 'moved',
                moveOnSelect: false,
                nonSelectedFilter: '',
                filterTextClear: 'Todos',
                helperSelectNamePostfix: ''


            });

        });


    </script>

@endsection