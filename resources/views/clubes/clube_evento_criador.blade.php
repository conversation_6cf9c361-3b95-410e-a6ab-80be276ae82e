@section('titulo','Eventos Clube')


<section id="evento">

    <div class="row">

        @include("eventos.evento_view")

    </div>
    <div class="row">
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-aqua"><i class="fa fa-users"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">{{trans('clubes.criadores_inscritos')}}</span>
                    <span class="info-box-number">{{$num_inscricoes}}</span>
                    <span><a href="<?php echo route('clube_evento_inscrever_criadores', [$clube->id, $evento->id]); ?>">{{trans('clubes.gerir')}}</a></span>
                </div><!-- /.info-box-content -->
            </div><!-- /.info-box -->
        </div><!-- /.col -->
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-aqua"><i class="fa fa-send-o"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">{{trans('eventos.passaros_inscritos')}}</span>
                    <span class="info-box-number">{{$num_passaros}}</span>
                    <span><a href="<?php echo route('clube_evento_inscrever_passaros', [$clube->id, $evento->id]); ?>">{{trans('clubes.gerir')}}</a></span>
                </div><!-- /.info-box-content -->
            </div><!-- /.info-box -->
        </div><!-- /.col -->
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-aqua"><i class="fa fa-file-text-o"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">{{trans('general.seccoes_classes')}}</span>
                    <span class="info-box-number">{{$num_seccoes_classes}}</span>
                    <span><a href="<?php echo route('eventos_seccoes_import', [$clube->id, $evento->id]); ?>">{{trans('clubes.gerir')}}</a></span>
                </div><!-- /.info-box-content -->
            </div><!-- /.info-box -->
        </div><!-- /.col -->

        <div class="col-sm-3">

            <a href="<?php echo route('casa'); ?>">
                <button type="button" class="btn btn-default">{trans('clubes.cancelar')}}</button>
            </a>

        </div>
    </div>
    <div class="row">
        <div class="col-md-6 col-sm-12 col-xs-12">
            <div class="box box-solid box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">{{trans('general.Listagens')}}</h3>
                </div>
                <div class="box-body">
                    <div class="list-group">
                        <a class="list-group-item" target="_blank" href="<?php echo route('pdffichasinscricao',[$clube->id,$evento->id]); ?>">{{trans('clubes.fichas_de_inscricao')}} (PDF)</a>
                        <a class="list-group-item" target="_blank" href="<?php echo route('pdfetiquetasprovisorias',[$clube->id,$evento->id]); ?>">{{trans('clubes.etiquetas_provisorias')}} (PDF)</a>
                        <a class="list-group-item" target="_blank" href="<?php echo route('pdfetiquetasdefinitivas',[$clube->id,$evento->id]); ?>">{{trans('clubes.etiquetas_definitivas')}} (PDF)</a>
                        <a class="list-group-item" href="<?php echo route('etiquetasprovisoriasindividuais',[$clube->id,$evento->id]); ?>">{{trans('clubes.etiquetas_provisorias_individuais')}} ...</a>
                        <a class="list-group-item" target="_blank" href="<?php echo route('pdffichasjulgamento',[$evento->id]); ?>">{{trans('clubes.fichas_de_Controlo_de_Julgamento')}} (PDF)</a>
                        <a class="list-group-item" target="_blank" href="<?php echo route('pdfcatalogoavesclasse',[$evento->id]); ?>">{{trans('clubes.catalogo_aves_por_classe')}} (PDF)</a>
                        <a class="list-group-item" target="_blank" href="<?php echo route('pdffichasdevolucao',[$clube->id,$evento->id]); ?>">{{trans('clubes.fichas_de_devolucao')}} (PDF)</a>

                    </div>

                </div>

            </div>
        </div>
        <div class="col-md-3 col-sm-3 col-xs-12">
            <form onsubmit="return validarF()" id="form_pontuar_passaros" action="{!! route('procurar_gaiola') !!}" method="post">
                <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                <label for="numgaiola">{{trans('clubes.num_gaiola')}}</label>
                <input type="text" size="4" class="form-control" name="numgaiola" placeholder="Nº Gaiola">
                <input type="submit" class="btn btn-info" value="Procurar">

                </form>
        </div>

    </div>
</section>

{{--
 -inscriçao para socios: 2,0 euros
    -inscriçaõ para nao socios: 2,50 euros
    -catalogo obrigatorio: 5,00 euros

$table->string('img_logotipo');

$table->string('img_backdiploma');
$table->decimal('preco_inscricao_passaro', 10, 2);
$table->decimal('preco_inscricao_equipa', 10, 2);
$table->decimal('preco_inscricao_ave_feira', 10, 2);
$table->decimal('preco_inscricao_catalogo', 10, 2);
$table->decimal('preco_jantar', 10, 2);
$table->decimal('preco_mesa_venda', 10, 2);
$table->integer('desconto_socio'); // percentagem
$table->integer('percentagem_venda_aves'); // percentagem
$table->integer('percentagem_venda_aves_feira'); // percentagem--}}
