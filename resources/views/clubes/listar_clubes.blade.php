@extends('layouts.app')
@section('page_css')



    <link href="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.css')}}" rel="stylesheet"
          type="text/css"/>


@endsection
@section('titulo',trans('forms.gestao_clubes') )
@section('subtitulo','')
@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casaadmin'); ?>">{{ trans('forms.inicio_admin') }}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{ trans('forms.gestao_clubes') }}</span>
        </li>
    </ul>

@endsection

@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{ trans('forms.clubes_associacao') }}
                        </div>

                    </div>
                    <div class="portlet-body">
                        @if(Session::has('flash_message'))
                            <div class="alert alert-success">

                                {{Session::get('flash_message')}}
                                <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;
                                </button>
                            </div>
                        @endif
                        <div style="text-align: left;margin-top: 10px;margin-bottom: 15px;" class="form-group">
                            <a class="demo btn btn-primary btn-large" data-toggle="modal"
                               href="{{URL::route('adminclubecreate')}}"
                               data-target="#ajaxcreateca">{{ trans('forms.criar_clube') }}</a>
                        </div>
                        <div id="gridclubes" style="width: 100%; height: 500px;"></div>
                    </div>
                </div>


            </div>
        </div>
    </div>
    <div class="modal fade" id="ajaxcreateca" role="basic" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">
                    <span> &nbsp;&nbsp;{{trans('general.loading')}}</span>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="ajaxeditca" role="basic" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" id="tralha">
                <div class="modal-body">
                    <img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">
                    <span> &nbsp;&nbsp;{{trans('general.loading')}}</span>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('scripts')

    <script src="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.min.js')}}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/pages/scripts/ui-sweetalert.min.js')}}" type="text/javascript"></script>


    <script>

        var gg;


        $(document).ready(function () {
            w2utils.settings.dataType = 'RESTFULL';
            w2utils.locale('https://raw.githubusercontent.com/vitmalina/w2ui/master/src/locale/pt-br.json')

            gg = $('#gridclubes').w2grid({
                name: 'gridclubes',
                header: 'Clubes e Associações',
                multiSelect: false,
                multiSearch: true,
                recid: "id",
                show: {
                    toolbar: true,
                    footer: true,
                    toolbarSearch: false,
                    toolbarEdit: true,
                    toolbarDelete: true,
                },
                searches: [
                    {field: 'sigla', caption: 'Sigla', type: 'text'},
                    {field: 'nome', caption: 'Nome', type: 'text'},
                    {field: 'email', caption: 'E-mail', type: 'text'},
                    {field: 'pais', caption: 'País', type: 'text'},
                    {field: 'federacao', caption: 'Federação'},
                ],
                columns: [
                    {field: 'id', caption: 'ID', size: '50px', hidden: true},
                    {field: 'sigla', caption: 'Sigla', size: '50px', sortable: true},
                    {field: 'nome', caption: 'Nome', size: '50%', sortable: true},
                    {field: 'email', caption: 'E-mail', size: '30%'},
                    {field: 'pais', caption: 'País', size: '30%'},
                    {field: 'federacao', caption: 'Federação', size: '30%'},
                ],
                onDblClick: function (event) {
                    var link = '<?php echo url('clubes') ?>/' + event['recid'] + '/edit';

                    //console.log(link);
                    $("#ajaxeditca").modal("show");
                    $("#tralha").load(link);

                },
                onSearch: function (target, data) {
                    for (var s in data.searchData) {
                        data.searchData[s].operator = 'contains';
                    }
                },
                onEdit: function (event) {
                    var link = '<?php echo url('clubes') ?>/' + event['recid'] + '/edit';

                    //console.log(link);
                    $("#ajaxeditca").modal("show");
                    $("#tralha").load(link);

                },
                onDelete: function (event) {
                    event.preventDefault();
                    var sel = this.getSelection();
                    var nome = gg.get(sel)[0]['nome'];
                    var id = gg.get(sel)[0]['id'];

                    var link = '<?php echo url('ajax_numusersclube_admin') ?>/' + id;
                    $.ajax({
                        type: "GET",
                        url: link,
                        success: function (response) {

                            //console.log(response);
                            var apagavel = false;
                            var mensagem1;
                            var mensagem2;
                            if (response > 0) {


                                mensagem1 = "Este clube tem " + response + " criadores associados. Não é possível apagar."
                                mensagem2 = 'Para eliminar terá de desassociar todos os criadores deste clube';

                            } else {
                                console.log(gg);
                                apagavel = true;
                                mensagem1 = "Posso eliminar o Clube/Associação?";
                                mensagem2 = nome;

                            }

                            swal({
                                    title: mensagem1,
                                    text: mensagem2,
                                    type: "warning",
                                    showCancelButton: true,
                                    showConfirmButton: apagavel,
                                    cancelButtonText: "Cancelar",
                                    confirmButtonClass:
                                        'btn-danger',
                                    confirmButtonText:
                                        'Sim, apagar.',
                                    closeOnConfirm:
                                        false,
                                    //closeOnCancel: false
                                },

                                function () {


                                    $.ajax({
                                        type: "GET",
                                        url: '<?php echo url('ajax_deleteclube_admin') ?>/' + id,
                                        success: function (response) {
                                            swal("Apagado", "Apaguei o Clube/Associação que pretendia", "success");

                                            gg.reload();
                                        },
                                        error: function (response) {


                                        }
                                    });


                                });


                        },
                        error: function (response) {


                        }
                    }).fail(function (response) {

                    });


                }
            });
            //url: "{{URL::route('ajaxclubesadmin')}}",
            gg.load('{{URL::route('ajaxclubesadmin')}}');


        });

    </script>

@endsection