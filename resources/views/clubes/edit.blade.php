<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3>{{ trans('forms.editar_federacao') }}</h3>
</div>
<div class="modal-body">
    <div class="row">

        <div class="form-group col-md-12" id="erros">
        </div>

        {!!  Form::model($clube, ['id'=>'formcluedit', 'method'=>'PATCH', 'route'=>['adminclubeupdate',$clube->id]]) !!}

        <div class="form-group col-md-12">
            {!! Form::label('federacao',trans('forms.federacao')) !!}
            <select id="federacao_id" name="federacao_id" class="form-control col-sm-6" data-live-search="true">
                @foreach($federacoes as $te)
                    <option value="{{ $te->id }}" {{( $te->id==$clube->federacao_id)?'selected':''}}>{{$te->nome}}</option>
                @endforeach
            </select>
        </div>
        <div class="form-group col-md-2">
            {!! Form::label('sigla',trans('forms.sigla')) !!}
            {!! Form::text('sigla', null, ['class'=> 'form-control' ]) !!}
        </div>
        <div class="form-group col-md-10">
            {!! Form::label('nome',trans('forms.nome')) !!}
            {!! Form::text('nome', null, ['class'=> 'form-control' ]) !!}
        </div>
        <div class="form-group col-md-6">

            {!! Form::label('email',trans('general.email')) !!}
            {!! Form::text('email', null, ['class'=> 'form-control' ]) !!}

        </div>
        <div class="form-group col-md-6">

            {!! Form::label('telefone',trans('general.telefone')) !!}
            {!! Form::text('telefone', null, ['class'=> 'form-control' ]) !!}

        </div>
        <div class="form-group col-md-12">
            {!! Form::label('morada',trans('general.morada')) !!}
            {!! Form::text('morada', null, ['class'=> 'form-control' ]) !!}
        </div>

        <div class="form-group col-md-6">
            {!! Form::label('cod_postal','general.codigo_postal') !!}
            {!! Form::text('cod_postal', null, ['class'=> 'form-control' ]) !!}


        </div>
        <div class="form-group col-md-6">
            {!! Form::label('localidade1','general.localidade'.' 1') !!}
            {!! Form::text('localidade1', null, ['class'=> 'form-control' ]) !!}
        </div>
        <div class="form-group col-md-6">
            {!! Form::label('localidade2','general.localidade'.' 2') !!}
            {!! Form::text('localidade2', null, ['class'=> 'form-control' ]) !!}
        </div>
        <div class="form-group col-md-6">
            {!! Form::label('num_contribuinte','general.num_contribuinte') !!}
            {!! Form::text('num_contribuinte', null, ['class'=> 'form-control' ]) !!}
        </div>
        <div class="form-group col-md-12">
            {!! Form::label('observacoes','general.observacoes') !!}
            {!! Form::text('observacoes', null, ['class'=> 'form-control' ]) !!}
        </div>
        <div class="form-group col-md-12">
            falta logotipo upload
        </div>

        <div class="form-group col-md-12 pull-right">
            <button type="button" data-dismiss="modal" class="btn">{{ trans('forms.cancelar') }}</button>
            <button class="btn btn-primary">{{ trans('forms.gravar') }}</button>
            <br>
        </div>
        {!! Form::close() !!}
    </div>

</div>
<script>

    $('#formcluedit').submit(function (e) {
        e.preventDefault();

        var $form = $(this);

        var urlpost = $form.attr('action');
        //console.log(urlpost);
        $.ajax({
            type: "POST",
            url: urlpost,
            data: $(this).serialize(),
            success: function (response) {

                $('#ajaxeditca').modal('toggle');

                swal("Alteração gravada", "A sua alteração foi gravada com sucesso", "success");
                gg.reload();
                //window.location.href = "{{URL::route('adminfederacoeslist')}}";


            },
            error: function (response) {

                if (response.status == 422) {


                    $('#erros').empty();


                    $('#erros').append("<div class=\"note note-danger\" id=\"errosi\"></div>");
                    $.each(response.responseJSON, function (key, value) {


                        $('#errosi').append("<p>" + value + "</p>");
                        //console.log(key + " - " + value);
                    });
                    //console.log(response.responseJSON);
                }


            },
            statusCode: {
                422: function () {

                }
            }
        }).fail(function (response) {

        });


    });


</script>
