<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3>{{ trans('forms.criar_clube') }}</h3>
</div>
<div class="modal-body">
    <div class="row">

        <div class="form-group col-md-12" id="erros">
        </div>

        {!! Form::open(['route' => 'adminclubestore','id'=>'formclu']) !!}
        {{ Form::hidden('estado', 1) }}
        @include('clubes._formgeral')
        <div class="form-group col-md-12 pull-right">
            <button type="button" data-dismiss="modal" class="btn">{{ trans('forms.cancelar') }}</button>
            <button class="btn btn-primary">{{ trans('forms.criar') }}</button>
            <br>
        </div>
        {!! Form::close() !!}
    </div>

</div>
<script>

    $('#formclu').submit(function (e) {
        e.preventDefault();

        var $form = $(this);

        var urlpost = $form.attr('action');
        console.log(urlpost);

        $.ajax({
            type: "POST",
            url: urlpost,
            data: $(this).serialize(),
            success: function (response) {
                $('#ajaxcreateca').modal('toggle');

                //console.log("success:" + response);
                swal("Clube Criado", "O novo clube foi criado com sucesso", "success");
                gg.reload();
                //window.location.href = "{{URL::route('adminclubeslist')}}";


            },
            error: function (response) {
                if (response.status == 422) {


                    $('#erros').empty();


                    $('#erros').append("<div class=\"note note-danger\" id=\"errosi\"></div>");
                    $.each(response.responseJSON, function (key, value) {


                        $('#errosi').append("<p>" + value + "</p>");
                        //console.log(key + " - " + value);
                    });
                    //console.log(response.responseJSON);
                }


            },
            statusCode: {
                422: function () {

                }
            }
        }).fail(function (response) {

        });


    });


</script>
