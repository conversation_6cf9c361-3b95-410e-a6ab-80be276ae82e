@extends('layouts.app')



@section('content')

@section('titulo', trans('general.dashboard_administrador'))

<section id="infotralha">
    <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
            <a class="dashboard-stat dashboard-stat-v2 blue" href="{!! route('adminuserslist') !!}">
                <div class="visual">
                    <i class="fa fa-users"></i>
                </div>
                <div class="details">
                    <div class="number">
                        <span data-counter="counterup" data-value="{{$nusers}}">{{$nusers}}</span>
                    </div>
                    <div class="desc"> {{ trans('general.utilizadores') }}</div>
                    <div class="desc"><h6>({{ trans('general.clique_gerir') }})</h6></div>
                </div>
            </a>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
            <a class="dashboard-stat dashboard-stat-v2 green" href="{!! route('adminclubeslist') !!}">
                <div class="visual">
                    <i class="fa fa-building"></i>
                </div>
                <div class="details">
                    <div class="number">
                        <span data-counter="counterup" data-value="{{$nclubes}}">{{$nclubes}}</span>
                    </div>
                    <div class="desc"> {{ trans('general.clubes_associacoes') }}</div>
                    <div class="desc"><h6>({{ trans('general.clique_gerir') }})</h6></div>
                </div>
            </a>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
            <a class="dashboard-stat dashboard-stat-v2 purple" href="{!! route('adminfederacoeslist') !!}">
                <div class="visual">
                    <i class="fa fa-bank"></i>
                </div>
                <div class="details">
                    <div class="number">
                        <span data-counter="counterup" data-value="{{$nfederacoes}}">{{$nfederacoes}}</span>
                    </div>
                    <div class="desc"> {{ trans('general.federacoes') }}</div>
                    <div class="desc"><h6>({{ trans('general.clique_gerir') }})</h6></div>
                </div>
            </a>
        </div>
    </div>

</section>
<br>
<section id="pendentes">
    <div class="portlet box blue-hoki">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-share font-white"></i>
                <span class="caption-subject font-white uppercase">{{ trans('general.pendentes') }}</span>
            </div>

        </div>
        <div class="portlet-body" style="height: 450px;">
            <div class="col-md-8">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{ trans('general.criadores') }}
                        </div>

                    </div>
                    <div class="portlet-body">
                        <div id="gridusers" style="width: 100%; height: 300px;"></div>
                    </div>
                </div>


            </div>

            <div class="col-md-4">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{ trans('general.eventos_pedidos') }}
                        </div>
                        <div class="tools">
                            <a href="javascript:;" class="collapse" data-original-title="" title=""> </a>
                        </div>
                    </div>
                    <div class="portlet-body">
                        {{ trans('general.eventos_pedidos_lista') }}
                    </div>
                </div>


            </div>

        </div>
    </div>


</section>

<section id="eventos">

    <div class="portlet box blue-hoki">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-share font-white"></i>
                <span class="caption-subject font-white uppercase">{{ trans('general.eventos_activos') }}</span>
            </div>

        </div>
        <div class="portlet-body">
            <div class="row">
                @foreach ($eventos_activos as $evt)
                    <div class="col-lg-6 col-md-6 col-xs-12">
                        <div class="mt-element-ribbon bg-grey-steel" style="min-height: 250px">
                            <div class="ribbon ribbon-color-default uppercase bold">{{ $evt->nome }}</div>
                            <table style="margin-bottom: 20px;" class="table table-striped task-table">
                                <tbody>
                                <tr>
                                    <td class="table-text">
                                        <div><strong>{{ trans('general.data_inicio') }}</strong><br> {{ $evt->data_inicio }}
                                        </div>
                                        <div><strong>{{ trans('general.data_fim') }}</strong><br> {{ $evt->data_fim }}</div>
                                    </td>
                                    <td class="table-text">
                                        <div style="margin-top: 10px;">{{ trans('general.registration_period') }}<br>
                                            <strong>{{ $evt->data_inicio_inscricoes }}</strong> a
                                            <strong>{{ $evt->data_fim_inscricoes }}</strong></div>
                                    </td>
                                    <td class="table-text">
                                        <a href="{{ route('eventohome',[$evt->id]) }}">
                                            <button class="btn btn-success">{{ trans('general.gerir') }}</button>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="col-md-6 col-sm-6 col-xs-12">

                                <div class="box-footer">
                                    <ul class="nav nav-stacked">
                                        <li>
                                            {{ trans('general.registrations') }} <span
                                                    class="pull-right badge bg-blue">{{$evt->num_inscricoes}}</span>
                                        </li>
                                        <li>
                                            {{ trans('general.birds') }} <span
                                                    class="pull-right badge bg-aqua">{{$evt->num_passaros}}</span>
                                        </li>

                                    </ul>
                                </div>

                            </div><!-- /.col -->
                            <div class="col-md-6 col-sm-6 col-xs-12">

                                <div class="box-footer">
                                    <ul class="nav nav-stacked">
                                        <li>
                                            {{ trans('general.juizes') }} <span
                                                    class="pull-right badge bg-green">{{$evt->num_juizes}}</span>
                                        </li>
                                        <li>
                                            {{ trans('general.fichas_julgamento') }}<span
                                                    class="pull-right badge bg-red">{{$evt->num_fichas_julgamento}}</span>
                                        </li>
                                        <li>
                                            {{ trans('general.seccoes_classes') }}<span
                                                    class="pull-right badge bg-red">{{$evt->num_seccoes_classes}}</span>
                                        </li>
                                    </ul>
                                </div>


                            </div><!-- /.col -->


                        </div>
                    </div>

                @endforeach
            </div>

        </div>
    </div>


    <div class="portlet box red-pink">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-share font-white"></i>
                <span class="caption-subject font-white uppercase">{{ trans('general.evt_nao_publicados') }}</span>
            </div>

        </div>
        <div class="portlet-body">
            <div class="row">
                @foreach ($eventos_nao_publicados as $evtnpub)
                    <div class="col-lg-6 col-md-6 col-xs-12">
                        <div class="mt-element-ribbon bg-grey-steel" style="min-height: 250px">
                            <div class="ribbon ribbon-color-default uppercase bold">{{ $evtnpub->nome }}</div>
                            <table style="margin-bottom: 20px;" class="table table-striped task-table">
                                <tbody>
                                <tr>
                                    <td class="table-text">
                                        <div>
                                            <strong>{{ trans('general.data_inicio') }}</strong><br> {{ $evtnpub->data_inicio }}
                                        </div>
                                        <br>
                                        <div>
                                            <strong>{{ trans('general.data_fim') }}</strong><br> {{ $evtnpub->data_fim }}
                                        </div>
                                    </td>
                                    <td class="table-text">
                                        <div>{{ trans('general.registration_period') }}<br>
                                            <strong>{{ $evtnpub->data_inicio_inscricoes }}</strong><br> a<br>
                                            <strong>{{ $evtnpub->data_fim_inscricoes }}</strong></div>
                                    </td>
                                    <td class="table-text">
                                        <a href="{{ route('eventohome',[$evtnpub->id]) }}">
                                            <button class="btn btn-success">{{ trans('general.gerir') }}</button>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="col-md-6 col-sm-6 col-xs-12">

                                <div class="box-footer">
                                    <ul class="nav nav-stacked">
                                        <li>
                                            {{ trans('general.inscricoes') }} <span
                                                    class="pull-right badge bg-blue">{{$evtnpub->num_inscricoes}}</span>
                                        </li>
                                        <li>
                                            {{ trans('general.passaros_inscritos') }}<span
                                                    class="pull-right badge bg-aqua">{{$evtnpub->num_passaros}}</span>
                                        </li>

                                    </ul>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6 col-xs-12">

                                <div class="box-footer">
                                    <ul class="nav nav-stacked">
                                        <li>
                                            {{ trans('general.juizes') }}<span
                                                    class="pull-right badge bg-green">{{$evtnpub->num_juizes}}</span>
                                        </li>
                                        <li>
                                            {{ trans('general.fichas_julgamento') }}<span
                                                    class="pull-right badge bg-red">{{$evtnpub->num_fichas_julgamento}}</span>
                                        </li>
                                        <li>
                                            {{ trans('general.seccoes_classes') }}<span
                                                    class="pull-right badge bg-red">{{$evtnpub->num_seccoes_classes}}</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>


                        </div>

                        @endforeach
                    </div>

            </div>
        </div>
    </div>

</section>

@endsection

@section('scripts')
    <script>

        var gg;

        function refreshPendentes() {

            gg.load("{{URL::route('ajaxusersadminpending')}}");
            gg.select(0);

        }

        $(document).ready(function () {
            w2utils.settings.dataType = 'RESTFULL';
            w2utils.locale('https://raw.githubusercontent.com/vitmalina/w2ui/master/src/locale/pt-br.json')

            gg = $('#gridusers').w2grid({
                name: 'gridusers',
                header: 'Criadores',
                recid: "id",
                multiSelect: false,
                show: {
                    toolbar: true,
                    footer: true,
                    toolbarEdit: true,
                },
                toolbar: {
                    items: [
                        {
                            type: 'button',
                            id: 'activarMassa',
                            caption: 'Activar em Massa',
                            img: 'w2ui-icon-check',
                            disabled: false
                        },

                    ],
                    onClick: function (event) {
                        event.onComplete = function () {
                            //console.log(event);
                            if (event.target == 'activarMassa') {

                                var acp = '{!! route('ajaxactivarcriadores') !!}';
                                if (confirm("{{ trans('messages.activate_all_users_confirmation') }}")) {

                                    $.ajax({
                                        url: acp,
                                        cache: false
                                    })
                                        .done(function (html) {

                                            refreshPendentes();
                                        });
                                } else {
                                    return false;
                                }


                            }
                        }

                    }
                },
                multiSearch: false,
                multiSelect: false,
                searches: [
                    {field: 'stam', caption: 'stam ', type: 'text'},
                ],
                columns: [
                    {field: 'id', caption: 'ID', size: '30px', hidden: true},
                    {field: 'stam', caption: 'STAM', size: '60px', sortable: true},
                    {field: 'estado', caption: 'Estado', size: '30%'},
                    {field: 'name', caption: 'Nome', size: '70%', sortable: true},
                ],
                onEdit: function (event) {
                    var link = '<?php echo url('users') ?>/' + event['recid'] + '/edit/casaadmin';
                    window.open(link, "_self");
                },
                onRender: function () {

                }


            });

            //console.dir(gg);

            function ldatac() {
                gg.request('get-records', [], '{{URL::route('ajaxusersadminpending',['origem'=>'casaadmin'])}}');
            }

            //ldatac();
            refreshPendentes();


        });


    </script>






@endsection

