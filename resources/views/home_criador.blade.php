@extends('layouts.app')



@section('content')


@section('titulo', trans('general.dashboard_criador'))
<section id="tralha">
    @if(Session::has('flash_message'))
        <div class="alert alert-success col-sm-12">

            {{Session::get('flash_message')}}
            <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;</button>
        </div>
    @endif


    <div class="row">
        <div class="col-sm-12">
            <div class="note note-info">
                <h4 class="block">{{ trans('general.bemvindo') }}</h4>
                <p>{{ trans('general.bemvindotxt1') }}</p>
                <p>{{ trans('general.bemvindotxt2') }}</p>
            </div>

        </div>
    </div>

    @if (count($eventos_activos) > 0)

        <div class="portlet box blue-hoki">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-share font-white"></i>
                    <span class="caption-subject font-white uppercase">{{ trans('general.eventos_activos') }}</span>
                </div>

            </div>
            <div class="portlet-body">
                <div class="row">
                    @foreach ($eventos_activos as $evt)
                        <div class="col-lg-6 col-md-6 col-xs-12">
                            <div class="mt-element-ribbon bg-grey-steel" style="min-height: 250px">
                                <div class="ribbon ribbon-color-default uppercase bold">{{ $evt->nome }}</div>
                                <p class="ribbon-content"><strong>{{ trans('forms.data_inicio') }}</strong> {{ $evt->data_inicio }}<br>
                                    <strong>{{ trans('general.data_fim') }}</strong> {{ $evt->data_fim }}<br><br>
                                    {{ trans('general.registration_period') }}
                                    <strong>{{ $evt->data_inicio_inscricoes }}</strong> a
                                    <strong>{{ $evt->data_fim_inscricoes }}</strong><br><br>
                                    @if($evt->inscricao==null)

                                        <a href="{!! route('eventohomecriador',[$evt->id]) !!}">
                                            <button class="btn btn-primary">{{ trans('general.detalhes') }}</button>
                                        </a>



                                    @else
                                        {{ trans('general.ja_inscrito') }}<br>
                                        <a href="{!! route('eventohomecriador',[$evt->id]) !!}">
                                            <button class="btn btn-primary">{{ trans('general.detalhes_inscricao') }}</button>
                                        </a>



                                    @endif
                                </p>
                            </div>
                        </div>

                    @endforeach


                </div>


            </div>
        </div>

    @endif




    @if(count($clubes)>0)

        <div class="portlet box blue-hoki">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-share font-white"></i>
                    <span class="caption-subject font-white uppercase">{{ trans('general.clubes_associados') }}</span>
                </div>

            </div>
            <div class="portlet-body">
                <div class="row">
                    @foreach ($clubes as $c)
                        <div class="col-lg-6 col-md-6 col-xs-12 m-heading-1 border-green m-bordered">
                            <h3>{{ $c->nome }}</h3>
                            @foreach ($c->perms as $p)
                                @if ($p->slug == 'clube_admin' && $p->ruc_id!=null)

                                    <p><a href="javascript:;" class="btn btn-outline sbold blue-madison"
                                          id="blockui_sample_1_1">Gerir utilizadores</a></p>


                                @endif

                                @if ($p->slug == 'clube_anilhas' && $p->ruc_id!=null)

                                    <p><a href="javascript:;" class="btn btn-outline sbold blue-madison"
                                          id="blockui_sample_1_1">Gerir Anilhas</a></p>


                                @endif


                                @if ($p->slug == 'clube_eventos' && $p->ruc_id!=null)

                                    <p><a href="{{URL::route('clube_eventos_home',$c->clube_id)}}"
                                          class="btn btn-outline sbold blue-madison"
                                          id="blockui_sample_1_1">Gerir Eventos</a></p>


                                @endif
                            @endforeach

                        </div>

                    @endforeach


                </div>


            </div>
        </div>





    @endif





    @role('juiz')


    @if(count($eventosjulgamento)>0)

        <div class="portlet box red-haze">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-share font-white"></i>
                    <span class="caption-subject font-white uppercase">{{ trans('general.eventos_julgamento_activo') }}</span>
                </div>

            </div>
            <div class="portlet-body">
                <div class="list-group">
                    @foreach ($eventosjulgamento as $j)
                        <a class="list-group-item"
                           href="<?php echo route('julgareventohome', [$j->evento_id]); ?>">{!! $j->nome !!}</a>
                        <br>
                    @endforeach
                </div>


            </div>
        </div>


    @endif

    @endrole
</section>
@endsection