@extends('layouts.app')

@section('page_css')

    {{--<link href="{{ asset('assets/global/plugins/bootstrap-modal/css/bootstrap-modal-bs3patch.css')}}" rel="stylesheet"--}}
    {{--type="text/css"/>--}}
    {{--<link href="{{ asset('assets/global/plugins/bootstrap-modal/css/bootstrap-modal.css')}}" rel="stylesheet"--}}
    {{--type="text/css"/>--}}

    <link href="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.css')}}" rel="stylesheet"
          type="text/css"/>


@endsection



@section('titulo',trans('general.titulo_federacoes'))
@section('subtitulo','')
@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casaadmin'); ?>">In<PERSON><PERSON></a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{ trans('general.titulo_federacoes') }}</span>
        </li>
    </ul>

@endsection

@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>Federações
                        </div>

                    </div>
                    <div class="portlet-body">
                        @if(Session::has('flash_message'))
                            <div class="alert alert-success">

                                {{Session::get('flash_message')}}
                                <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;
                                </button>
                            </div>
                        @endif


                        <a class="demo btn btn-primary btn-large" data-toggle="modal"
                           href="{{URL::route('adminfederacaocreate')}}"
                           data-target="#ajaxcreatef">{{ trans('forms.criar_federacao') }}</a>
                    </div>
                    <div id="gridfederacoes" style="width: 100%; height: 500px;"></div>
                </div>
            </div>


        </div>
    </div>
    </div>
    <div class="modal fade" id="ajaxcreatef" role="basic" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">
                    <span> &nbsp;&nbsp;{{trans('general.loading')}}</span>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="ajaxeditf" role="basic" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" id="tralha">
                <div class="modal-body">
                    <img src="{{ asset('assets/global/img/loading-spinner-grey.gif')}}" alt="" class="loading">
                    <span> &nbsp;&nbsp;{{trans('general.loading')}}</span>
                </div>
            </div>
        </div>
    </div>


@endsection

@section('scripts')

    {{--<script src="{{ asset('assets/global/plugins/bootstrap-modal/js/bootstrap-modalmanager.js')}}"--}}
    {{--type="text/javascript"></script>--}}
    {{--<script src="{{ asset('assets/global/plugins/bootstrap-modal/js/bootstrap-modal.js')}}"--}}
    {{--type="text/javascript"></script>--}}


    <script src="{{ asset('assets/global/plugins/bootstrap-sweetalert/sweetalert.min.js')}}"
            type="text/javascript"></script>
    <script src="{{ asset('assets/pages/scripts/ui-sweetalert.min.js')}}" type="text/javascript"></script>



    <script>

        function refresca(){

            gg.reload();
        }

        var gg;
        $(document).ready(function () {
            // $('#ajaxeditf').on('shown.bs.modal', function (e) {
            //     $('#pais_id').selectpicker({
            //         size: 6
            //     });
            //     let gs = gg.getSelection();
            //     //console.log(pp);
            //     selectPais(gg.get(gs)[0]['paisid']);
            //     //$('#pais_id').selectpicker('val', gg.get(gs)[0]['paisid']);
            //     console.log(gg.get(gs)[0]['paisid']);
            //
            // });
            $('#edit_classe_passaro').on('shown.bs.modal', function (e) {
                $('.listaseccoespa').selectpicker({
                    size: 10
                });
                let pp = grid_passaros.getSelection();
                //console.log(pp);
                selectClass(grid_passaros.get(pp)[0]['rescid']);
                //console.log(grid_passaros.get(pp)[0]);

            });




            w2utils.settings.dataType = 'RESTFULL';
            w2utils.locale('https://raw.githubusercontent.com/vitmalina/w2ui/master/src/locale/pt-br.json')
// TODO: alterar o locale logo acima para a lingua escolhida
            gg = $('#gridfederacoes').w2grid({
                name: 'gridfederacoes',
                url: "{{URL::route('ajaxfederacoesadmin')}}",
                header: 'Federações',
                multiSelect: false,
                recid: "id",
                show: {
                    toolbar: true,
                    footer: true,
                    toolbarEdit: true,
                    toolbarDelete: true,

                },
                multiSearch: true,
                searches: [
                    {field: 'nome', caption: 'Nome', type: 'text', operator: 'contains'},
                    {field: 'email', caption: 'E-mail', type: 'text', operator: 'contains'},
                    {field: 'pais', caption: 'País', type: 'text', operator: 'contains'},
                ],
                columns: [
                    {field: 'id', caption: 'ID', size: '50px', hidden: true},
                    {field: 'sigla', caption: 'Sigla', size: '50px', sortable: false},
                    {field: 'nome', caption: 'Nome', size: '50%', sortable: false},
                    {field: 'email', caption: 'E-mail', size: '30%'},
                    {field: 'pais', caption: 'País', size: '30%'},
                ],
                onSearch: function (target, data) {
                    for (var s in data.searchData) {
                        data.searchData[s].operator = 'contains';
                    }
                },
                onDblClick: function (event) {
                    var link = '<?php echo url('federacoes') ?>/' + event['recid'] + '/edit';

                    //console.log(link);
                    $("#ajaxeditf").modal("show");
                    $("#tralha").load(link);

                },
                onEdit: function (event) {
                    var link = '<?php echo url('federacoes') ?>/' + event['recid'] + '/edit';

                    //console.log(link);
                    $("#ajaxeditf").modal("show");
                    $("#tralha").load(link);
                    //console.log($("#tralha"));

                },
                onDelete: function (event) {
                    event.preventDefault();
                    var sel = this.getSelection();
                    var nome = gg.get(sel)[0]['nome'];
                    var id = gg.get(sel)[0]['id'];

                    var link = '<?php echo url('ajax_numclubesfederacao_admin') ?>/' + id;
                    $.ajax({
                        type: "GET",
                        url: link,
                        success: function (response) {

                            //console.log(response);
                            var apagavel = false;
                            var mensagem1;
                            var mensagem2;
                            if (response > 0) {


                                mensagem1 = "Esta federação tem " + response + " clubes associados. Não é possível apagar."
                                mensagem2 = 'Para eliminar terá de desassociar todos os clubes desta federação';

                            } else {

                                apagavel = true;
                                mensagem1 = "Posso eliminar a federação?";
                                mensagem2 = nome;

                            }

                            swal({
                                    title: mensagem1,
                                    text: mensagem2,
                                    type: "warning",
                                    showCancelButton: true,
                                    showConfirmButton: apagavel,
                                    cancelButtonText: "Cancelar",
                                    confirmButtonClass:
                                        'btn-danger',
                                    confirmButtonText:
                                        'Sim, apagar.',
                                    closeOnConfirm:
                                        false,
                                    //closeOnCancel: false
                                },

                                function () {


                                    $.ajax({
                                        type: "GET",
                                        url: '<?php echo url('ajax_deletefederacao_admin') ?>/' + id,
                                        success: function (response) {
                                            swal("Apagada", "Apaguei a federação que pretendia", "success");
                                            gg.reload();
                                        },
                                        error: function (response) {


                                        }
                                    });


                                });


                        },
                        error: function (response) {


                        }
                    }).fail(function (response) {

                    });


                    //console.log('selection:', gg.get(sel));


                }

            });

            //gg.load('{{URL::route('ajaxfederacoesadmin')}}');




        });

    </script>

@endsection