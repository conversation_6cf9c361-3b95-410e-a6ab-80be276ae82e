@extends('layouts.app')

@section('titulo','Edição de Permissões')
@section('subtitulo','')
@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> Início</a></li>
        <li><a href="<?php echo route('adminuserslist'); ?>"><i class="fa fa-dashboard"></i> Lista Utilizadores</a></li>
        <li class="active">Editar Permissões</li>
    </ol>
@endsection



@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-md-12">

                <div class="box box-primary col-md-12">
                    <div class="box-header with-border">
                        <h3><strong> {{$user->stam}}</strong></h3><h4>{{$user->name}}</h4>
                    </div>
                    <div class="box-body">

                        {!! Form::open(array('url' => LaravelLocalization::getLocalizedURL(LaravelLocalization::getCurrentLocale(), url('users/associaclube')), 'id'=>'uassoc', 'method'=>'post')) !!}
                        {{ Form::hidden('user_id', $user->id) }}
                        <div class="form-group">

                            {!! Form::label('clube_id','Clubes e Associações:') !!}<br/>
                            {!! Form::select('clube_id', $clubes, 0, ['class' => 'form-control']) !!}

                        </div>
                        <div class="form-group">
                            {!! Form::submit('Associar Criador',['class'=>'btn btn-primary']) !!}
                        </div>
                        {!! Form::close() !!}

                    </div>
                </div>

                @if(count($clubesAssociados)>0)
                    @foreach ($clubesAssociados as $c)

                        <div class="box box-primary col-md-12">
                            <div class="box-header with-border">

                                <strong> {{$c->nome}}</strong>
                                <div class="box-tools pull-right">
                                    {!! Form::open(array('url' => LaravelLocalization::getLocalizedURL(LaravelLocalization::getCurrentLocale(), url('users/associaclube/remove')), 'id'=>'remove'.$c->id, 'data-cid'=>$c->id,'method'=>'post')) !!}
                                    {!! Form::hidden('rucid', $c->id) !!}
                                    {!! Form::hidden('uid', $user->id) !!}
                                    {!! Form::button('<i class="fa fa-times"></i>',array('type'=>'submit','class'=>'btn btn-box-tool')) !!}
                                    {!! Form::close() !!}


                                </div>
                            </div>
                            <div class="box-body">
                                <div class="row">

                                    @for ($i = 0; $i < count($clubesRoles); $i++)

                                        <div class="col-md-4">
                                            @if(isset($c->perms[$i]->ruc_id))

                                                <div class="box box-success">

                                                    @else
                                                        <div class="box box-default">

                                                            @endif


                                                            <div class="box-header with-border">
                                                                {{$clubesRoles[$i]->name}}

                                                                <div class="box-tools pull-right">
                                                                </div>
                                                                <!-- /.box-tools -->
                                                            </div>
                                                            <!-- /.box-header -->
                                                            <div class="box-body">
                                                                <small>{{$clubesRoles[$i]->description}}</small>
                                                                <br>
                                                                @if(isset($c->perms[$i]->ruc_id))

                                                                    {!! Form::open(array('url' => LaravelLocalization::getLocalizedURL(LaravelLocalization::getCurrentLocale(), url('users/permclube/edit')), 'method'=>'post')) !!}
                                                                    {!! Form::hidden('rucid', $c->id) !!}
                                                                    {!! Form::hidden('roleid', $c->perms[$i]->id) !!}
                                                                    {!! Form::hidden('uid', $user->id) !!}
                                                                    {!! Form::button('Desactivar',array('type'=>'submit','class'=>'btn btn-danger')) !!}
                                                                    <span style="font-size:9px"> (Esta permissão está ACTIVA)</span>
                                                                    {!! Form::close() !!}


                                                                @else
                                                                    {!! Form::open(array('url' => LaravelLocalization::getLocalizedURL(LaravelLocalization::getCurrentLocale(), url('users/permclube/edit')), 'method'=>'post')) !!}
                                                                    {!! Form::hidden('rucid', $c->id) !!}
                                                                    {!! Form::hidden('roleid', $c->perms[$i]->id) !!}
                                                                    {!! Form::hidden('uid', $user->id) !!}
                                                                    {!! Form::button('Activar',array('type'=>'submit','class'=>'btn btn-primary')) !!}
                                                                    <span style="font-size:9px"> (Esta permissão está INACTIVA)</span>
                                                                    {!! Form::close() !!}

                                                                @endif

                                                            </div>
                                                            <!-- /.box-body -->

                                                        </div>
                                                </div>

                                                @endfor

                                        </div>

                                </div>
                            </div>

                            @endforeach
                            @else
                                <div class="box box-danger col-md-7">

                                    <div class="box-body">

                                        Este utilizador ainda não tem Clubes/Associações atribuídas.

                                    </div>
                                </div>

                            @endif

                        </div>

            </div>
        </div>
    </div>

@endsection

@section('scripts')
    <script>

        $("form[id^='remove']").submit(function (event) {

            //console.log($(this).data('cid'));

            var x = confirm("Tem a certeza que pretende eliminar este clube deste criador?");
            if (x) {
                return true;
            }
            else {

                event.preventDefault();
                return false;
            }

        });
        //
        //        function confirmaRemocao() {
        //
        //            var result = confirm("Want to delete?");
        //            if (result) {
        //                //Logic to delete the item
        //            }
        //
        //        }
        //
        //
        //        $(document).ready(function () {
        //            $("button[data-tipo='remover']").on("click", function (event) {
        //                console.log($(this).data('cid'));
        //
        //                window.open(link, "_self");
        //
        //            });
        ////
        ////            $("#list").on("click", "a", function (event) {
        ////                var elem = $(this);
        ////                if (elem.is("[href^='http']")) {
        ////                    elem.attr("target", "_blank");
        ////                }
        ////            });
        //
        //
        //        });


    </script>

@endsection
