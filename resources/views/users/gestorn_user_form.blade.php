<div class="form-group">


    {!! Form::label('stam','STAM:') !!}
    <b>{{ $user->stam }}</b>

</div>

<div class="form-group">


    {!! Form::label('password', trans('forms.password')) !!}
    {!! Form::password('password', null, ['class'=> 'form-control' ]) !!}

</div>
<div class="form-group">


    {!! Form::label('name', trans('forms.nome')) !!}
    {{ Form::hidden('name', $user->name) }}
    <b>{{ $user->name }}</b>

</div>
<div class="form-group">

    {!! Form::label('estado_user_id', trans('general.Estado')) !!}<br/>
    {!! Form::select('estado_user_id', $estados, null, ['class' => 'form-control', 'disabled' => true]) !!}

</div>

<div class="form-group">


    {!! Form::label('email', trans('general.email')) !!}
    {!! Form::text('email', null, ['class'=> 'form-control' ]) !!}

</div>
<div class="form-group">


    {!! Form::label('data_nascimento', trans('general.data_de_nascimento'))!!}
    {!! Form::text('data_nascimento', null, ['class'=> 'form-control' ]) !!}

</div>

<div class="form-group">


    {!! Form::label('num_ident_civil', trans('general.num_ident_civil')) !!}
    {!! Form::text('num_ident_civil', null, ['class'=> 'form-control' ]) !!}

</div>
<div class="form-group">


    {!! Form::label('num_icn','Num_icn:') !!}
    {!! Form::text('num_icn', null, ['class'=> 'form-control' ]) !!}

</div>

<div class="form-group">


    {!! Form::label('telefone', trans('general.telefone')) !!}
    {!! Form::text('telefone', null, ['class'=> 'form-control' ]) !!}

</div>
<div class="form-group">


    {!! Form::label('morada', trans('general.morada')) !!}
    {!! Form::text('morada', null, ['class'=> 'form-control' ]) !!}

</div>
<div class="form-group">


    {!! Form::label('cod_postal', trans('general.cod_postal')) !!}
    {!! Form::text('cod_postal', null, ['class'=> 'form-control' ]) !!}

</div>
<div class="form-group">


    {!! Form::label('localidade1',trans('general.localidade').' 1') !!}
    {!! Form::text('localidade1', null, ['class'=> 'form-control' ]) !!}

</div>
<div class="form-group">


    {!! Form::label('localidade2',trans('general.localidade').' 2')  !!}
    {!! Form::text('localidade2', null, ['class'=> 'form-control' ]) !!}

</div>
<div class="form-group">


    {!! Form::label('regiao', trans('general.regiao')) !!}
    {!! Form::text('regiao', null, ['class'=> 'form-control' ]) !!}

</div>
<div class="form-group">

    {!! Form::label('pais_id', trans('general.pais')) !!}<br/>
    <select disabled name="pais_id" id="pais_id" class="col-sm-6 selectpicker"
            data-live-search="true" title="--">
        @foreach($paises as $p)
            <option value="{{ $p->id }}" {{ (isset($user))?($user->pais_id==$p->id)?'selected':'':'' }}>{{$p->pais}}</option>
        @endforeach
    </select>




    {{--o 'pais_id' é o campo que o blade utiliza para seleccionar a opção que vem da BD - utiliza a chave estrangeira e não a chave da tabela de destino! --}}

</div>


<div class="form-group">


    {!! Form::label('observacoes', trans('general.observacoes'))  !!}
    {!! Form::textarea('observacoes', null, ['class'=> 'form-control' ]) !!}

</div>