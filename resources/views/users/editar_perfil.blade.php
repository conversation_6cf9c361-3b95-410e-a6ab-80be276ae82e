@extends('layouts.app')

@section('titulo','Edição de Dados')
@section('subtitulo','')
@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casaadmin'); ?>"><PERSON><PERSON><PERSON> Criador</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>Edição de Utilizador</span>
        </li>
    </ul>
@endsection

@section('content')

    <div class="content body">
        <div class="row">
            <div class="portlet box blue">
                <div class="portlet-title">
                    <div class="caption">
                        Dados do Utilizador
                    </div>
                </div>
                <div class="portlet-body">
                    @if ($errors->any())

                        <ul class="alert alert-danger">
                            @foreach($errors->all() as $error)
                                <li>{{$error}}</li>
                            @endforeach
                        </ul>

                    @endif
                    {!!  Form::model($user, ['method'=>'PATCH', 'route'=>['user.update',$user->id]]) !!}
                        <input name="origem" type="hidden" value="homecriador">
                        <input name="token_requesttype" type="hidden" value="gyw87rg8RF21189yhfdh">
                    @include('users._form_perfil')


                    <div class="form-group">
                        {!! link_to_route('homecriador','Cancelar',null,['class'=>'btn btn-default col-md-2']) !!}


                        {!! Form::submit('Actualizar registo',['class'=>'btn btn-primary col-md-2']) !!}
                    </div>
                    <br>
                    <br>
                    <br>
                    <br>
                    {!!  Form::close() !!}

                    <div class="portlet box blue">
                        <div class="portlet-title">
                            <div class="caption">
                                {{trans('user.permissoes')}}
                            </div>
                        </div>
                        <div class="portlet-body">
                            <ul>
                                @if($user->isadmin)
                                    <li>{{trans('user.role_admin')}}</li>
                                @endif
                                @if($user->isjuiz)
                                    <li>{{trans('user.role_juiz')}}</li>
                                @endif
                                @if($user->isgestornacional)
                                    <li>{{trans('user.role_gestor_nacional')}}</li>
                                @endif
                            </ul>


                        </div>
                    </div>

                    @if(count($clubesAssociados)>0)

                        <div class="portlet box blue">
                            <div class="portlet-title">
                                <div class="caption">
                                    {{trans('user.associacoes_a_clubes')}}
                                </div>
                            </div>
                            <div class="portlet-body">
                                @foreach ($clubesAssociados as $c)
                                    <strong> {{$c->nome}}</strong><br>

                                @endforeach


                                @endif

                            </div>
                        </div>
                </div>
            </div>


        </div>
    </div>

@endsection

@section('scripts')

@section('scripts')
    <script>
        $(document).ready(function () {

            $('#federsel').change(function () {

                var url = "<?php echo url('ajax_clubes_federacao_admin') ?>/" + $(this).val();
                $.get(url,
                    function (data) {
                        var subcat = $('#clubesel');
                        subcat.empty();
                        $.each(data, function (index, element) {
                            subcat.append("<option value='" + element.id + "'>" + element.nome + "</option>");
                        });
                        subcat.val(0);
                    });
            });


        });


    </script>


@endsection


@endsection
