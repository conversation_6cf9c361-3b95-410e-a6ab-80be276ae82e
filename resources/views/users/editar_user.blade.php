@extends('layouts.app')

@section('titulo','Edição de Dados')
@section('subtitulo','')
@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casaadmin'); ?>"><PERSON><PERSON><PERSON></a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="<?php echo route('adminuserslist'); ?>">Gestão de Utilizadores</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>Edição de Utilizador</span>
        </li>
    </ul>
@endsection



@section('content')

    <div class="content body">
        <div class="row">
            <div class="portlet box blue">
                <div class="portlet-title">
                    <div class="caption">
                        Dados do Utilizador
                    </div>
                </div>
                <div class="portlet-body">
                    @if ($errors->any())

                        <ul class="alert alert-danger">
                            @foreach($errors->all() as $error)
                                <li>{{$error}}</li>
                            @endforeach
                        </ul>

                    @endif
                    {!!  Form::model($user, ['method'=>'PATCH', 'route'=>['user.update',$user->id]]) !!}
                    {{ Form::hidden('origem', $origem) }}
                    @include('users._form')
                    <div class="form-group">


                        {!! Form::label('isadmin','Administrador:') !!}
                        {!! Form::checkbox('isadmin', 'value', null) !!}

                    </div>
                    <div class="form-group">


                        {!! Form::label('isjuiz','Juiz:') !!}
                        {!! Form::checkbox('isjuiz', 'value', null) !!}

                    </div>
                    <div class="form-group">


                        {!! Form::label('isgestornacional','Gestor Nacional:') !!}
                        {!! Form::checkbox('isgestornacional', 'value', null) !!}

                    </div>
                    <div class="form-group">
                        {!! link_to_route($origem,'Cancelar',null,['class'=>'btn btn-default col-md-2']) !!}


                        {!! Form::submit('Actualizar registo',['class'=>'btn btn-primary col-md-2']) !!}
                    </div>
                    <br>
                    <br>
                    <br>
                    <br>
                    {!!  Form::close() !!}

                    @if(count($clubesAssociados)>0)

                        <div class="portlet box blue">
                            <div class="portlet-title">
                                <div class="caption">
                                    Associações a clubes
                                </div>
                            </div>
                            <div class="portlet-body">
                                @foreach ($clubesAssociados as $c)
                                    <strong> {{$c->nome}}</strong><br>

                                @endforeach

                                <a href="<?php echo url('users') . "/" . $user->id . "/editperm" ?>"
                                   class="btn btn-default">Alterar Associaçõe</a><br>


                                @endif

                            </div>
                        </div>
                </div>
            </div>


        </div>
    </div>

@endsection

@section('scripts')

@section('scripts')
    <script>
        $(document).ready(function () {

            $('#federsel').change(function () {

                var url = "<?php echo url('ajax_clubes_federacao_admin') ?>/" + $(this).val();
                $.get(url,
                    function (data) {
                        var subcat = $('#clubesel');
                        subcat.empty();
                        $.each(data, function (index, element) {
                            subcat.append("<option value='" + element.id + "'>" + element.nome + "</option>");
                        });
                        subcat.val(0);
                    });
            });


        });


    </script>


@endsection


@endsection
