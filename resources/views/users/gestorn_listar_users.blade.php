@extends('layouts.app')

@section('titulo',trans('general.gestao_utilizadores'))
@section('subtitulo','')
@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casaadmin'); ?>">{{trans('general.home_gestorn')}}</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>{{trans('general.gestao_utilizadores')}}</span>
        </li>
    </ul>

@endsection

@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-12">
                <div class="portlet box blue">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="fa fa-gift"></i>{{trans('general.criadores')}}
                        </div>

                    </div>
                    <div class="portlet-body">
                        @if(Session::has('flash_message'))
                            <div class="alert alert-success">

                                {{Session::get('flash_message')}}
                                <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;
                                </button>
                            </div>
                        @endif
                        <div style="text-align: left;margin-top: 10px;margin-bottom: 15px;" class="form-group">
                            <a href="{!! route('gestornusercreate') !!}" class="btn btn-primary">{{trans('general.novo_criador')}}</a>
                        </div>


                        <div id="grid2" style="width: 100%; height: 500px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>



@endsection

@section('scripts')
    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <!-- END PAGE LEVEL SCRIPTS -->
    <script>

        var gg;
        $(document).ready(function () {



            w2utils.settings.dataType = 'RESTFULL';
            w2utils.locale('https://raw.githubusercontent.com/vitmalina/w2ui/master/src/locale/pt-br.json');

            gg = $('#grid2').w2grid({
                name: 'grid2',
                header: 'Criadores',
                multiSelect: false,
                multiSearch: true,
                recid: "id",
                show: {
                    toolbarSearch: false,     // hides search button on the toolbar
                    toolbarInput: true,     // hides search input on the toolbar
                    toolbar: true,
                    footer: true,
                    toolbarEdit: true,
                },
                toolbar: {

                },
                searches: [
                    {field: 'stam', caption: 'stam ', type: 'text'},
                    {field: 'name', caption: 'Nome', type: 'text'},
                    {field: 'email', caption: 'E-mail', type: 'text'},
                ],
                columns: [
                    {field: 'id', caption: 'ID', size: '50px', hidden: true},
                    {field: 'pais_id', caption: 'pais_id', size: '50px', hidden: true},
                    {field: 'user_estado_id', caption: 'estado_id', size: '50px', hidden: true},
                    {field: 'stam', caption: 'STAM', size: '30%', sortable: true},
                    {field: 'estado', caption: 'Estado', size: '30%'},
                    {field: 'name', caption: 'Nome', size: '50%', sortable: true},
                    {field: 'email', caption: 'E-mail', size: '30%'},
                    {field: 'pais', caption: 'País', size: '30%'},
                ],
                onEdit: function (event) {
                    var link = '<?php echo url('gestornusers') ?>/' + event['recid'] + '/edit';
                    window.open(link, "_self");
                },
                onRefresh: function (event) {

                    this.toolbar.disable('editPerm');

                },
                onSelect: function (event) {

                    this.toolbar.enable('editPerm');

                },
                onUnselect: function (event) {

                    var sel = this.getSelection();
                    //console.log(sel);
                    if (sel.length == 1) this.toolbar.disable('editPerm');
                },
                onSearch: function (target, data) {
                    for (var s in data.searchData) {
                        data.searchData[s].operator = 'contains';
                    }
                },

            });

            gg.load('{{URL::route('ajaxusersgestorn',['pais_id'=>$pais_id])}}');
            $("#reset_btn").click(function() {
                    $("#estado").val('0');
                    $("#pais_id").val('0');

            });

            $("#pesquisaf").submit(function (e) {
                e.preventDefault();
                var dados = $(this).serializeArray();
                //console.log(dados);

                var pesquisa = [];

                var indice = 0;

                var user_estado_id = 0;
                var pais_id = 0;

                if (dados[0].value != 0) {
                    user_estado_id = dados[0].value;
                    pesquisa[indice] = {field: 'user_estado_id', value: user_estado_id, operator: 'is'};
                    indice++;

                }
                if (dados[1].value != 0) {
                    pais_id = dados[1].value;
                    pesquisa[indice] = {field: 'pais_id', value: pais_id, operator: 'is'};
                    indice++;

                }

                // verificar se apenas a pesquisa livre está activa

                var linka = '<?php echo url('ajax_users_admin') ?>/' + user_estado_id + '/' + pais_id;


                //console.log(linka);
                gg.load(linka);


// TODO : ver esta tralha

                //console.log(pesquisa);

                //gg.search([{field: 'name', value: 'luis', operator: 'contains'}], 'AND');
                //gg.search([ {field: 'estado_id', value: '2', operator: 'contains'}],'AND');
                //gg.search([{field: 'pais_id', value: '13', operator: 'contains'}], 'AND');
                //gg.search(pesquisa, 'OR')


                //para pesquisa multiplas:
                //gg.search([ {field: 'name', value: 'aaa', operator: 'contains'}],'OR');


            });


        });

    </script>

@endsection