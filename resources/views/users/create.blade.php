@extends('layouts.app')

@section('titulo','Listagem de Utilizadores')
@section('subtitulo','')
@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casaadmin'); ?>"><PERSON><PERSON><PERSON></a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="<?php echo route('adminuserslist'); ?>">Lista Utilizadores</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>Criar Utilizador</span>
        </li>
    </ul>

@endsection



@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-10">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">Criar Novo Utilizador</h3>
                    </div>
                    <div class="box-body">
                        @if ($errors->any())

                            <ul class="alert alert-danger">
                                @foreach($errors->all() as $error)
                                    <li>{{$error}}</li>
                                @endforeach
                            </ul>

                        @endif
                        {!! Form::open(['route'=> 'adminuserstore']) !!}
                        {{ Form::hidden('origem', $origem) }}

                        @include('users._form')

                        <div class="form-group">

                            {!! Form::label('federacao_id','Federação:') !!}<br/>
                            {!! Form::select('federacao_id', $federacoes, null, ['class' => 'form-control','id' => 'federsel']) !!}


                        </div>

                        <div class="form-group">

                            {!! Form::label('id','Clube:') !!}<br/>
                            {!! Form::select('clube_id', $clubes, null, ['class' => 'form-control','id' => 'clubesel']) !!}


                        </div>
                        <div class="form-group col-sm-6">


                            {!! Form::label('isadmin','Administrador:') !!}
                            {!! Form::checkbox('isadmin', 'value', null) !!}

                        </div>
                        <div class="form-group col-sm-6">


                            {!! Form::label('isjuiz','Juiz:') !!}
                            {!! Form::checkbox('isjuiz', 'value', null) !!}

                        </div>
                            <div class="form-group col-sm-6">


                                {!! Form::label('isgestorn','Gestor Nacional:') !!}
                                {!! Form::checkbox('isgestorn', 'value', null) !!}

                            </div>
                        <div class="form-group">
                            {!! link_to_route($origem,'Cancelar',null,['class'=>'btn btn-danger col-md-12']) !!}

                        </div>
                        <div class="form-group">

                            {!! Form::submit('Criar Utilizador', ['class'=> 'btn btn-primary form-control']) !!}

                        </div>

                        {!! Form::close() !!}

                    </div>
                </div>

            </div>
        </div>
    </div>



@endsection

@section('scripts')

    <script>


        $(document).ready(function () {
            $('#pais_id').selectpicker({
                size: 10
            });
console.log()
            $('#federsel').change(function () {

                var url = "<?php echo url('ajax_clubes_federacao_admin') ?>/"+$(this).val();
                $.get(url,
                    function (data) {
                        var subcat = $('#clubesel');
                        subcat.empty();
                        $.each(data, function (index, element) {
                            subcat.append("<option value='" + element.id + "'>" + element.nome + "</option>");
                        });
                        subcat.val(0);
                    });
            });

        });



    </script>


@endsection
