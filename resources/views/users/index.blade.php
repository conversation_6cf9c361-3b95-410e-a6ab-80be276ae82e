@extends('layouts.app')


@section('titulo','Gestão de Utilizadores')


@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> Início</a></li>
        <li class="active">Utilizadores</li>
    </ol>
@endsection
@section('content')




    <!-- Main content -->
    <div class="content body">


        <div class="row">
            <button id="bteditar" type="button" class="btn btn-default">Editar</button>
            <button id="btapagar" type="button" class="btn btn-default">Sair</button>

            <div class="col-md-8">
                <div class="box box-solid box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">Activos</h3>
                    </div>
                    <div class="box-body">

                        <table class="table table-striped table-bordered" cellspacing="0" id="dtable">
                            <thead>
                            <tr>
                                <th>id</th>
                                <th>STAM</th>
                                <th>Nome</th>
                                <th>Email</th>
                                <th>menu</th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>

                            </tbody>
                        </table>


                    </div>
                </div>
            </div>
        </div>


    </div>


@endsection

@section('scripts')


    <script>

        $(document).ready(function () {


            var tabela = $('#dtable').DataTable({
                processing: true,
                serverSide: true,
                renderer: "bootstrap",
                ajax: '{!! route('ajaxusers') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'stam', name: 'stam'},
                    {data: 'name', name: 'name'},
                    {data: 'email', name: 'email'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],


            });


        });



    </script>

@endsection