@extends('layouts.minApp')


@section('content')
    <p><strong>Formulário de submissão de problemas e sugestões</strong>
    </p>
    <br>
    <p class="text-center"><h5>Se encontrou algum problema, mensagem de erro ou unicórnios, por favor descreva-nos a situação que nós trataremos de verificar e, se necessário, resolver.</h5></p><br>
    <form id="form_inscrever" action="<?php echo route('enviaremailwebmaster'); ?>" method="post">

        <input type="hidden" name="redirectto" value="eventohomecriador">
        <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
        <div class="form-group">
            <label for="tipo_problema">Tipo de problema</label><br>
            <select class="form-control" name="tipo_problema" id="tipo_problema" class="selectpicker col-md-8" data-live-search="true">
                    <option selected value="Site">Problema Técnico</option>
                    <option value="Sugestão">Sugestão</option>
                    <option value="Outro">Outro tipo</option>
            </select>
        </div>
        <div class="form-group">
            <label for="texto_problema">Descrição</label>
            <textarea class="form-control" rows="10" cols="50" name="texto_problema" id="texto_problema" placeholder="Mensagem aqui"></textarea>
        </div>
        <input type="submit" class="btn btn-primary" value="Enviar">
        <a href="javascript:parent.jQuery.fancybox.close();">
            <button type="button" class="btn btn-default">Cancelar</button>
        </a>
    </form>
@endsection