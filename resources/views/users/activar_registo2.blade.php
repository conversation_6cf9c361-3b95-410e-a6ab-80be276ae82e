<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>FONP</title>

    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">

    <!-- Bootstrap 3.3.6 -->
    <link rel="stylesheet" href="http://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.5.0/css/font-awesome.min.css">

    <!-- Ionicons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">

    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/2.3.3/css/AdminLTE.min.css">

    <!-- iCheck -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/2.3.3/css/skins/_all-skins.min.css">

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

</head>
<body class="hold-transition login-page">
<div class="login-box">
    <div class="login-logo">
        <a href="{{ url('/home') }}"><b>SISCOM </b>{{ trans('general.sistema_de_gestao') }}</a>
    </div>

    <!-- /.login-logo -->
    <div class="login-box-body">


        @if($user=='')

            <p class="login-box-msg"><span style="font-weight: bold;font-size: 26px">{{ trans('general.erro') }}</span><br></p>

            <div class="form-group">
                <div class="alert alert-danger">{{ trans('general.activacao2_texto1') }}</div>
                <br></div>

            <div class="row">
                <!-- /.col -->
                <div class="col-xs-6">
                    <a href="{{url('/activarregisto')}}" class="btn btn-primary btn-block btn-flat">< {{ trans('general.recomecar_processo') }} ></a>
                </div>

            </div>
        @else
            <p class="login-box-msg"><span style="font-weight: bold;font-size: 26px">{{ trans('general.activacao2_texto2') }}</span><br><br>{{ trans('general.activacao2_texto3') }}</p>

            <form action="{{ url('/'.LaravelLocalization::getCurrentLocale().'/activarregisto3') }}" method="post">
                {!! csrf_field() !!}
                {{ Form::hidden('id', $user->id) }}
                <div class="form-group"><strong>STAM: </strong><br>
                    <p>{{$user->stam}}</p></div>
                <div class="form-group"><strong>{{ trans('general.nome') }}: </strong><br>
                    <p>{{$user->name}}</p></div>
                <div class="form-group"><strong>{{ trans('general.morada') }}: </strong><br>
                    <p>{{$user->morada}}</p></div>
                <div class="form-group"><strong>{{ trans('general.cod_postal') }}: </strong><br>
                    <p>{{$user->cod_postal}}</p></div>
                <div class="form-group"><strong>{{ trans('general.localidade') }}: </strong><br>
                    <p>{{$user->localidade1}}</p></div>
                <div class="form-group"><strong>{{ trans('general.telefone') }}: </strong><br>
                    <p>{{$user->telefone}}</p></div>

                <div class="alert alert-warning">{{ trans('general.activacao2_texto4') }}</div>
                <div class="form-group has-feedback{{ $errors->has('email') ? ' has-error' : '' }}">
                    <label for="email">E-mail:</label>
                    <input type="email" class="form-control" value="{{$user->email}}" placeholder="E-mail" name="email">
                    <span class="glyphicon glyphicon-envelope form-control-feedback"></span>
                    @if ($errors->has('email'))
                        <span class="help-block">
                    <strong>{{ $errors->first('email') }}</strong>
                </span>
                    @endif

                </div>
                <div class="form-group has-feedback{{ $errors->has('password') ? ' has-error' : '' }}">
                    <label for="password">Password:</label>
                    <input type="password" class="form-control" placeholder="Password" name="password">
                    <span class="glyphicon glyphicon-lock form-control-feedback"></span>
                    @if ($errors->has('password'))
                        <span class="help-block">
                    <strong>{{ $errors->first('password') }}</strong>
                </span>
                    @endif

                </div>

                <div class="row">
                    <!-- /.col -->
                    <div class="col-xs-6">
                        <a href="{{ url('login') }}">< Login</a>
                    </div>
                    <div class="col-xs-6">
                        <button type="submit" class="btn btn-primary btn-block btn-flat">{{ trans('general.passo_seguinte') }} ></button>
                    </div>
                    <!-- /.col -->
                </div>
            </form>




        @endif


    </div>
    <!-- /.login-box-body -->
</div>
<!-- /.login-box -->

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.0/jquery.min.js"></script>
<script src="http://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/iCheck/1.0.2/icheck.min.js"></script>

<!-- AdminLTE App -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/2.3.3/js/app.min.js"></script>
<script>
    $(function () {
        $('input').iCheck({
            checkboxClass: 'icheckbox_square-blue',
            radioClass: 'iradio_square-blue',
            increaseArea: '20%' // optional
        });
    });
</script>
</body>
</html>
