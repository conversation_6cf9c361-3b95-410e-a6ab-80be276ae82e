@extends('layouts.app')

@section('titulo','Importar Criadores')
@section('subtitulo','')
@section('breadcrumbs')
    <ul class="page-breadcrumb">
        <li>
            <a href="<?php echo route('casaadmin'); ?>"><PERSON><PERSON><PERSON></a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="<?php echo route('adminuserslist'); ?>">Gestão de Utilizadores</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <span>Importação de Utilizadores</span>
        </li>
    </ul>
@endsection



@section('content')

    <div class="content body">
        <div class="row">
            <div class="portlet box blue">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-upload"></i>
                        <span class="caption-subject bold font-yellow-crusta uppercase">{{trans('general.importar')}}</span>
                        <span class="caption-helper">{{trans('general.utilizadores')}}</span>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-xs-12">
                            <div class="mt-element-ribbon bg-grey-steel">
                              nao ultrapassar um maximo de 4000 criadores por excel<br>


                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-4 col-md-4 col-xs-12">

                            <form action="{{ route('importusers') }}"
                                  class="form-horizontal" method="post" enctype="multipart/form-data">
                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                <div class="form-group col-xm-12 col-md-2">

                                    <input type="file" name="import_file"/><br>
                                    <button class="btn btn-primary">{{trans ('general.importar')}}</button>
                                </div>

                            </form>
                            <br>

                        </div>
                        <div class="col-lg-8 col-md-8 col-xs-12">
                            @if(Session::has('flash_message_success'))
                                <div class="alert alert-success col-sm-12">

                                    {{session('flash_message_success')}}
                                    <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;
                                    </button>
                                </div>
                            @endif
                            @if(Session::has('flash_message_error'))
                                <div class="alert alert-danger col-sm-12">

                                    {!! session('flash_message_error') !!}
                                    <button class="close" type="button" data-dismiss="alert" aria-hidden="true">&times;
                                    </button>
                                </div>
                            @endif

                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

@endsection

@section('scripts')

@section('scripts')
    <script>
        $(document).ready(function () {


        });


    </script>


@endsection


@endsection
