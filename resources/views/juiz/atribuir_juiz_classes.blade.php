@extends('layouts.app')

@section('titulo','Atribuição de juizes a Evento')
@section('subtitulo',$evento->nome)
@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casaadmin'); ?>"><i class="fa fa-dashboard"></i> {{trans('clubes.inicio_admin')}}</a></li>
        <li class="active">{{trans('general.juizes')}}</li>
    </ol>
@endsection



@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-12 col-xm-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $evento->nome; ?></h3>
                    </div>
                    <div class="box-body">

                        <form onsubmit="return validarF()" id="form_atribuir" action="<?php echo route('gravaratribuirjuizesclasses')   ?>" method="post">
                            <div class="col-md-5 col-xm-12">
                                <fieldset class="form-group">
                                    <label for="listausers">{{trans('forms.juizes_do_evento')}}</label>
                                    <select class="form-control selectpicker" name="juizid" id="listausers" data-live-search="true">
                                        @foreach($users as $j)
                                            <option value="{{$j->id}}">{{$j->name}} ({{$j->stam}})</option>
                                        @endforeach
                                    </select>
                                </fieldset>
                            </div>
                            <div class="col-sm-1">
                                <fieldset class="form-group">
                                    <label for="listaseccoes">Secções</label>
                                    <select class="form-control selectpicker" name="seccao" id="listaseccoes" title="--">
                                        @foreach($seccoes as $s)
                                            <option value="{{$s->seccao}}">{{$s->seccao}}</option>
                                        @endforeach
                                    </select>
                                </fieldset>
                            </div>
                            <div class="col-sm-6">
                                <fieldset class="form-group">
                                    <label for="listaclasses">Classes</label>

                                    <select class="form-control selectpicker" name="seccaoclasseid" id="selclasse" data-live-search="true" title="--">
                                        @foreach($classes as $cl)
                                            <option value="{{$cl->id}}">{{$cl->seccao}} {{$cl->classe}} - {{$cl->nome}}</option>
                                        @endforeach
                                    </select>
                                </fieldset>
                            </div>
                            <div class="col-sm-10">
                                <input type="hidden" name="eventoid" value="<?php echo $evento->id; ?>">
                                <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                                <br>
                                <input type="submit" class="btn btn-info" value="Atribuir Juiz">
                                <a href="{{URL::route('casa')}}">
                                    <button type="button" class="btn btn-default">{{trans('clubes.sair')}}</button>
                                </a>
                            </div>
                        </form>

                    </div>
                </div>

            </div>


        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{trans('general.juizes_atribuidos')}}</h3>
                    </div>
                    <div class="box-body">

                        <table class="table table-striped table-bordered" cellspacing="0" id="tabatrib">
                            <thead>
                            <tr>
                                <th>id</th>
                                <th>STAM</th>
                                <th>{{trans('general.juiz')}}</th>
                                <th>{{trans('general.seccao')}}</th>
                                <th>{{trans('general.seccao_classe')}}</th>
                                <th></th>
                            </tr>
                            </thead>

                        </table>
                    </div>
                </div>
            </div>
        </div>

    </div>


    </div>
    {{--

    {{ Form::open(array('url' => 'atribuir_juiz')) }}

    @if (count($juizes) > 0)

        <div class="form-group">
            {!! Form::label(trans('general.atribuir_juizes')) !!}
        </div>
        <div>
            {!! Form::select('aJuizes[]', $juizes, null,
            array('multiple' => true)) !!}
        </div>
        <div>
            {!! Form::submit(trans('general.Submeter')) !!}
        </div>

    @endif

    {{ Form::close() }}--}}
@endsection

@section('scripts')


    <script>

        function validarF() {


            if ($("#listaseccoes").val() != '' && $("#selclasse").val() != '') {

                alert({{trans('forms.apenas_seccao_classe')}});
                return false;
            }

            if ($("#listaseccoes").val() == '' && $("#selclasse").val() == '') {

                alert({{trans('forms.erro_escolher_seccao_classe')}});
                return false;
            }


        }

        $(document).ready(function () {


            $("#listaseccoes").change(function () {
                $("#selclasse").selectpicker('val', '');
            });

            $("#selclasse").change(function () {
                $("#listaseccoes").selectpicker('val', '');
            });


            var sub = '{!! route('gravaratribuirjuizesclasses') !!}}';



            $('#tabatrib').on('draw.dt', function () {
                $("button[name^='remover-']").click(function () {


                    var reg = $(this).data("regressa");
                    if (confirm({{trans('forms.posso_remover_este_juiz')}})) {

                        $.ajax({
                            url: reg,
                            cache: false
                        })
                                .done(function (html) {
                                    location.reload(true);
                                });
                    } else {
                        return false;
                    }


                });
            });


        });

    </script>

@endsection
