@extends('layouts.app')

@section('titulo','Atribuição de juizes a Evento')
@section('subtitulo',$evento->nome)
@section('breadcrumbs')
    <ol class="breadcrumb">
        <li><a href="<?php echo route('casa'); ?>"><i class="fa fa-dashboard"></i> {{trans('clubes.inicio_admin')}}</a></li>
        <li class="active">{{trans('general.juizes')}}</li>
    </ol>
@endsection
@section('content')

    <div class="content body">
        <div class="row">
            <div class="col-sm-10">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">------------------</h3>
                    </div>
                    <div class="box-body">
                        <form id="form_remover" action="" method="post">
                        {{ csrf_field() }}

                            <div class="col-sm-3">
                                <fieldset class="form-group">
                                    <label for="listausers">Juízes</label>
                                    <select multiple class="form-control" id="listausers">
                                        @foreach($users as $j)
                                            <option value="{{$j->id}}">{{$j->name}} ({{$j->email}})</option>
                                        @endforeach
                                    </select>
                                </fieldset>
                            </div>
                            <div class="col-sm-10">
                                <input type="hidden" name="eventoid" value="1">
                                <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
                                <br>
                                <input type="submit" class="btn btn-info" value="Remover Juiz">
                                <a href="{{URL::route('casa')}}">
                                    <button type="button" class="btn btn-default">Sair</button>
                                </a>
                            </div>
                        </form>

                    </div>
                </div>

            </div>
            <div class="row">
                <div class="col-sm-3">
                </div>
                <div class="col-sm-3">

                </div>
            </div>

        </div>

    </div>

    </div>
@endsection