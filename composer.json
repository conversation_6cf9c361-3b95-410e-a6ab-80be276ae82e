{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": ">=5.6.4", "codedge/laravel-fpdf": "^1.0", "httpoz/roles": "^2.1", "infyomlabs/metronic-templates": "dev-master", "laracasts/flash": "^2.0", "laravel/framework": "5.5.*", "laravel/tinker": "~1.0", "laravelcollective/html": "^5.4.0", "maatwebsite/excel": "~2.1.0", "mcamara/laravel-localization": "1.2.*", "phpoffice/phpspreadsheet": "^1.5", "simplesoftwareio/simple-qrcode": "~4", "themsaid/laravel-langman-gui": "^0.1.1", "vsch/laravel-translation-manager": "~2.4", "yajra/laravel-datatables-oracle": "^8.0"}, "require-dev": {"fzaninotto/faker": "~1.4", "laravel/homestead": "^6.0", "mockery/mockery": "0.9.*", "phpunit/phpunit": "~6.0", "filp/whoops": "~2.0"}, "autoload": {"classmap": ["database", "app/Models"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-root-package-install": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall", "php artisan optimize"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "php artisan vendor:publish --provider=\"Vsch\\TranslationManager\\ManagerServiceProvider\" --tag=public --force", "php artisan optimize"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}}