- edit file generate.php
- edit line 8 and change to the eventoId you want qr codes to be generate for
nano generate.php
- save file
- ssh to terminal
- go to /public_html/fonpadmin/_scripts/qrcode-generate
- type:
php generate.php
- it will generate all qr code image files to folder 'images'
- run:
./move-qrcodes.sh [eventoID here]
- it will move all generated qr codes to the public directory where they belong (../../public/qrcodes/$DIR)
- login to fonpadmin (PTA:1)
- go to the event
- go to listagens
- use list 14. etiquetas definitivas