if [ $# -eq 0 ]; then
    echo "Please provide corrrect Evento ID as argument:";
    echo "./move-qrcodes.sh [eventId]";
    exit 1
fi

echo "Checking if directory exists";

DIR="../../public/qrcodes/$1";

if [ -d "$DIR" ]; then
   echo "'$DIR' found";
   # todo remove destination images
else
   echo  "'$DIR' not found! Creating...";
   mkdir -p "../../public/qrcodes/$DIR"
fi

echo "Moving files for Evento Id: $1";

mv images/* $DIR
