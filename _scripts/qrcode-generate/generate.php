<?php

//load the ar library
include 'phpqrcode/qrlib.php';

// EVENT STUFF
// ALTERAR ISTO SEMPRE QUE SE GERE PARA NOVO EVENTO!!!!!!!!!!!!!!
$eventoId = 300;

// QR CODE STUFF
$url = "https://conforni.com/fonpadmin/qrcode_ficha_julgamento/".$eventoId."/";

//other parameters
$ecc = 'H';
$pixel_size = 15;
$frame_size = 0;

// DATABASE STUFF
include 'dbinfo-remote.php';

$conn = new mysqli($servername, $username, $password, $database);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$sql = "SELECT
eip.numgaiola,
  if(RIGHT(eip.numgaiola,1) IN ('A', 'B', 'C', 'D'),RIGHT(eip.numgaiola,1), ' ') as letragaiola,
if(RIGHT(eip.numgaiola,1) IN ('A', 'B', 'C', 'D'),LEFT(eip.numgaiola, LENGTH(eip.numgaiola) - 1) , numgaiola) as numerogaiola
FROM
eventos_inscricoes_passaros AS eip
INNER JOIN eventos_inscricoes AS ei ON eip.inscricao_id = ei.id
WHERE
ei.evento_id = " . $eventoId . "
ORDER BY numgaiola ASC,
letragaiola ASC
";

$count = 1;

$result = $conn->query($sql);

// delete files from images directory
$files = glob('images/*');
foreach($files as $file) {
    if(is_file($file))
    {
        unlink($file);
    }
}

echo "Total cages: ".mysqli_num_rows($result). "\n";

while ($row = $result->fetch_assoc()) {
    $numgaiola = $row['numgaiola'];

    $file = 'images/' . $numgaiola . '.png';
    $text = $url . $numgaiola;

    // Generates QR Code and Save as PNG
    QRcode::png($text, $file, $ecc, $pixel_size, $frame_size);

    if ($count % 500 == 0) {
        echo "Generated " . $count . "\n";
    }
    $count++;
}

$conn->close();
?>
