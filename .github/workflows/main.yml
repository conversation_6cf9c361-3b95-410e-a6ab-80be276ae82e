name: Deploy via git-ftp
on: push
jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@master
    - name: git-ftp push
      uses: sebastianpopp/git-ftp-action@releases/v2
      with:
        url: "ftp://ftp.conforni.com/public_html/fonpadmin_dev/"
        user: ${{ secrets.FTP_USERNAME }}
        password: ${{ secrets.FTP_PASSWORD }}
        syncroot: .
        