/**
 * Bootstrap Table Afrikaans translation
 * Author: <PERSON> <<EMAIL>>
 */
(function ($) {
    'use strict';

    $.fn.bootstrapTable.locales['af-ZA'] = {
        formatLoadingMessage: function () {
            return 'Besig om te laai, wag asseblief ...';
        },
        formatRecordsPerPage: function (pageNumber) {
            return pageNumber + ' rekords per bladsy';
        },
        formatShowingRows: function (pageFrom, pageTo, totalRows) {
            return 'Resultate ' + pageFrom + ' tot ' + pageTo + ' van ' + totalRows + ' rye';
        },
        formatSearch: function () {
            return 'Soek';
        },
        formatNoMatches: function () {
            return 'Geen rekords gevind nie';
        },
        formatPaginationSwitch: function () {
            return 'Wys/verberg bladsy nummering';
        },
        formatRefresh: function () {
            return 'Herlaai';
        },
        formatToggle: function () {
            return 'Wissel';
        },
        formatColumns: function () {
            return 'Kolomme';
        }
    };

    $.extend($.fn.bootstrapTable.defaults, $.fn.bootstrapTable.locales['af-ZA']);

})(jQuery);
