/*
* bootstrap-table - v1.11.1 - 2017-02-22
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";var b=a.fn.bootstrapTable.utils.sprintf;a.extend(a.fn.bootstrapTable.defaults,{stickyHeader:!1});var c=a.fn.bootstrapTable.Constructor,d=c.prototype.initHeader;c.prototype.initHeader=function(){function c(b){var c=b.data,d=c.find("thead").attr("id");if(c.length<1||a("#"+h).length<1)return a(window).off("resize."+h),a(window).off("scroll."+h),void c.closest(".fixed-table-container").find(".fixed-table-body").off("scroll."+h);var g="0";f.options.stickyHeaderOffsetY&&(g=f.options.stickyHeaderOffsetY.replace("px",""));var i=a(window).scrollTop(),m=a("#"+k).offset().top-g,n=a("#"+l).offset().top-g-a("#"+d).css("height").replace("px","");if(i>m&&n>=i){a.each(f.$stickyHeader.find("tr").eq(0).find("th"),function(b,c){a(c).css("min-width",a("#"+d+" tr").eq(0).find("th").eq(b).css("width"))}),a("#"+j).removeClass("hidden").addClass("fix-sticky fixed-table-container"),a("#"+j).css("top",g+"px");var o=a('<div style="position:absolute;width:100%;overflow-x:hidden;" />');a("#"+j).html(o.append(f.$stickyHeader)),e(b)}else a("#"+j).removeClass("fix-sticky").addClass("hidden")}function e(b){var c=b.data,d=c.find("thead").attr("id");a("#"+j).css("width",+c.closest(".fixed-table-body").css("width").replace("px","")+1),a("#"+j+" thead").parent().scrollLeft(Math.abs(a("#"+d).position().left))}var f=this;if(d.apply(this,Array.prototype.slice.apply(arguments)),this.options.stickyHeader){var g=this.$tableBody.find("table"),h=g.attr("id"),i=g.attr("id")+"-sticky-header",j=i+"-sticky-header-container",k=i+"_sticky_anchor_begin",l=i+"_sticky_anchor_end";g.before(b('<div id="%s" class="hidden"></div>',j)),g.before(b('<div id="%s"></div>',k)),g.after(b('<div id="%s"></div>',l)),g.find("thead").attr("id",i),this.$stickyHeader=a(a("#"+i).clone(!0,!0)),this.$stickyHeader.removeAttr("id"),a(window).on("resize."+h,g,c),a(window).on("scroll."+h,g,c),g.closest(".fixed-table-container").find(".fixed-table-body").on("scroll."+h,g,e),this.$el.on("all.bs.table",function(){f.$stickyHeader=a(a("#"+i).clone(!0,!0)),f.$stickyHeader.removeAttr("id")})}}}(jQuery);