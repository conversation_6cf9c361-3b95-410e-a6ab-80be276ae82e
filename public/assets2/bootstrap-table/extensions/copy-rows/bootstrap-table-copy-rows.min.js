/*
* bootstrap-table - v1.11.1 - 2017-02-22
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";var b=a.fn.bootstrapTable.utils.calculateObjectValue,c=(a.fn.bootstrapTable.utils.sprintf,function(b){var c=document.createElement("textarea");a(c).html(b),document.body.appendChild(c),c.select();try{document.execCommand("copy")}catch(d){console.log("Oops, unable to copy")}a(c).remove()});a.extend(a.fn.bootstrapTable.defaults,{copyBtn:!1,copyWHiddenBtn:!1,copyDelemeter:", "}),a.fn.bootstrapTable.methods.push("copyColumnsToClipboard","copyColumnsToClipboardWithHidden");var d=a.fn.bootstrapTable.Constructor,e=d.prototype.initToolbar;d.prototype.initToolbar=function(){e.apply(this,Array.prototype.slice.apply(arguments));var a=this,b=this.$toolbar.find(">.btn-group");if(this.options.clickToSelect||this.options.singleSelect){if(this.options.copyBtn){var c="<button class='btn btn-default' id='copyBtn'><span class='glyphicon glyphicon-copy icon-pencil'></span></button>";b.append(c),b.find("#copyBtn").click(function(){a.copyColumnsToClipboard()})}if(this.options.copyWHiddenBtn){var d="<button class='btn btn-default' id='copyWHiddenBtn'><span class='badge'><span class='glyphicon glyphicon-copy icon-pencil'></span></span></button>";b.append(d),b.find("#copyWHiddenBtn").click(function(){a.copyColumnsToClipboardWithHidden()})}}},d.prototype.copyColumnsToClipboard=function(){var d=this,e="",f=this.options.copyDelemeter;a.each(d.getSelections(),function(c,g){a.each(d.options.columns[0],function(a,h){"state"!==h.field&&"RowNumber"!==h.field&&h.visible&&(null!==g[h.field]&&(e+=b(h,d.header.formatters[a],[g[h.field],g,c],g[h.field])),e+=f)}),e+="\r\n"}),c(e)},d.prototype.copyColumnsToClipboardWithHidden=function(){var d=this,e="",f=this.options.copyDelemeter;a.each(d.getSelections(),function(c,g){a.each(d.options.columns[0],function(a,h){"state"!=h.field&&"RowNumber"!==h.field&&(null!==g[h.field]&&(e+=b(h,d.header.formatters[a],[g[h.field],g,c],g[h.field])),e+=f)}),e+="\r\n"}),c(e)}}(jQuery);