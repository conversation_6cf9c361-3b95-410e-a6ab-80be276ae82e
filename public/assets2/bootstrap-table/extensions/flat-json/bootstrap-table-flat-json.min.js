/*
* bootstrap-table - v1.11.1 - 2017-02-22
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";var b=function(b,c){function d(b,f){if(Object(b)!==b)e[f]=b;else if(a.isArray(b))for(var g=0,h=b.length;h>g;g++)d(b[g],f?f+c.options.flatSeparator+g:""+g),0==h&&(e[f]=[]);else{var i=!0;for(var j in b)i=!1,d(b[j],f?f+c.options.flatSeparator+j:j);i&&(e[f]={})}}var e={};return d(b,""),e},c=function(c,d){var e=[];return a.each(a.isArray(c)?c:[c],function(a,c){e.push(b(c,d))}),e};a.extend(a.fn.bootstrapTable.defaults,{flat:!1,flatSeparator:"."});var d=a.fn.bootstrapTable.Constructor,e=d.prototype.initData;d.prototype.initData=function(a,b){this.options.flat&&(a=c(a?a:this.options.data,this)),e.apply(this,[a,b])}}(jQuery);