/*
* bootstrap-table - v1.11.1 - 2017-02-22
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";var b=a.fn.bootstrapTable.utils.sprintf,c=function(a){a.initHeader(),a.initSearch(),a.initPagination(),a.initBody()};a.extend(a.fn.bootstrapTable.defaults,{showToggleBtn:!1,multiToggleDefaults:[]}),a.fn.bootstrapTable.methods.push("hideAllColumns","showAllColumns");var d=a.fn.bootstrapTable.Constructor,e=d.prototype.initToolbar;d.prototype.initToolbar=function(){e.apply(this,Array.prototype.slice.apply(arguments));var a=this,b=this.$toolbar.find(">.btn-group");if("string"==typeof this.options.multiToggleDefaults&&(this.options.multiToggleDefaults=JSON.parse(this.options.multiToggleDefaults)),this.options.showToggleBtn&&this.options.showColumns){var c="<button class='btn btn-default hidden' id='showAllBtn'><span class='glyphicon glyphicon-resize-full icon-zoom-in'></span></button>",d="<button class='btn btn-default' id='hideAllBtn'><span class='glyphicon glyphicon-resize-small icon-zoom-out'></span></button>";b.append(c+d),b.find("#showAllBtn").click(function(){a.showAllColumns(),b.find("#hideAllBtn").toggleClass("hidden"),b.find("#showAllBtn").toggleClass("hidden")}),b.find("#hideAllBtn").click(function(){a.hideAllColumns(),b.find("#hideAllBtn").toggleClass("hidden"),b.find("#showAllBtn").toggleClass("hidden")})}},d.prototype.hideAllColumns=function(){var d=this,e=d.options.multiToggleDefaults;a.each(this.columns,function(a,c){if(-1==e.indexOf(c.field)&&c.switchable){c.visible=!1;var f=d.$toolbar.find(".keep-open input").prop("disabled",!1);f.filter(b('[value="%s"]',a)).prop("checked",!1)}}),c(d)},d.prototype.showAllColumns=function(){var d=this;a.each(this.columns,function(a,c){c.switchable&&(c.visible=!0);var e=d.$toolbar.find(".keep-open input").prop("disabled",!1);e.filter(b('[value="%s"]',a)).prop("checked",!0)}),c(d),d.toggleColumn(0,d.columns[0].visible,!1)}}(jQuery);