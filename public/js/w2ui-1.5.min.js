/* w2ui 1.5 (c) http://w2ui.com, <EMAIL> */
var w2ui=w2ui||{},w2obj=w2obj||{},w2utils=function(g){var e={};return{version:"1.5",settings:{locale:"en-us",dateFormat:"m/d/yyyy",timeFormat:"hh:mi pm",datetimeFormat:"m/d/yyyy|hh:mi pm",currencyPrefix:"$",currencySuffix:"",currencyPrecision:2,groupSymbol:",",decimalSymbol:".",shortmonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],fullmonths:["January","February","March","April","May","June","July","August","September","October","November","December"],shortdays:["M","T","W","T","F","S","S"],fulldays:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],weekStarts:"M",dataType:"HTTPJSON",phrases:{},dateStartYear:1950,dateEndYear:2030,macButtonOrder:!1},isBin:function(e){return/^[0-1]+$/.test(e)},isInt:c,isFloat:function(e){"string"==typeof e&&(e=e.replace(/\s+/g,"").replace(w2utils.settings.groupSymbol,"").replace(w2utils.settings.decimalSymbol,"."));return("number"==typeof e||"string"==typeof e&&""!==e)&&!isNaN(Number(e))},isMoney:function(e){var t=w2utils.settings,i=new RegExp("^"+(t.currencyPrefix?"\\"+t.currencyPrefix+"?":"")+"[-+]?"+(t.currencyPrefix?"\\"+t.currencyPrefix+"?":"")+"[0-9]*[\\"+t.decimalSymbol+"]?[0-9]+"+(t.currencySuffix?"\\"+t.currencySuffix+"?":"")+"$","i");"string"==typeof e&&(e=e.replace(new RegExp(t.groupSymbol,"g"),""));return"object"!=typeof e&&""!==e&&i.test(e)},isHex:function(e){return/^(0x)?[0-9a-fA-F]+$/.test(e)},isAlphaNumeric:function(e){return/^[a-zA-Z0-9_-]+$/.test(e)},isEmail:function(e){return/^[a-zA-Z0-9._%\-+]+@[а-яА-Яa-zA-Z0-9.-]+\.[а-яА-Яa-zA-Z]+$/.test(e)},isIpAddress:function(e){return new RegExp("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$").test(e)},isDate:function(e,t,i){if(!e)return!1;var s,n,l,o="Invalid Date";null==t&&(t=w2utils.settings.dateFormat);if("function"==typeof e.getFullYear)l=e.getFullYear(),s=e.getMonth()+1,n=e.getDate();else if(parseInt(e)==e&&0<parseInt(e))e=new Date(parseInt(e)),l=e.getFullYear(),s=e.getMonth()+1,n=e.getDate();else{if(e=String(e),new RegExp("mon","ig").test(t)){t=t.replace(/month/gi,"m").replace(/mon/gi,"m").replace(/dd/gi,"d").replace(/[, ]/gi,"/").replace(/\/\//g,"/").toLowerCase(),e=e.replace(/[, ]/gi,"/").replace(/\/\//g,"/").toLowerCase();for(var a=0,r=w2utils.settings.fullmonths.length;a<r;a++){var d=w2utils.settings.fullmonths[a];e=e.replace(new RegExp(d,"ig"),parseInt(a)+1).replace(new RegExp(d.substr(0,3),"ig"),parseInt(a)+1)}}var u=e.replace(/-/g,"/").replace(/\./g,"/").toLowerCase().split("/"),t=t.replace(/-/g,"/").replace(/\./g,"/").toLowerCase();"mm/dd/yyyy"===t&&(s=u[0],n=u[1],l=u[2]),"m/d/yyyy"===t&&(s=u[0],n=u[1],l=u[2]),"dd/mm/yyyy"===t&&(s=u[1],n=u[0],l=u[2]),"d/m/yyyy"===t&&(s=u[1],n=u[0],l=u[2]),"yyyy/dd/mm"===t&&(s=u[2],n=u[1],l=u[0]),"yyyy/d/m"===t&&(s=u[2],n=u[1],l=u[0]),"yyyy/mm/dd"===t&&(s=u[1],n=u[2],l=u[0]),"yyyy/m/d"===t&&(s=u[1],n=u[2],l=u[0]),"mm/dd/yy"===t&&(s=u[0],n=u[1],l=u[2]),"m/d/yy"===t&&(s=u[0],n=u[1],l=parseInt(u[2])+1900),"dd/mm/yy"===t&&(s=u[1],n=u[0],l=parseInt(u[2])+1900),"d/m/yy"===t&&(s=u[1],n=u[0],l=parseInt(u[2])+1900),"yy/dd/mm"===t&&(s=u[2],n=u[1],l=parseInt(u[0])+1900),"yy/d/m"===t&&(s=u[2],n=u[1],l=parseInt(u[0])+1900),"yy/mm/dd"===t&&(s=u[1],n=u[2],l=parseInt(u[0])+1900),"yy/m/d"===t&&(s=u[1],n=u[2],l=parseInt(u[0])+1900)}if(!c(l))return!1;if(!c(s))return!1;if(!c(n))return!1;if(l=+l,s=+s,n=+n,(o=new Date(l,s-1,n)).setFullYear(l),null==s)return!1;if("Invalid Date"===String(o))return!1;if(o.getMonth()+1!==s||o.getDate()!==n||o.getFullYear()!==l)return!1;return!0!==i||o},isTime:function(e,t){if(null==e)return!1;var i,s,n;e=(e=String(e)).toUpperCase(),s=0<=e.indexOf("AM");var l=(n=0<=e.indexOf("PM"))||s;i=l?12:24;e=e.replace("AM","").replace("PM","");var o=(e=g.trim(e)).split(":"),a=parseInt(o[0]||0),r=parseInt(o[1]||0),e=parseInt(o[2]||0);if((!l||1!==o.length)&&2!==o.length&&3!==o.length)return!1;if(""===o[0]||a<0||i<a||!this.isInt(o[0])||2<o[0].length)return!1;if(1<o.length&&(""===o[1]||r<0||59<r||!this.isInt(o[1])||2!==o[1].length))return!1;if(2<o.length&&(""===o[2]||e<0||59<e||!this.isInt(o[2])||2!==o[2].length))return!1;if(!l&&i===a&&(0!==r||0!==e))return!1;if(l&&1===o.length&&0===a)return!1;if(!0!==t)return!0;n&&12!==a&&(a+=12);s&&12===a&&(a+=12);return{hours:a,minutes:r,seconds:e}},isDateTime:function(e,t,i){if("function"==typeof e.getFullYear)return!0!==i||e;var s=parseInt(e);if(s===e)return!(s<0)&&(!0!==i||new Date(s));s=String(e).indexOf(" ");{if(s<0)return!(String(e).indexOf("T")<0||"Invalid Date"==String(new Date(e)))&&(!0!==i||new Date(e));t=(t=null==t?w2utils.settings.datetimeFormat:t).split("|"),s=[e.substr(0,s),e.substr(s).trim()];t[0]=t[0].trim(),t[1]&&(t[1]=t[1].trim());t=w2utils.isDate(s[0],t[0],!0),s=w2utils.isTime(s[1],!0);return!1!==t&&!1!==s&&(!0!==i||(t.setHours(s.hours),t.setMinutes(s.minutes),t.setSeconds(s.seconds),t))}},age:function(e){if(""===e||null==e)return"";i="function"==typeof e.getFullYear?e:parseInt(e)==e&&0<parseInt(e)?new Date(parseInt(e)):new Date(e);if("Invalid Date"===String(i))return"";var t=((new Date).getTime()-i.getTime())/1e3,e="",i="";t<0?(e=0,i="sec"):t<60?(e=Math.floor(t),i="sec",t<0&&(e=0,i="sec")):t<3600?(e=Math.floor(t/60),i="min"):t<86400?(e=Math.floor(t/60/60),i="hour"):t<2592e3?(e=Math.floor(t/24/60/60),i="day"):t<31536e3?(e=Math.floor(t/30/24/60/60*10)/10,i="month"):t<126144e3?(e=Math.floor(t/365/24/60/60*10)/10,i="year"):126144e3<=t&&(e=Math.floor(t/365.25/24/60/60*10)/10,i="year");return e+" "+i+(1<e?"s":"")},interval:function(e){var t="";t=e<1e3?"< 1 sec":e<6e4?Math.floor(e/1e3)+" secs":e<36e5?Math.floor(e/6e4)+" mins":e<864e5?Math.floor(e/36e5*10)/10+" hours":e<2628e6?Math.floor(e/864e5*10)/10+" days":e<31536e6?Math.floor(e/2628e6*10)/10+" months":Math.floor(e/31536e5)/10+" years";return t},date:function(e){if(""===e||null==e||"object"==typeof e&&!e.getMonth)return"";var t=new Date(e);w2utils.isInt(e)&&(t=new Date(Number(e)));if("Invalid Date"===String(t))return"";var i=w2utils.settings.shortmonths,s=new Date,n=new Date;n.setTime(n.getTime()-864e5);var l=i[t.getMonth()]+" "+t.getDate()+", "+t.getFullYear(),e=i[s.getMonth()]+" "+s.getDate()+", "+s.getFullYear(),s=i[n.getMonth()]+" "+n.getDate()+", "+n.getFullYear(),i=t.getHours()-(12<t.getHours()?12:0)+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()+" "+(12<=t.getHours()?"pm":"am"),n=t.getHours()-(12<t.getHours()?12:0)+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()+":"+(t.getSeconds()<10?"0":"")+t.getSeconds()+" "+(12<=t.getHours()?"pm":"am"),t=l;l==e&&(t=i);l==s&&(t=w2utils.lang("Yesterday"));return'<span title="'+l+" "+n+'">'+t+"</span>"},formatSize:function(e){if(!w2utils.isFloat(e)||""===e)return"";if(0===(e=parseFloat(e)))return 0;var t=parseInt(Math.floor(Math.log(e)/Math.log(1024)));return(Math.floor(e/Math.pow(1024,t)*10)/10).toFixed(0===t?0:1)+" "+(["Bt","KB","MB","GB","TB","PB","EB","ZB"][t]||"??")},formatNumber:function(e,t,i){if(null==e||""===e||"object"==typeof e)return"";i={minimumFractionDigits:t,maximumFractionDigits:t,useGrouping:i};(null==t||t<0)&&(i.minimumFractionDigits=0,i.maximumFractionDigits=20);return parseFloat(e).toLocaleString(w2utils.settings.locale,i)},formatDate:function(e,t){t=t||this.settings.dateFormat;if(""===e||null==e||"object"==typeof e&&!e.getMonth)return"";var i=new Date(e);w2utils.isInt(e)&&(i=new Date(Number(e)));if("Invalid Date"===String(i))return"";var s=i.getFullYear(),e=i.getMonth(),i=i.getDate();return t.toLowerCase().replace("month",w2utils.settings.fullmonths[e]).replace("mon",w2utils.settings.shortmonths[e]).replace(/yyyy/g,("000"+s).slice(-4)).replace(/yyy/g,("000"+s).slice(-4)).replace(/yy/g,("0"+s).slice(-2)).replace(/(^|[^a-z$])y/g,"$1"+s).replace(/mm/g,("0"+(e+1)).slice(-2)).replace(/dd/g,("0"+i).slice(-2)).replace(/th/g,1==i?"st":"th").replace(/th/g,2==i?"nd":"th").replace(/th/g,3==i?"rd":"th").replace(/(^|[^a-z$])m/g,"$1"+(e+1)).replace(/(^|[^a-z$])d/g,"$1"+i)},formatTime:function(e,t){w2utils.settings.shortmonths,w2utils.settings.fullmonths;t=t||this.settings.timeFormat;if(""===e||null==e||"object"==typeof e&&!e.getMonth)return"";var i=new Date(e);w2utils.isInt(e)&&(i=new Date(Number(e)));w2utils.isTime(e)&&(l=w2utils.isTime(e,!0),(i=new Date).setHours(l.hours),i.setMinutes(l.minutes));if("Invalid Date"===String(i))return"";var s="am",n=i.getHours(),e=i.getHours(),l=i.getMinutes(),i=i.getSeconds();l<10&&(l="0"+l);i<10&&(i="0"+i);-1===t.indexOf("am")&&-1===t.indexOf("pm")||(12<=n&&(s="pm"),12<n&&(n-=12),0===n&&(n=12));return t.toLowerCase().replace("am",s).replace("pm",s).replace("hhh",n<10?"0"+n:n).replace("hh24",e<10?"0"+e:e).replace("h24",e).replace("hh",n).replace("mm",l).replace("mi",l).replace("ss",i).replace(/(^|[^a-z$])h/g,"$1"+n).replace(/(^|[^a-z$])m/g,"$1"+l).replace(/(^|[^a-z$])s/g,"$1"+i)},formatDateTime:function(e,t){var i;if(""===e||null==e||"object"==typeof e&&!e.getMonth)return"";"string"!=typeof t?i=[this.settings.dateFormat,this.settings.timeFormat]:((i=t.split("|"))[0]=i[0].trim(),i[1]=1<i.length?i[1].trim():this.settings.timeFormat);"h12"===i[1]&&(i[1]="h:m pm");"h24"===i[1]&&(i[1]="h24:m");return this.formatDate(e,i[0])+" "+this.formatTime(e,i[1])},stripTags:function(e){if(null==e)return e;switch(typeof e){case"number":break;case"string":e=String(e).replace(/<(?:[^>=]|='[^']*'|="[^"]*"|=[^'"][^\s>]*)*>/gi,"");break;case"object":if(Array.isArray(e)){e=g.extend(!0,[],e);for(var t=0;t<e.length;t++)e[t]=this.stripTags(e[t])}else for(var t in e=g.extend(!0,{},e))e[t]=this.stripTags(e[t])}return e},encodeTags:i,decodeTags:function(e){if(null==e)return e;switch(typeof e){case"number":break;case"string":e=String(e).replace(/&gt;/g,">").replace(/&lt;/g,"<").replace(/&quot;/g,'"').replace(/&amp;/g,"&");break;case"object":if(Array.isArray(e)){e=g.extend(!0,[],e);for(var t=0;t<e.length;t++)e[t]=this.decodeTags(e[t])}else for(var t in e=g.extend(!0,{},e))e[t]=this.decodeTags(e[t])}return e},escapeId:function(e){return""===e||null==e?"":String(e).replace(/([;&,\.\+\*\~'`:"\!\^#$%@\[\]\(\)=<>\|\/? {}\\])/g,"\\$1")},base64encode:function(e){var t,i,s,n,l,o,a="",r=0,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";e=function(e){e=String(e).replace(/\r\n/g,"\n");for(var t="",i=0;i<e.length;i++){var s=e.charCodeAt(i);s<128?t+=String.fromCharCode(s):(127<s&&s<2048?t+=String.fromCharCode(s>>6|192):(t+=String.fromCharCode(s>>12|224),t+=String.fromCharCode(s>>6&63|128)),t+=String.fromCharCode(63&s|128))}return t}(e);for(;r<e.length;)o=e.charCodeAt(r++),t=e.charCodeAt(r++),i=e.charCodeAt(r++),s=o>>2,n=(3&o)<<4|t>>4,l=(15&t)<<2|i>>6,o=63&i,isNaN(t)?l=o=64:isNaN(i)&&(o=64),a=a+d.charAt(s)+d.charAt(n)+d.charAt(l)+d.charAt(o);return a},base64decode:function(e){var t,i,s,n,l,o="",a=0,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");for(;a<e.length;)i=r.indexOf(e.charAt(a++)),s=r.indexOf(e.charAt(a++)),n=r.indexOf(e.charAt(a++)),l=r.indexOf(e.charAt(a++)),t=i<<2|s>>4,i=(15&s)<<4|n>>2,s=(3&n)<<6|l,o+=String.fromCharCode(t),64!==n&&(o+=String.fromCharCode(i)),64!==l&&(o+=String.fromCharCode(s));return o=function(e){var t,i,s="",n=0,l=0;for(;n<e.length;)(l=e.charCodeAt(n))<128?(s+=String.fromCharCode(l),n++):191<l&&l<224?(t=e.charCodeAt(n+1),s+=String.fromCharCode((31&l)<<6|63&t),n+=2):(t=e.charCodeAt(n+1),i=e.charCodeAt(n+2),s+=String.fromCharCode((15&l)<<12|(63&t)<<6|63&i),n+=3);return s}(o)},md5:function(e){var l=0;function t(e){return o(a(n(e),8*e.length))}function i(e){for(var t,i=l?"0123456789ABCDEF":"0123456789abcdef",s="",n=0;n<e.length;n++)t=e.charCodeAt(n),s+=i.charAt(t>>>4&15)+i.charAt(15&t);return s}function s(e){for(var t,i,s="",n=-1;++n<e.length;)t=e.charCodeAt(n),i=n+1<e.length?e.charCodeAt(n+1):0,55296<=t&&t<=56319&&56320<=i&&i<=57343&&(t=65536+((1023&t)<<10)+(1023&i),n++),t<=127?s+=String.fromCharCode(t):t<=2047?s+=String.fromCharCode(192|t>>>6&31,128|63&t):t<=65535?s+=String.fromCharCode(224|t>>>12&15,128|t>>>6&63,128|63&t):t<=2097151&&(s+=String.fromCharCode(240|t>>>18&7,128|t>>>12&63,128|t>>>6&63,128|63&t));return s}function n(e){for(var t=Array(e.length>>2),i=0;i<t.length;i++)t[i]=0;for(i=0;i<8*e.length;i+=8)t[i>>5]|=(255&e.charCodeAt(i/8))<<i%32;return t}function o(e){for(var t="",i=0;i<32*e.length;i+=8)t+=String.fromCharCode(e[i>>5]>>>i%32&255);return t}function a(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var i=1732584193,s=-271733879,n=-1732584194,l=271733878,o=0;o<e.length;o+=16){var a=i,r=s,d=n,u=l,i=c(i,s,n,l,e[o+0],7,-680876936),l=c(l,i,s,n,e[o+1],12,-389564586),n=c(n,l,i,s,e[o+2],17,606105819),s=c(s,n,l,i,e[o+3],22,-1044525330);i=c(i,s,n,l,e[o+4],7,-176418897),l=c(l,i,s,n,e[o+5],12,1200080426),n=c(n,l,i,s,e[o+6],17,-1473231341),s=c(s,n,l,i,e[o+7],22,-45705983),i=c(i,s,n,l,e[o+8],7,1770035416),l=c(l,i,s,n,e[o+9],12,-1958414417),n=c(n,l,i,s,e[o+10],17,-42063),s=c(s,n,l,i,e[o+11],22,-1990404162),i=c(i,s,n,l,e[o+12],7,1804603682),l=c(l,i,s,n,e[o+13],12,-40341101),n=c(n,l,i,s,e[o+14],17,-1502002290),i=h(i,s=c(s,n,l,i,e[o+15],22,1236535329),n,l,e[o+1],5,-165796510),l=h(l,i,s,n,e[o+6],9,-1069501632),n=h(n,l,i,s,e[o+11],14,643717713),s=h(s,n,l,i,e[o+0],20,-373897302),i=h(i,s,n,l,e[o+5],5,-701558691),l=h(l,i,s,n,e[o+10],9,38016083),n=h(n,l,i,s,e[o+15],14,-660478335),s=h(s,n,l,i,e[o+4],20,-405537848),i=h(i,s,n,l,e[o+9],5,568446438),l=h(l,i,s,n,e[o+14],9,-1019803690),n=h(n,l,i,s,e[o+3],14,-187363961),s=h(s,n,l,i,e[o+8],20,1163531501),i=h(i,s,n,l,e[o+13],5,-1444681467),l=h(l,i,s,n,e[o+2],9,-51403784),n=h(n,l,i,s,e[o+7],14,1735328473),i=p(i,s=h(s,n,l,i,e[o+12],20,-1926607734),n,l,e[o+5],4,-378558),l=p(l,i,s,n,e[o+8],11,-2022574463),n=p(n,l,i,s,e[o+11],16,1839030562),s=p(s,n,l,i,e[o+14],23,-35309556),i=p(i,s,n,l,e[o+1],4,-1530992060),l=p(l,i,s,n,e[o+4],11,1272893353),n=p(n,l,i,s,e[o+7],16,-155497632),s=p(s,n,l,i,e[o+10],23,-1094730640),i=p(i,s,n,l,e[o+13],4,681279174),l=p(l,i,s,n,e[o+0],11,-358537222),n=p(n,l,i,s,e[o+3],16,-722521979),s=p(s,n,l,i,e[o+6],23,76029189),i=p(i,s,n,l,e[o+9],4,-640364487),l=p(l,i,s,n,e[o+12],11,-421815835),n=p(n,l,i,s,e[o+15],16,530742520),i=f(i,s=p(s,n,l,i,e[o+2],23,-995338651),n,l,e[o+0],6,-198630844),l=f(l,i,s,n,e[o+7],10,1126891415),n=f(n,l,i,s,e[o+14],15,-1416354905),s=f(s,n,l,i,e[o+5],21,-57434055),i=f(i,s,n,l,e[o+12],6,1700485571),l=f(l,i,s,n,e[o+3],10,-1894986606),n=f(n,l,i,s,e[o+10],15,-1051523),s=f(s,n,l,i,e[o+1],21,-2054922799),i=f(i,s,n,l,e[o+8],6,1873313359),l=f(l,i,s,n,e[o+15],10,-30611744),n=f(n,l,i,s,e[o+6],15,-1560198380),s=f(s,n,l,i,e[o+13],21,1309151649),i=f(i,s,n,l,e[o+4],6,-145523070),l=f(l,i,s,n,e[o+11],10,-1120210379),n=f(n,l,i,s,e[o+2],15,718787259),s=f(s,n,l,i,e[o+9],21,-343485551),i=g(i,a),s=g(s,r),n=g(n,d),l=g(l,u)}return Array(i,s,n,l)}function r(e,t,i,s,n,l){return g((l=g(g(t,e),g(s,l)))<<(n=n)|l>>>32-n,i)}function c(e,t,i,s,n,l,o){return r(t&i|~t&s,e,t,n,l,o)}function h(e,t,i,s,n,l,o){return r(t&s|i&~s,e,t,n,l,o)}function p(e,t,i,s,n,l,o){return r(t^i^s,e,t,n,l,o)}function f(e,t,i,s,n,l,o){return r(i^(t|~s),e,t,n,l,o)}function g(e,t){var i=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(i>>16)<<16|65535&i}return function(e){return i(t(s(e)))}(e)},transition:function(e,t,i,s){var n=g(e).width(),l=g(e).height();if(!e||!t)return void console.log("ERROR: Cannot do transition when one of the divs is null");switch(e.parentNode.style.cssText+="perspective: 900px; overflow: hidden;",e.style.cssText+="; position: absolute; z-index: 1019; backface-visibility: hidden",t.style.cssText+="; position: absolute; z-index: 1020; backface-visibility: hidden",i){case"slide-left":e.style.cssText+="overflow: hidden; transform: translate3d(0, 0, 0)",t.style.cssText+="overflow: hidden; transform: translate3d("+n+"px, 0, 0)",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; transform: translate3d(0, 0, 0)",e.style.cssText+="transition: 0.5s; transform: translate3d(-"+n+"px, 0, 0)"},1);break;case"slide-right":e.style.cssText+="overflow: hidden; transform: translate3d(0, 0, 0)",t.style.cssText+="overflow: hidden; transform: translate3d(-"+n+"px, 0, 0)",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; transform: translate3d(0px, 0, 0)",e.style.cssText+="transition: 0.5s; transform: translate3d("+n+"px, 0, 0)"},1);break;case"slide-down":e.style.cssText+="overflow: hidden; z-index: 1; transform: translate3d(0, 0, 0)",t.style.cssText+="overflow: hidden; z-index: 0; transform: translate3d(0, 0, 0)",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; transform: translate3d(0, 0, 0)",e.style.cssText+="transition: 0.5s; transform: translate3d(0, "+l+"px, 0)"},1);break;case"slide-up":e.style.cssText+="overflow: hidden; transform: translate3d(0, 0, 0)",t.style.cssText+="overflow: hidden; transform: translate3d(0, "+l+"px, 0)",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; transform: translate3d(0, 0, 0)",e.style.cssText+="transition: 0.5s; transform: translate3d(0, 0, 0)"},1);break;case"flip-left":e.style.cssText+="overflow: hidden; transform: rotateY(0deg)",t.style.cssText+="overflow: hidden; transform: rotateY(-180deg)",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; transform: rotateY(0deg)",e.style.cssText+="transition: 0.5s; transform: rotateY(180deg)"},1);break;case"flip-right":e.style.cssText+="overflow: hidden; transform: rotateY(0deg)",t.style.cssText+="overflow: hidden; transform: rotateY(180deg)",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; transform: rotateY(0deg)",e.style.cssText+="transition: 0.5s; transform: rotateY(-180deg)"},1);break;case"flip-down":e.style.cssText+="overflow: hidden; transform: rotateX(0deg)",t.style.cssText+="overflow: hidden; transform: rotateX(180deg)",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; transform: rotateX(0deg)",e.style.cssText+="transition: 0.5s; transform: rotateX(-180deg)"},1);break;case"flip-up":e.style.cssText+="overflow: hidden; transform: rotateX(0deg)",t.style.cssText+="overflow: hidden; transform: rotateX(-180deg)",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; transform: rotateX(0deg)",e.style.cssText+="transition: 0.5s; transform: rotateX(180deg)"},1);break;case"pop-in":e.style.cssText+="overflow: hidden; transform: translate3d(0, 0, 0)",t.style.cssText+="overflow: hidden; transform: translate3d(0, 0, 0); transform: scale(.8); opacity: 0;",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; transform: scale(1); opacity: 1;",e.style.cssText+="transition: 0.5s;"},1);break;case"pop-out":e.style.cssText+="overflow: hidden; transform: translate3d(0, 0, 0); transform: scale(1); opacity: 1;",t.style.cssText+="overflow: hidden; transform: translate3d(0, 0, 0); opacity: 0;",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; opacity: 1;",e.style.cssText+="transition: 0.5s; transform: scale(1.7); opacity: 0;"},1);break;default:e.style.cssText+="overflow: hidden; transform: translate3d(0, 0, 0)",t.style.cssText+="overflow: hidden; translate3d(0, 0, 0); opacity: 0;",g(t).show(),window.setTimeout(function(){t.style.cssText+="transition: 0.5s; opacity: 1;",e.style.cssText+="transition: 0.5s"},1)}setTimeout(function(){"slide-down"===i&&(g(e).css("z-index","1019"),g(t).css("z-index","1020")),t&&g(t).css({opacity:"1"}).css(w2utils.cssPrefix({transition:"",transform:""})),e&&g(e).css({opacity:"1"}).css(w2utils.cssPrefix({transition:"",transform:""})),"function"==typeof s&&s()},500)},lock:function(e,t,i){var s={};"object"==typeof t?s=t:(s.msg=t,s.spinner=i);s.msg||0===s.msg||(s.msg="");w2utils.unlock(e),g(e).prepend('<div class="w2ui-lock"></div><div class="w2ui-lock-msg"></div>');i=g(e).find(".w2ui-lock"),e=g(e).find(".w2ui-lock-msg");s.msg||e.css({"background-color":"transparent",border:"0px"});!0===s.spinner&&(s.msg='<div class="w2ui-spinner" '+(s.msg?"":'style="width: 35px; height: 35px"')+"></div>"+s.msg);null!=s.opacity&&i.css("opacity",s.opacity);"function"==typeof i.fadeIn?(i.fadeIn(200),e.html(s.msg).fadeIn(200)):(i.show(),e.html(s.msg).show(0))},unlock:function(e,t){c(t)?(g(e).find(".w2ui-lock").fadeOut(t),setTimeout(function(){g(e).find(".w2ui-lock").remove(),g(e).find(".w2ui-lock-msg").remove()},t)):(g(e).find(".w2ui-lock").remove(),g(e).find(".w2ui-lock-msg").remove())},message:function(s,e){var t,n,l=this;g().w2tag(),e=e||{width:200,height:100};null==e.on&&g.extend(e,w2utils.event);null==e.width&&(e.width=200);null==e.height&&(e.height=100);var i=parseInt(g(s.box).width()),o=parseInt(g(s.box).height()),a=parseInt(g(s.box).find(s.title).css("height")||0);e.width>i&&(e.width=i-10);e.height>o-a&&(e.height=o-10-a);e.originalWidth=e.width,e.originalHeight=e.height,parseInt(e.width)<0&&(e.width=i+e.width);parseInt(e.width)<10&&(e.width=10);parseInt(e.height)<0&&(e.height=o+e.height-a);parseInt(e.height)<10&&(e.height=10);null==e.hideOnClick&&(e.hideOnClick=!1);var r=g(s.box).data("options")||{};(null==e.width||e.width>r.width-10)&&(e.width=r.width-10);(null==e.height||e.height>r.height-a-5)&&(e.height=r.height-a-5);e.originalHeight<0&&(e.height=o+e.originalHeight-a);e.originalWidth<0&&(e.width=i+2*e.originalWidth);var d=g(s.box).find(s.title),a=g(s.box).find(".w2ui-message.w2ui-closing");0<g(s.box).find(".w2ui-message.w2ui-closing").length&&(clearTimeout(t),p(a,a.data("options")||{}));var u=g(s.box).find(".w2ui-message").length;if(""===g.trim(e.html)&&""===g.trim(e.body)&&""===g.trim(e.buttons)){var c;0!==u&&(c=g(s.box).find("#w2ui-message"+(u-1)),e=c.data("options")||{},!0!==(n=e.trigger({phase:"before",type:"close",target:"self"})).isCancelled&&(c.css(w2utils.cssPrefix({transition:"0.15s",transform:"translateY(-"+e.height+"px)"})).addClass("w2ui-closing"),1===u?this.unlock&&(s.param?this.unlock(s.param,150):this.unlock(150)):g(s.box).find("#w2ui-message"+(u-2)).css("z-index",1500),t=setTimeout(function(){p(c,e)},150)))}else{""===g.trim(e.body)&&""===g.trim(e.buttons)||(e.html='<div class="w2ui-message-body">'+(e.body||"")+'</div><div class="w2ui-message-buttons">'+(e.buttons||"")+"</div>"),g(s.box).find(".w2ui-message").css("z-index",1390),d.data("old-z-index",d.css("z-index")),d.css("z-index",1501),g(s.box).find(s.body).before('<div id="w2ui-message'+u+'" onmousedown="event.stopPropagation();"    class="w2ui-message" style="display: none; z-index: 1500; '+(0===d.length?"top: 0px;":"top: "+w2utils.getSize(d,"height")+"px;")+(null!=e.width?"width: "+e.width+"px; left: "+(i-e.width)/2+"px;":"left: 10px; right: 10px;")+(null!=e.height?"height: "+e.height+"px;":"bottom: 6px;")+w2utils.cssPrefix("transition",".3s",!0)+'"'+(!0===e.hideOnClick?s.param?'onclick="'+s.path+".message('"+s.param+"');\"":'onclick="'+s.path+'.message();"':"")+"></div>"),g(s.box).find("#w2ui-message"+u).data("options",e).data("prev_focus",g(":focus"));var h=g(s.box).find("#w2ui-message"+u).css("display");if(g(s.box).find("#w2ui-message"+u).css(w2utils.cssPrefix({transform:"none"===h?"translateY(-"+e.height+"px)":"translateY(0px)"})),"none"===h){if(g(s.box).find("#w2ui-message"+u).show().html(e.html),e.box=g(s.box).find("#w2ui-message"+u),!0===(n=e.trigger({phase:"before",type:"open",target:"self"})).isCancelled)return d.css("z-index",d.data("old-z-index")),void g(s.box).find("#w2ui-message"+u).remove();setTimeout(function(){g(s.box).find("#w2ui-message"+u).css(w2utils.cssPrefix({transform:"none"===h?"translateY(0px)":"translateY(-"+e.height+"px)"}))},1),0===u&&this.lock&&(s.param?this.lock(s.param):this.lock()),setTimeout(function(){g(s.box).find("#w2ui-message"+u).css(w2utils.cssPrefix({transition:"0s"})),e.trigger(g.extend(n,{phase:"after"}))},350)}}function p(e,t){if(null==n&&!0===(n=t.trigger({phase:"before",type:"open",target:"self"})).isCancelled)return d.css("z-index",d.data("old-z-index")),void g(s.box).find("#w2ui-message"+u).remove();var i=e.data("prev_focus");e.remove(),i&&0<i.length?i.focus():l&&l.focus&&l.focus(),d.css("z-index",d.data("old-z-index")),t.trigger(g.extend(n,{phase:"after"}))}},naturalCompare:function(e,t){var s,i,n=1,l=0,o=0,a=String.alphabet;function r(e,t,i){if(i){for(s=t;(i=r(e,s))<76&&65<i;)++s;return+e.slice(t-1,s)}return-1<(i=a&&a.indexOf(e.charAt(t)))?i+76:(i=e.charCodeAt(t)||0)<45||127<i?i:i<46?65:i<48?i-1:i<58?i+18:i<65?i-11:i<91?i+11:i<97?i-37:i<123?i+5:i-63}if((e+="")!=(t+=""))for(;n;)if(i=r(e,l++),n=r(t,o++),i<76&&n<76&&66<i&&66<n&&(i=r(e,l,l),n=r(t,o,l=s),o=s),i!=n)return i<n?-1:1;return 0},lang:function(e){var t=this.settings.phrases[e];return null==t?e:t},locale:function(s,n){s=s||"en-us";if("string"!=typeof s)return void(w2utils.settings=g.extend(!0,w2utils.settings,s));5===s.length&&(s="locale/"+s+".json");w2utils.settings.phrases={},g.ajax({url:s,type:"GET",dataType:"JSON",success:function(e,t,i){w2utils.settings=g.extend(!0,w2utils.settings,e),"function"==typeof n&&n()},error:function(e,t,i){console.log("ERROR: Cannot load locale "+s)}})},getSize:function(e,t){var i=g(e),s=parseInt(i.css("border-left-width"))||0,n=parseInt(i.css("border-right-width"))||0,l=parseInt(i.css("border-top-width"))||0,o=parseInt(i.css("border-bottom-width"))||0,a=parseInt(i.css("margin-left"))||0,r=parseInt(i.css("margin-right"))||0,d=parseInt(i.css("margin-top"))||0,u=parseInt(i.css("margin-bottom"))||0,c=parseInt(i.css("padding-left"))||0,h=parseInt(i.css("padding-right"))||0,p=parseInt(i.css("padding-top"))||0,f=parseInt(i.css("padding-bottom"))||0;switch(t){case"top":return l+d+p;case"bottom":return o+u+f;case"left":return s+a+c;case"right":return n+r+h;case"width":return s+n+a+r+c+h+parseInt(i.width());case"height":return l+o+d+u+p+f+parseInt(i.height());case"+width":return s+n+a+r+c+h;case"+height":return l+o+d+u+p+f}return 0},getStrWidth:function(e,t){var e='<div id="_tmp_width" style="position: absolute; top: -900px;'+(t||"")+'">'+i(e)+"</div>";return g("body").append(e),e=g("#_tmp_width").width(),g("#_tmp_width").remove(),e},scrollBarSize:function(){if(e.scrollBarSize)return e.scrollBarSize;g("body").append('<div id="_scrollbar_width" style="position: absolute; top: -300px; width: 100px; height: 100px; overflow-y: scroll;">    <div style="height: 120px">1</div></div>'),e.scrollBarSize=100-g("#_scrollbar_width > div").width(),g("#_scrollbar_width").remove(),0<=String(navigator.userAgent).indexOf("MSIE")&&(e.scrollBarSize=e.scrollBarSize/2);return e.scrollBarSize},checkName:function(e,t){return e&&null!=e.name?null==w2ui[e.name]?!!w2utils.isAlphaNumeric(e.name)||(console.log('ERROR: The parameter "name" has to be alpha-numeric (a-z, 0-9, dash and underscore). '),!1):(console.log('ERROR: The parameter "name" is not unique. There are other objects already created with the same name (obj: '+e.name+")."),!1):(console.log('ERROR: The parameter "name" is required but not supplied in $().'+t+"()."),!1)},checkUniqueId:function(e,t,i,s){g.isArray(t)||(t=[t]);for(var n=0;n<t.length;n++)if(t[n].id===e)return console.log('ERROR: The parameter "id='+e+'" is not unique within the current '+i+". (obj: "+s+")"),!1;return!0},parseRoute:function(e){var o=[],e=e.replace(/\/\(/g,"(?:/").replace(/\+/g,"__plus__").replace(/(\/)?(\.)?:(\w+)(?:(\(.*?\)))?(\?)?/g,function(e,t,i,s,n,l){return o.push({name:s,optional:!!l}),t=t||"",(l?"":t)+"(?:"+(l?t:"")+(i||"")+(n||(i?"([^/.]+?)":"([^/]+?)"))+")"+(l||"")}).replace(/([\/.])/g,"\\$1").replace(/__plus__/g,"(.+)").replace(/\*/g,"(.*)");return{path:new RegExp("^"+e+"$","i"),keys:o}},cssPrefix:function(e,t,i){var s={},n={},l="";g.isPlainObject(e)?(s=e,!0===t&&(i=!0)):s[e]=t;for(o in s)n[o]=s[o],n["-webkit-"+o]=s[o],n["-moz-"+o]=s[o].replace("-webkit-","-moz-"),n["-ms-"+o]=s[o].replace("-webkit-","-ms-"),n["-o-"+o]=s[o].replace("-webkit-","-o-");if(!0===i)for(var o in n)l+=o+": "+n[o]+"; ";else l=n;return l},parseColor:function(e){{if("string"!=typeof e)return null;e=e.trim().toUpperCase()}"#"===e[0]&&(e=e.substr(1));var t={};if(3===e.length)t={r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:1};else if(6===e.length)t={r:parseInt(e.substr(0,2),16),g:parseInt(e.substr(2,2),16),b:parseInt(e.substr(4,2),16),a:1};else if(8===e.length)t={r:parseInt(e.substr(0,2),16),g:parseInt(e.substr(2,2),16),b:parseInt(e.substr(4,2),16),a:Math.round(parseInt(e.substr(6,2),16)/255*100)/100};else if(4<e.length&&"RGB("===e.substr(0,4)){var i=e.replace("RGB","").replace(/\(/g,"").replace(/\)/g,"").split(",");t={r:parseInt(i[0],10),g:parseInt(i[1],10),b:parseInt(i[2],10),a:1}}else{if(!(5<e.length&&"RGBA("===e.substr(0,5)))return null;i=e.replace("RGBA","").replace(/\(/g,"").replace(/\)/g,"").split(",");t={r:parseInt(i[0],10),g:parseInt(i[1],10),b:parseInt(i[2],10),a:parseFloat(i[3])}}return t},hsv2rgb:function(e,t,i,s){var n,l,o,a,r,d,u,c;1===arguments.length&&(t=e.s,i=e.v,s=e.a,e=e.h);switch(e/=360,t/=100,i/=100,a=Math.floor(6*e),d=i*(1-t),u=i*(1-(r=6*e-a)*t),c=i*(1-(1-r)*t),a%6){case 0:n=i,l=c,o=d;break;case 1:n=u,l=i,o=d;break;case 2:n=d,l=i,o=c;break;case 3:n=d,l=u,o=i;break;case 4:n=c,l=d,o=i;break;case 5:n=i,l=d,o=u}return{r:Math.round(255*n),g:Math.round(255*l),b:Math.round(255*o),a:null!=s?s:1}},rgb2hsv:function(e,t,i,s){1===arguments.length&&(t=e.g,i=e.b,s=e.a,e=e.r);var n,l=Math.max(e,t,i),o=Math.min(e,t,i),a=l-o,r=0===l?0:a/l,d=l/255;switch(l){case o:n=0;break;case e:n=t-i+a*(t<i?6:0),n/=6*a;break;case t:n=i-e+2*a,n/=6*a;break;case i:n=e-t+4*a,n/=6*a}return{h:Math.round(360*n),s:Math.round(100*r),v:Math.round(100*d),a:null!=s?s:1}},tooltip:function(e,t){var i="mouseenter",s="mouseleave";(t=t||{}).showOn&&(i=t.showOn,delete t.showOn);t.hideOn&&(s=t.hideOn,delete t.hideOn);return"on"+i+"=\"$(this).w2tag(w2utils.base64decode('"+w2utils.base64encode(e)+"'),JSON.parse(w2utils.base64decode('"+w2utils.base64encode(JSON.stringify(t))+"')))\"on"+s+'="$(this).w2tag()"'},getCursorPosition:function(e){if(null==e)return null;var t=0,i=e.ownerDocument||e.document,s=i.defaultView||i.parentWindow;{var n,l;e.tagName&&"INPUT"===e.tagName.toUpperCase()&&e.selectionStart?t=e.selectionStart:s.getSelection?0<(l=s.getSelection()).rangeCount&&(n=l.getRangeAt(0),(s=n.cloneRange()).selectNodeContents(e),s.setEnd(n.endContainer,n.endOffset),t=s.toString().length):(l=i.selection)&&"Control"!==l.type&&(l=l.createRange(),(i=i.body.createTextRange()).moveToElementText(e),i.setEndPoint("EndToEnd",l),t=i.text.length)}return t},setCursorPosition:function(e,t,i){var s,n=document.createRange(),l=window.getSelection();if(null==e)return;for(var o=0;o<e.childNodes.length;o++){var a=g(e.childNodes[o]).text();if(e.childNodes[o].tagName&&(a=(a=g(e.childNodes[o]).html()).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&nbsp;/g," ")),t<=a.length){(s=(s=e.childNodes[o]).childNodes&&0<s.childNodes.length?s.childNodes[0]:s).childNodes&&0<s.childNodes.length&&(s=s.childNodes[0]);break}t-=a.length}if(null==s)return;t>s.length&&(t=s.length);n.setStart(s,t),i?n.setEnd(s,i):n.collapse(!0);l.removeAllRanges(),l.addRange(n)},testLocalStorage:t,hasLocalStorage:t(),isIOS:-1!==navigator.userAgent.toLowerCase().indexOf("iphone")||-1!==navigator.userAgent.toLowerCase().indexOf("ipod")||-1!==navigator.userAgent.toLowerCase().indexOf("ipad")||-1!==navigator.userAgent.toLowerCase().indexOf("mobile")||-1!==navigator.userAgent.toLowerCase().indexOf("android"),isIE:-1!==navigator.userAgent.toLowerCase().indexOf("msie")||-1!==navigator.userAgent.toLowerCase().indexOf("trident")};function c(e){return/^[-+]?[0-9]+$/.test(e)}function i(e){if(null==e)return e;switch(typeof e){case"number":break;case"string":e=String(e).replace(/&/g,"&amp;").replace(/>/g,"&gt;").replace(/</g,"&lt;").replace(/"/g,"&quot;");break;case"object":if(Array.isArray(e)){e=g.extend(!0,[],e);for(var t=0;t<e.length;t++)e[t]=this.encodeTags(e[t])}else for(var t in e=g.extend(!0,{},e))e[t]=this.encodeTags(e[t])}return e}function t(){var e="w2ui_test";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}}}(jQuery);w2utils.formatters={number:function(e,t){return 20<parseInt(t)&&(t=20),parseInt(t)<0&&(t=0),null==e||""===e?"":w2utils.formatNumber(parseFloat(e),t,!0)},float:function(e,t){return w2utils.formatters.number(e,t)},int:function(e,t){return w2utils.formatters.number(e,0)},money:function(e,t){if(null==e||""===e)return"";e=w2utils.formatNumber(Number(e),w2utils.settings.currencyPrecision||2);return(w2utils.settings.currencyPrefix||"")+e+(w2utils.settings.currencySuffix||"")},currency:function(e,t){return w2utils.formatters.money(e,t)},percent:function(e,t){return null==e||""===e?"":w2utils.formatNumber(e,t||1)+"%"},size:function(e,t){return null==e||""===e?"":w2utils.formatSize(parseInt(e))},date:function(e,t){if(""===t&&(t=w2utils.settings.dateFormat),null==e||0===e||""===e)return"";var i=w2utils.isDateTime(e,t,!0);return'<span title="'+(i=!1===i?w2utils.isDate(e,t,!0):i)+'">'+w2utils.formatDate(i,t)+"</span>"},datetime:function(e,t){if(""===t&&(t=w2utils.settings.datetimeFormat),null==e||0===e||""===e)return"";var i=w2utils.isDateTime(e,t,!0);return'<span title="'+(i=!1===i?w2utils.isDate(e,t,!0):i)+'">'+w2utils.formatDateTime(i,t)+"</span>"},time:function(e,t){if("h24"===(t="h12"===(t=""===t?w2utils.settings.timeFormat:t)?"hh:mi pm":t)&&(t="h24:mi"),null==e||0===e||""===e)return"";var i=w2utils.isDateTime(e,t,!0);return'<span title="'+(i=!1===i?w2utils.isDate(e,t,!0):i)+'">'+w2utils.formatTime(e,t)+"</span>"},timestamp:function(e,t){if(""===t&&(t=w2utils.settings.datetimeFormat),null==e||0===e||""===e)return"";var i=w2utils.isDateTime(e,t,!0);return(i=!1===i?w2utils.isDate(e,t,!0):i).toString?i.toString():""},gmt:function(e,t){if(""===t&&(t=w2utils.settings.datetimeFormat),null==e||0===e||""===e)return"";var i=w2utils.isDateTime(e,t,!0);return(i=!1===i?w2utils.isDate(e,t,!0):i).toUTCString?i.toUTCString():""},age:function(e,t){if(null==e||0===e||""===e)return"";var i=w2utils.isDateTime(e,null,!0);return'<span title="'+(i=!1===i?w2utils.isDate(e,null,!0):i)+'">'+w2utils.age(e)+(t?" "+t:"")+"</span>"},interval:function(e,t){return null==e||0===e||""===e?"":w2utils.interval(e)+(t?" "+t:"")},toggle:function(e,t){return e?"Yes":""},password:function(e,t){for(var i="",s=0;s<e.length;s++)i+="*";return i}},w2utils.event={on:function(e,t){var i,s,n=jQuery;if("string"==typeof e&&-1!==e.indexOf(".")&&(e=(s=e.split("."))[0],i=s[1]),"string"==typeof e&&-1!==e.indexOf(":")&&(s=e.split(":"),-1!==["complete","done"].indexOf(e[1])&&(e[1]="after"),e={type:s[0],execute:s[1]},i&&(e.scope=i)),n.isPlainObject(e)||(e={type:e,scope:i}),(e=n.extend({type:null,execute:"before",target:null,onComplete:null},e)).type){if(t)return n.isArray(this.handlers)||(this.handlers=[]),this.handlers.push({edata:e,handler:t}),this;console.log("ERROR: You must specify event handler function when calling .on() method of "+this.name)}else console.log("ERROR: You must specify event type when calling .on() method of "+this.name)},off:function(e,t){var i,s,n=jQuery;if("string"==typeof e&&-1!==e.indexOf(".")&&(e=(s=e.split("."))[0],i=s[1],""===e&&(e="*")),"string"==typeof e&&-1!==e.indexOf(":")&&(s=e.split(":"),-1!==["complete","done"].indexOf(e[1])&&(e[1]="after"),e={type:s[0],execute:s[1]}),n.isPlainObject(e)||(e={type:e}),(e=n.extend({},{type:null,execute:null,target:null,onComplete:null},e)).type||i){t=t||null;for(var l=[],o=0,a=this.handlers.length;o<a;o++){var r=this.handlers[o];(r.edata.type!==e.type&&"*"!==e.type&&(null==r.edata.scope||""!=e.type)||r.edata.target!==e.target&&null!=e.target||r.edata.execute!==e.execute&&null!=e.execute||!(r.handler===t&&null!=t||null!=i&&r.edata.scope==i))&&l.push(r)}return this.handlers=l,this}console.log("ERROR: You must specify event type when calling .off() method of "+this.name)},trigger:function(e){var t,i,s,n=jQuery;"before"===(e=n.extend({type:null,phase:"before",target:null,doneHandlers:[]},e,{isStopped:!1,isCancelled:!1,done:function(e){this.doneHandlers.push(e)},preventDefault:function(){this.isCancelled=!0},stopPropagation:function(){this.isStopped=!0}})).phase&&(e.onComplete=null),null==e.target&&(e.target=null),n.isArray(this.handlers)||(this.handlers=[]);for(var l=this.handlers.length-1;0<=l;l--){var o=this.handlers[l];if(!(null==o||o.edata.type!==e.type&&"*"!==o.edata.type||o.edata.target!==e.target&&null!=o.edata.target||o.edata.execute!==e.phase&&"*"!==o.edata.execute&&"*"!==o.edata.phase)&&(e=n.extend({},o.edata,e),t=[],2===(t=(s=new RegExp(/\((.*?)\)/).exec(o.handler))?s[1].split(/\s*,\s*/):t).length?o.handler.call(this,e.target,e):o.handler.call(this,e),!0===e.isStopped||!0===e.stop))return e}var a="on"+e.type.substr(0,1).toUpperCase()+e.type.substr(1);if("before"===e.phase&&"function"==typeof this[a]&&(i=this[a],t=[],2===(t=(s=new RegExp(/\((.*?)\)/).exec(i))?s[1].split(/\s*,\s*/):t).length?i.call(this,e.target,e):i.call(this,e),!0===e.isStopped||!0===e.stop))return e;if(null!=e.object&&"before"===e.phase&&"function"==typeof e.object[a]&&(i=e.object[a],t=[],2===(t=(s=new RegExp(/\((.*?)\)/).exec(i))?s[1].split(/\s*,\s*/):t).length?i.call(this,e.target,e):i.call(this,e),!0===e.isStopped||!0===e.stop))return e;if("after"===e.phase){"function"==typeof e.onComplete&&e.onComplete.call(this,e);for(var r=0;r<e.doneHandlers.length;r++)"function"==typeof e.doneHandlers[r]&&e.doneHandlers[r].call(this,e)}return e}},function(y){y.fn.w2render=function(e){0<y(this).length&&("string"==typeof e&&w2ui[e]&&w2ui[e].render(y(this)[0]),"object"==typeof e&&e.render(y(this)[0]))},y.fn.w2destroy=function(e){"string"==typeof(e=!e&&0<this.length?this.attr("name"):e)&&w2ui[e]&&w2ui[e].destroy(),"object"==typeof e&&e.destroy()},y.fn.w2marker=function(){var l=Array.prototype.slice.call(arguments,0);return 0!==(l=Array.isArray(l[0])?l[0]:l).length&&l[0]?y(this).each(function(e,t){o(0,t);for(var i=0;i<l.length;i++){var s=(s="string"!=typeof(s=l[i])?String(s):s).replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&").replace(/&/g,"&amp;").replace(/</g,"&gt;").replace(/>/g,"&lt;"),s=new RegExp(s+"(?!([^<]+)?>)","gi");t.innerHTML=t.innerHTML.replace(s,n)}function n(e){return'<span class="w2ui-marker">'+e+"</span>"}}):y(this).each(o);function o(e,t){for(;-1!==t.innerHTML.indexOf('<span class="w2ui-marker">');)t.innerHTML=t.innerHTML.replace(/\<span class=\"w2ui\-marker\"\>((.|\n|\r)*)\<\/span\>/gi,"$1")}},y.fn.w2tag=function(a,r){if(1===arguments.length&&"object"==typeof a&&null!=(r=a).html&&(a=r.html),null!=(r=y.extend({id:null,auto:null,html:a,position:"right|top",align:"none",left:0,top:0,maxWidth:null,style:"",css:{},className:"",inputClass:"",onShow:null,onHide:null,hideOnKeyPress:!0,hideOnFocus:!1,hideOnBlur:!1,hideOnClick:!1,hideOnChange:!0},r)).name&&null==r.id&&(r.id=r.name),""!==r.class&&""===r.inputClass&&(r.inputClass=r.class),0!==y(this).length)return!0===r.auto||null!=r.showOn||null!=r.hideOn?0!=arguments.length&&a?y(this).each(function(e,t){var i="mouseenter",s="mouseleave";r.showOn&&(i=String(r.showOn).toLowerCase(),delete r.showOn),r.hideOn&&(s=String(r.hideOn).toLowerCase(),delete r.hideOn),r.potision||(r.position="top|bottom"),y(t).off(".w2tooltip").on(i+".w2tooltip",function(){r.auto=!1,y(this).w2tag(a,r)}).on(s+".w2tooltip",function(){y(this).w2tag()})}):y(this).each(function(e,t){y(t).off(".w2tooltip")}):y(this).each(function(e,t){var c,i=(i=""==(i=r.id||t.id)?y(t).find("input").attr("id"):i)||"noid",s=w2utils.escapeId(i);function n(){var e;c.box.css("display","block"),c&&c.box&&y(c.attachedTo).offset()&&(e=c.getPos(),c.box.css({opacity:"1",left:e.left+"px",top:e.top+"px"}).data("w2tag",c).find(".w2ui-tag-body").addClass(e.posClass),c.tmp.pos=e.left+"x"+e.top,y(c.attachedTo).off(".w2tag").css(c.options.css).addClass(c.options.inputClass),c.options.hideOnKeyPress&&y(c.attachedTo).on("keypress.w2tag",c.hide),c.options.hideOnFocus&&y(c.attachedTo).on("focus.w2tag",c.hide),r.hideOnChange&&("INPUT"===t.nodeName?y(t):y(t).find("input")).on("change.w2tag",c.hide),c.options.hideOnBlur&&y(c.attachedTo).on("blur.w2tag",c.hide),c.options.hideOnClick&&y("body").on("click.w2tag"+(c.id||""),c.hide),"function"==typeof c.options.onShow&&c.options.onShow(),l())}function l(e){var t=y(c.attachedTo).offset();0===y(c.attachedTo).length||0===t.left&&0===t.top||0===c.box.find(".w2ui-tag-body").length?c.hide():(t=o(),c.tmp.pos!==t.left+"x"+t.top&&(c.box.css(w2utils.cssPrefix({transition:e?"0s":".2s"})).css({left:t.left+"px",top:t.top+"px"}),c.tmp.pos=t.left+"x"+t.top),c.tmp.timer&&clearTimeout(c.tmp.timer),c.tmp.timer=setTimeout(l,100))}function o(){var e=y(c.attachedTo).offset(),t="w2ui-tag-right",i=parseInt(e.left+c.attachedTo.offsetWidth+(c.options.left||0)),s=parseInt(e.top+(c.options.top||0)),n=c.box.find(".w2ui-tag-body"),l=n[0].offsetWidth,o=n[0].offsetHeight;if("string"==typeof c.options.position&&-1!==c.options.position.indexOf("|")&&(c.options.position=c.options.position.split("|")),"top"===c.options.position)t="w2ui-tag-top",i=parseInt(e.left+(c.options.left||0))-14,s=parseInt(e.top+(c.options.top||0))-o-10;else if("bottom"===c.options.position)t="w2ui-tag-bottom",i=parseInt(e.left+(c.options.left||0))-14,s=parseInt(e.top+c.attachedTo.offsetHeight+(c.options.top||0))+10;else if("left"===c.options.position)t="w2ui-tag-left",i=parseInt(e.left+(c.options.left||0))-l-20,s=parseInt(e.top+(c.options.top||0));else if(Array.isArray(c.options.position)){for(var a=window.innerWidth,r=window.innerHeight,d=0;d<c.options.position.length;d++){var u=c.options.position[d];if("right"===u){if(t="w2ui-tag-right",i=parseInt(e.left+c.attachedTo.offsetWidth+(c.options.left||0)),s=parseInt(e.top+(c.options.top||0)),i+l<=a)break}else if("left"===u){if(t="w2ui-tag-left",i=parseInt(e.left+(c.options.left||0))-l-20,s=parseInt(e.top+(c.options.top||0)),0<=i)break}else if("top"===u){if(t="w2ui-tag-top",i=parseInt(e.left+(c.options.left||0))-14,s=parseInt(e.top+(c.options.top||0))-o-10,i+l<=a&&0<=s)break}else if("bottom"===u&&(t="w2ui-tag-bottom",i=parseInt(e.left+(c.options.left||0))-14,s=parseInt(e.top+c.attachedTo.offsetHeight+(c.options.top||0))+10,i+l<=a&&s+o<=r))break}n.data("posClass")!==t&&n.removeClass("w2ui-tag-right w2ui-tag-left w2ui-tag-top w2ui-tag-bottom").addClass(t).data("posClass",t)}return{left:i,top:s,posClass:t}}null!=y(this).data("w2tag")?(c=y(this).data("w2tag"),y.extend(c.options,r)):c={id:i,attachedTo:t,box:y("#w2ui-tag-"+s),options:y.extend({},r),init:n,hide:function(){if(c.box.length<=0)return;c.tmp.timer&&clearTimeout(c.tmp.timer);c.box.remove(),c.options.hideOnClick&&y("body").off(".w2tag"+(c.id||""));y(c.attachedTo).off(".w2tag").removeClass(c.options.inputClass).removeData("w2tag"),0<y(c.attachedTo).length&&(y(c.attachedTo)[0].style.cssText=c.tmp.originalCSS);"function"==typeof c.options.onHide&&c.options.onHide()},getPos:o,isMoved:l,tmp:{}},""===a||null==a?c.hide():0!==c.box.length?c.box.find(".w2ui-tag-body").css(c.options.css).attr("style",c.options.style).addClass(c.options.className).html(c.options.html):(c.tmp.originalCSS="",0<y(c.attachedTo).length&&(c.tmp.originalCSS=y(c.attachedTo)[0].style.cssText),i="white-space: nowrap;",c.options.maxWidth&&w2utils.getStrWidth(a)>c.options.maxWidth&&(i="width: "+c.options.maxWidth+"px"),y("body").append('<div onclick="event.stopPropagation()" style="display: none;" id="w2ui-tag-'+c.id+'"        class="w2ui-tag '+(0<y(c.attachedTo).parents(".w2ui-popup, .w2ui-overlay-popup, .w2ui-message").length?"w2ui-tag-popup":"")+'">   <div style="margin: -2px 0px 0px -2px; '+i+'">      <div class="w2ui-tag-body '+c.options.className+'" style="'+(c.options.style||"")+'">'+a+"</div>   </div></div>"),c.box=y("#w2ui-tag-"+s),y(c.attachedTo).data("w2tag",c),setTimeout(n,1))});y(".w2ui-tag").each(function(e,t){t=y(t).data("w2tag");t&&t.hide()})},y.fn.w2overlay=function(e,f){var g=this,m="";if(1===arguments.length&&(f="object"==typeof e?e:{html:e}),2===arguments.length&&(f.html=e),y.isPlainObject(f)||(f={}),(f=y.extend({},{name:null,html:"",align:"none",left:0,top:0,tipLeft:30,noTip:!1,selectable:!1,width:0,height:0,maxWidth:null,maxHeight:null,contextMenu:!1,pageX:null,pageY:null,originalEvent:null,style:"",class:"",overlayStyle:"",onShow:null,onHide:null,openAbove:null,tmp:{}},f)).name&&(m="-"+f.name),0===this.length||""===f.html||null==f.html)return 0<y("#w2ui-overlay"+m).length?"function"==typeof(s=y("#w2ui-overlay"+m)[0].hide)&&s():y("#w2ui-overlay"+m).remove(),y(this);0<y("#w2ui-overlay"+m).length&&(s=y("#w2ui-overlay"+m)[0].hide,y(document).off(".w2overlay"+m),"function"==typeof s&&s()),0<g.length&&(null==g[0].tagName||"BODY"===g[0].tagName.toUpperCase())&&(f.contextMenu=!0),f.contextMenu&&f.originalEvent&&(f.pageX=f.originalEvent.pageX,f.pageY=f.originalEvent.pageY),!f.contextMenu||null!=f.pageX&&null!=f.pageY||console.log("ERROR: to display menu at mouse location, pass options.pageX and options.pageY.");var t="";f.data&&Object.keys(f.data).forEach(function(e){t+="data-"+e+'="'+f.data[e]+'"'}),y("body").append('<div id="w2ui-overlay'+m+'" style="display: none; left: 0px; top: 0px; '+f.overlayStyle+'" '+t+'        class="w2ui-reset w2ui-overlay '+(0<y(this).parents(".w2ui-popup, .w2ui-overlay-popup, .w2ui-message").length?"w2ui-overlay-popup":"")+'">    <style></style>    <div style="min-width: 100%; '+f.style+'" class="'+f.class+'"></div></div>');var i=y("#w2ui-overlay"+m),s=i.find(" > div");s.html(f.html);s=s.css("background-color");null!=s&&"rgba(0, 0, 0, 0)"!==s&&"transparent"!==s&&i.css({"background-color":s,"border-color":s});s=y(g).offset()||{};return i.data("element",0<g.length?g[0]:null).data("options",f).data("position",s.left+"x"+s.top).fadeIn("fast").on("click",function(e){y("#w2ui-overlay"+m).data("keepOpen",!0),"LABEL"===e.target.tagName.toUpperCase()&&e.stopPropagation()}).on("mousedown",function(e){var t=e.target.tagName.toUpperCase();-1!==["INPUT","TEXTAREA","SELECT"].indexOf(t)||f.selectable||e.preventDefault()}),i[0].hide=n,i[0].resize=w,setTimeout(function(){y(document).off(".w2overlay"+m).on("click.w2overlay"+m,n),"function"==typeof f.onShow&&f.onShow(),w()},10),function e(){var t=y("#w2ui-overlay"+m);if(t.data("element")!==g[0])return;if(0===t.length)return;var i=y(g).offset()||{};var i=i.left+"x"+i.top;t.data("position")!==i?n():setTimeout(e,250)}(),y(this);function n(e){var t,i;e&&0!==e.button||(t=y("#w2ui-overlay"+m),e&&y(y(e.target).closest(".w2ui-overlay").data("element")).closest(".w2ui-overlay")[0]===t[0]||(!0!==t.data("keepOpen")?!1!==(i="function"==typeof f.onHide?f.onHide():i)&&(t.remove(),y(document).off(".w2overlay"+m),clearInterval(t.data("timer"))):t.removeData("keepOpen")))}function w(){var e=y("#w2ui-overlay"+m),t=e.find(" > div"),i=y("#w2ui-overlay"+m+" div.w2ui-menu"),s={};if(0<i.length&&(i.css("overflow-y","hidden"),s.scrollTop=i.scrollTop(),s.scrollLeft=i.scrollLeft()),0<e.length){t.height("auto").width("auto");var n=!1,l=t.height(),o=t.width();(o=f.width&&f.width<o?f.width:o)<30&&(o=30),f.tmp.contentHeight&&(l=parseInt(f.tmp.contentHeight),t.height(l),setTimeout(function(){var e=t.find("div.w2ui-menu");l>e.height()&&t.find("div.w2ui-menu").css("overflow-y","hidden")},1),setTimeout(function(){var e=t.find("div.w2ui-menu");"auto"!==e.css("overflow-y")&&e.css("overflow-y","auto")},10)),f.tmp.contentWidth&&"both"!==f.align?(o=parseInt(f.tmp.contentWidth),t.width(o),setTimeout(function(){o>t.find("div.w2ui-menu > table").width()&&t.find("div.w2ui-menu > table").css("overflow-x","hidden")},1),setTimeout(function(){t.find("div.w2ui-menu > table").css("overflow-x","auto")},10)):t.find("div.w2ui-menu").css("width","100%");var a=f.left,r=f.width,d=f.tipLeft;switch(f.align){case"both":a=17,0===f.width&&(f.width=w2utils.getSize(y(g),"width")),f.maxWidth&&f.width>f.maxWidth&&(f.width=f.maxWidth);break;case"left":a=17;break;case"right":a=w2utils.getSize(y(g),"width")-o+10,d=o-40}var u="auto"!==(r=30!==o||r?f.width||"auto":30)?(r-17)/2:(o-17)/2;u<25&&(a=25-u,d=Math.floor(u)),u=f.contextMenu?(h=f.pageX+8,p=+f.pageY,f.pageY):(h=(25<(c=g.offset()||{}).left?c.left:25)+a,p=c.top+w2utils.getSize(g,"height")+f.top+7,c.top),e.css({left:h+"px",top:p+"px","min-width":r,"min-height":f.height||"auto"});var c=t.offset()||{},h=window.innerHeight+y(document).scrollTop()-c.top-7,p=window.innerWidth+y(document).scrollLeft()-c.left-7;f.contextMenu&&(h=window.innerHeight+y(document).scrollTop()-f.pageY-15,p=window.innerWidth+y(document).scrollLeft()-f.pageX),(-50<h&&h<210||!0===f.openAbove)&&!1!==f.openAbove?(r=f.contextMenu?(h=f.pageY-7,5):(h=c.top-y(document).scrollTop()-7,24),(h=f.maxHeight&&h>f.maxHeight?f.maxHeight:h)<l&&(n=!0,t.height(h).width(o).css({"overflow-y":"auto"}),l=h),e.addClass("bottom-arrow"),e.css("top",u-l-r+f.top+"px")):((h=f.maxHeight&&h>f.maxHeight?f.maxHeight:h)<l&&(n=!0,t.height(h).width(o).css({"overflow-y":"auto"})),e.addClass("top-arrow")),e.find(">style").html("#w2ui-overlay"+m+":before { margin-left: "+parseInt(d)+"px; }#w2ui-overlay"+m+":after { margin-left: "+parseInt(d)+"px; }"),o=t.width(),p=window.innerWidth+y(document).scrollLeft()-c.left-7,(p=f.maxWidth&&p>f.maxWidth?f.maxWidth:p)<o&&"both"!==f.align&&(f.align="right",setTimeout(function(){w()},1)),(f.contextMenu||f.noTip)&&e.find(">style").html("#w2ui-overlay"+m+":before { display: none; }#w2ui-overlay"+m+":after { display: none; }"),n&&"both"!==f.align&&t.width(o+w2utils.scrollBarSize()+2)}0<i.length&&(i.css("overflow-y","auto"),i.scrollTop(s.scrollTop),i.scrollLeft(s.scrollLeft))}},y.fn.w2menu=function(e,w){w&&"function"==typeof w.items&&(w.items=w.items());var t,r=this,d="";if("refresh"===e)y.fn.w2menuOptions&&y.fn.w2menuOptions.name&&(d="-"+y.fn.w2menuOptions.name),w.name&&(d="-"+w.name),0<y("#w2ui-overlay"+d).length?(w=y.extend(y.fn.w2menuOptions,w),o=y("#w2ui-overlay"+d+" div.w2ui-menu").scrollTop(),y("#w2ui-overlay"+d+" div.w2ui-menu").html(v()),y("#w2ui-overlay"+d+" div.w2ui-menu").scrollTop(o),u()):y(this).w2menu(w);else if("refresh-index"===e){var i,s,n=y("#w2ui-overlay"+d+" div.w2ui-menu"),l=n.find("tr[index="+w.index+"]"),o=n.scrollTop();n.find("tr.w2ui-selected").removeClass("w2ui-selected"),l.addClass("w2ui-selected"),0<l.length&&(i=l[0].offsetTop-5,s=n.height(),n.scrollTop(o),(i<o||i+l.height()>o+s)&&n.animate({scrollTop:i-(s-2*l.height())/2},200,"linear")),u()}else{1===arguments.length?w=e:w.items=e,"object"!=typeof w&&(w={}),w=y.extend({},{type:"normal",index:null,items:[],render:null,msgNoItems:"No items",onSelect:null,hideOnRemove:!1,tmp:{}},w),(y.fn.w2menuOptions=w).name&&(d="-"+w.name),"function"==typeof w.select&&"function"!=typeof w.onSelect&&(w.onSelect=w.select),"function"==typeof w.remove&&"function"!=typeof w.onRemove&&(w.onRemove=w.remove),"function"==typeof w.onRender&&"function"!=typeof w.render&&(w.render=w.onRender),y.fn.w2menuClick=function(e,t,i){var s=!1,n=y(e.target).closest("tr");(e.shiftKey||e.metaKey||e.ctrlKey)&&(s=!0),items=(null==i?w:w.items[i]).items,y(e.target).hasClass("remove")?("function"==typeof w.onRemove&&w.onRemove({index:t,parentIndex:i,item:items[t],keepOpen:s,originalEvent:e}),s=!w.hideOnRemove,y(e.target).closest("tr").remove(),u()):n.hasClass("has-sub-menu")?(s=!0,n.hasClass("expanded")?(items[t].expanded=!1,n.removeClass("expanded").addClass("collapsed").next().hide()):(items[t].expanded=!0,n.addClass("expanded").removeClass("collapsed").next().show()),u()):"function"==typeof w.onSelect&&(n=items,null!=(n="function"==typeof items?items(w.items[i]):n)[t].keepOpen&&(s=n[t].keepOpen),w.onSelect({index:t,parentIndex:i,item:n[t],keepOpen:s,originalEvent:e})),null!=items[t]&&!0===items[t].keepOpen||((t=y("#w2ui-overlay"+d)).removeData("keepOpen"),0<t.length&&"function"==typeof t[0].hide&&!s&&t[0].hide())},y.fn.w2menuDown=function(e,t,i){var s=y(e.target).closest("tr"),n=y(s.get(0)).find(".w2ui-icon"),l=(null==i?w:w.items[i]).items,o=l[t];"check"!==w.type&&"radio"!==w.type||!1===o.group||y(e.target).hasClass("remove")||y(e.target).closest("tr").hasClass("has-sub-menu")||(o.checked=!o.checked,o.checked?("radio"===w.type&&n.parents("table").find(".w2ui-icon").removeClass("w2ui-icon-check").addClass("w2ui-icon-empty"),"check"===w.type&&null!=o.group&&l.forEach(function(e,t){e.id!=o.id&&e.group===o.group&&e.checked&&(n.closest("table").find("tr[index="+t+"] .w2ui-icon").removeClass("w2ui-icon-check").addClass("w2ui-icon-empty"),l[t].checked=!1)}),n.removeClass("w2ui-icon-empty").addClass("w2ui-icon-check")):"check"===w.type&&null==o.group&&!1!==o.group&&n.removeClass("w2ui-icon-check").addClass("w2ui-icon-empty")),s.parent().find("tr").removeClass("w2ui-selected"),s.addClass("w2ui-selected")};l="";if(w.search){l+='<div style="position: absolute; top: 0px; height: 40px; left: 0px; right: 0px; border-bottom: 1px solid silver; background-color: #ECECEC; padding: 8px 5px;">    <div class="w2ui-icon icon-search" style="position: absolute; margin-top: 4px; margin-left: 6px; width: 11px; background-position: left !important;"></div>    <input id="menu-search" type="text" style="width: 100%; outline: none; padding-left: 20px;" onclick="event.stopPropagation();"/></div>',w.style+=";background-color: #ECECEC";for(var a=w.index=0;a<w.items.length;a++)w.items[a].hidden=!1}l+=(w.topHTML||"")+'<div class="w2ui-menu" style="top: '+(w.search?40:0)+"px;"+(w.menuStyle||"")+'">'+v()+"</div>",t=y(this).w2overlay(l,w),setTimeout(function(){if(y("#w2ui-overlay"+d+" #menu-search").on("keyup",c).on("keydown",function(e){9===e.keyCode&&(e.stopPropagation(),e.preventDefault())}),w.search){if(-1!==["text","password"].indexOf(y(r)[0].type)||"TEXTAREA"===y(r)[0].tagName.toUpperCase())return;y("#w2ui-overlay"+d+" #menu-search").focus()}u()},250),u();l=y("#w2ui-overlay"+d);0<l.length&&(l[0].mresize=u,l[0].change=c)}return t;function u(){setTimeout(function(){y("#w2ui-overlay"+d+" tr.w2ui-selected").removeClass("w2ui-selected");var e,t,i=y("#w2ui-overlay"+d+" tr[index="+w.index+"]"),s=y("#w2ui-overlay"+d+" div.w2ui-menu").scrollTop();i.addClass("w2ui-selected"),w.tmp&&(w.tmp.contentHeight=y("#w2ui-overlay"+d+" table").height()+(w.search?50:10)+(parseInt(y("#w2ui-overlay"+d+" .w2ui-menu").css("top"))||0)+(parseInt(y("#w2ui-overlay"+d+" .w2ui-menu").css("bottom"))||0),w.tmp.contentWidth=y("#w2ui-overlay"+d+" table").width()),0<y("#w2ui-overlay"+d).length&&y("#w2ui-overlay"+d)[0].resize(),0<i.length&&(e=i[0].offsetTop-5,t=y("#w2ui-overlay"+d+" div.w2ui-menu").height(),y("#w2ui-overlay"+d+" div.w2ui-menu").scrollTop(s),(e<s||e+i.height()>s+t)&&y("#w2ui-overlay"+d+" div.w2ui-menu").animate({scrollTop:e-(t-2*i.height())/2},200,"linear"))},1)}function c(e){var t=this.value,i=!1;switch(e.keyCode){case 13:y("#w2ui-overlay"+d).remove(),y.fn.w2menuClick(e,w.index);break;case 9:case 27:y("#w2ui-overlay"+d).remove(),y.fn.w2menuClick(e,-1);break;case 38:for(w.index=w2utils.isInt(w.index)?parseInt(w.index):0,w.index--;0<w.index&&w.items[w.index].hidden;)w.index--;if(0===w.index&&w.items[w.index].hidden)for(;w.items[w.index]&&w.items[w.index].hidden;)w.index++;w.index<0&&(w.index=0),i=!0;break;case 40:for(w.index=w2utils.isInt(w.index)?parseInt(w.index):0,w.index++;w.index<w.items.length-1&&w.items[w.index].hidden;)w.index++;if(w.index===w.items.length-1&&w.items[w.index].hidden)for(;w.items[w.index]&&w.items[w.index].hidden;)w.index--;w.index>=w.items.length&&(w.index=w.items.length-1),i=!0}if(!i){for(var s=0,n=0;n<w.items.length;n++){var l=w.items[n],o="",a="";-1!==["is","begins with"].indexOf(w.match)&&(o="^"),-1!==["is","ends with"].indexOf(w.match)&&(a="$");try{new RegExp(o+t+a,"i").test(l.text)||"..."===l.text?l.hidden=!1:l.hidden=!0}catch(e){}"enum"===r.type&&-1!==y.inArray(l.id,ids)&&(l.hidden=!0),!0!==l.hidden&&s++}for(w.index=0;w.index<w.items.length-1&&w.items[w.index].hidden;)w.index++;s<=0&&(w.index=-1)}y(r).w2menu("refresh",w),u()}function v(e,t,i,s){if(w.spinner)return'<table><tbody><tr><td style="padding: 5px 10px 13px 10px; text-align: center">    <div class="w2ui-spinner" style="width: 18px; height: 18px; position: relative; top: 5px;"></div>     <div style="display: inline-block; padding: 3px; color: #999;">'+w2utils.lang("Loading...")+"</div></td></tr></tbody></table>";var n=0,l='<table cellspacing="0" cellpadding="0" class="'+(t?" sub-menu":"")+'"><tbody>',o=null,a=null;null==e&&(e=w.items),Array.isArray(e)||(e=[]);for(var r=0;r<e.length;r++){var d,u,c,h,p,f,g,m=e[r];"string"==typeof m?m={id:m,text:m}:(null!=m.text&&null==m.id&&(m.id=m.text),null==m.text&&null!=m.id&&(m.text=m.id),null!=m.caption&&(m.text=m.caption),null==(o=m.img)&&(o=null),null==(a=m.icon)&&(a=null)),-1==["radio","check"].indexOf(w.type)||Array.isArray(m.items)||!1===m.group||(a=!0===m.checked?"w2ui-icon-check":"w2ui-icon-empty"),!0!==m.hidden&&(d="",g=m.text,u="","function"==typeof(g="function"==typeof w.render?w.render(m,w):g)&&(g=g(m,w)),o&&(d='<td class="menu-icon"><div class="w2ui-tb-image w2ui-icon '+o+'"></div></td>'),a&&(d='<td class="menu-icon" align="center"><span class="w2ui-icon '+a+'"></span></td>'),"break"!==m.type&&null!=g&&""!==g&&"--"!=String(g).substr(0,2)?(c=n%2==0?"w2ui-item-even":"w2ui-item-odd",!0!==w.altRows&&(c=""),h=1,""===d&&h++,null==m.count&&null==m.hotkey&&!0!==m.remove&&null==m.items&&h++,null==m.tooltip&&null!=m.hint&&(m.tooltip=m.hint),!(p="")===m.remove?p='<span class="remove">X</span>':null!=m.items?(f=[],"function"==typeof m.items?f=m.items(m):Array.isArray(m.items)&&(f=m.items),p="<span></span>",u='<tr style="'+(m.expanded?"":"display: none")+'">     <td colspan="3">'+v(f,!0,m.expanded,r)+"</td><tr>"):(null!=m.count&&(p+="<span>"+m.count+"</span>"),null!=m.hotkey&&(p+='<span class="hotkey">'+m.hotkey+"</span>")),l+='<tr index="'+r+'" style="'+(m.style||"")+'" '+(m.tooltip?'title="'+w2utils.lang(m.tooltip)+'"':"")+' class="'+c+(w.index===r?" w2ui-selected":"")+(!0===m.disabled?" w2ui-disabled":"")+(""!==u?" has-sub-menu"+(m.expanded?" expanded":" collapsed"):"")+'"        onmousedown="if ('+(!0===m.disabled?"true":"false")+") return;               jQuery.fn.w2menuDown(event, "+r+",  "+s+');"        onclick="event.stopPropagation();                if ('+(!0===m.disabled?"true":"false")+") return;               jQuery.fn.w2menuClick(event, "+r+",  "+s+');">'+(t?"<td></td>":"")+d+'   <td class="menu-text" colspan="'+h+'">'+w2utils.lang(g)+'</td>   <td class="menu-count">'+p+"</td></tr>"+u,n++):l+='<tr><td colspan="3" class="menu-divider '+(""!=(g=g.replace(/^-+/g,""))?"divider-text":"")+'">   <div class="line">'+g+'</div>   <div class="text">'+g+"</div></td></tr>"),e[r]=m}return 0===n&&w.msgNoItems&&(l+='<tr><td style="padding: 13px; color: #999; text-align: center">'+w.msgNoItems+"</div></td></tr>"),l+="</tbody></table>"}},y.fn.w2color=function(s,e){var l,o,n,a,r,d,u,c,h,p,f,g,t=y(this),m=t[0];function i(e){e.color;for(var t,i='<div class="w2ui-colors" onmousedown="jQuery(this).parents(\'.w2ui-overlay\').data(\'keepOpen\', true)"><div class="w2ui-color-palette"><table cellspacing="5"><tbody>',s=0;s<o.length;s++){i+="<tr>";for(var n=0;n<o[s].length;n++)t="FFFFFF"===o[s][n]?";border: 1px solid #efefef":"",i+='<td>    <div class="w2ui-color '+(""===o[s][n]?"w2ui-no-color":"")+'" style="background-color: #'+o[s][n]+t+';"        name="'+o[s][n]+'" index="'+s+":"+n+'">'+(e.color==o[s][n]?"&#149;":"&#160;")+"    </div></td>",e.color==o[s][n]&&(l=[s,n]);i+="</tr>",s<2&&(i+='<tr><td style="height: 8px" colspan="8"></td></tr>')}return i+="</tbody></table></div>",i+='<div class="w2ui-color-advanced" style="display: none">   <div class="color-info">       <div class="color-preview-bg"><div class="color-preview"></div><div class="color-original"></div></div>       <div class="color-part">           <span>H</span> <input name="h" maxlength="3" max="360" tabindex="101">           <span>R</span> <input name="r" maxlength="3" max="255" tabindex="104">       </div>       <div class="color-part">           <span>S</span> <input name="s" maxlength="3" max="100" tabindex="102">           <span>G</span> <input name="g" maxlength="3" max="255" tabindex="105">       </div>       <div class="color-part">           <span>V</span> <input name="v" maxlength="3" max="100" tabindex="103">           <span>B</span> <input name="b" maxlength="3" max="255" tabindex="106">       </div>       <div class="color-part" style="margin: 30px 0px 0px 2px">           <span style="width: 40px">Opacity</span>            <input name="a" maxlength="5" max="1" style="width: 32px !important" tabindex="107">       </div>   </div>   <div class="palette" name="palette">       <div class="palette-bg"></div>       <div class="value1 move-x move-y"></div>   </div>   <div class="rainbow" name="rainbow">       <div class="value2 move-x"></div>   </div>   <div class="alpha" name="alpha">       <div class="alpha-bg"></div>       <div class="value2 move-x"></div>   </div></div>',i+="<div class=\"w2ui-color-tabs\">   <div class=\"w2ui-color-tab selected\" onclick=\"jQuery(this).addClass('selected').next().removeClass('selected').parents('.w2ui-overlay').find('.w2ui-color-advanced').hide().parent().find('.w2ui-color-palette').show(); jQuery.fn._colorAdvanced = false; jQuery('#w2ui-overlay')[0].resize()\"><span class=\"w2ui-icon w2ui-icon-colors\"></span></div>   <div class=\"w2ui-color-tab\" onclick=\"jQuery(this).addClass('selected').prev().removeClass('selected').parents('.w2ui-overlay').find('.w2ui-color-advanced').show().parent().find('.w2ui-color-palette').hide(); jQuery.fn._colorAdvanced = true; jQuery('#w2ui-overlay')[0].resize()\"><span class=\"w2ui-icon w2ui-icon-settings\"></span></div>   <div style=\"padding: 8px; text-align: right;\">"+("string"==typeof e.html?e.html:"")+'</div></div></div><div style="clear: both; height: 0"></div>'}t.data("skipInit")?t.removeData("skipInit"):(l=[-1,-1],null==y.fn.w2colorPalette&&(y.fn.w2colorPalette=[["000000","333333","555555","777777","888888","999999","AAAAAA","CCCCCC","DDDDDD","EEEEEE","F7F7F7","FFFFFF"],["FF011B","FF9838","FFC300","FFFD59","86FF14","14FF7A","2EFFFC","2693FF","006CE7","9B24F4","FF21F5","FF0099"],["FFEAEA","FCEFE1","FCF4DC","FFFECF","EBFFD9","D9FFE9","E0FFFF","E8F4FF","ECF4FC","EAE6F4","FFF5FE","FCF0F7"],["F4CCCC","FCE5CD","FFF1C2","FFFDA1","D5FCB1","B5F7D0","BFFFFF","D6ECFF","CFE2F3","D9D1E9","FFE3FD","FFD9F0"],["EA9899","F9CB9C","FFE48C","F7F56F","B9F77E","84F0B1","83F7F7","B5DAFF","9FC5E8","B4A7D6","FAB9F6","FFADDE"],["E06666","F6B26B","DEB737","E0DE51","8FDB48","52D189","4EDEDB","76ACE3","6FA8DC","8E7CC3","E07EDA","F26DBD"],["CC0814","E69138","AB8816","B5B20E","6BAB30","27A85F","1BA8A6","3C81C7","3D85C6","674EA7","A14F9D","BF4990"],["99050C","B45F17","80650E","737103","395E14","10783D","13615E","094785","0A5394","351C75","780172","782C5A"]]),o=y.fn.w2colorPalette,null==(s="string"==typeof s?{color:s,transparent:!0}:s).onSelect&&null!=e&&(s.onSelect=e),s.transparent&&"333333"==o[0][1]&&(o[0].splice(1,1),o[0].push("")),s.transparent||"333333"==o[0][1]||(o[0].splice(1,0,"333333"),o[0].pop()),s.color&&(s.color=String(s.color).toUpperCase()),"string"==typeof s.color&&"#"===s.color.substr(0,1)&&(s.color=s.color.substr(1)),null==s.fireChange&&(s.fireChange=!0),0===y("#w2ui-overlay").length?y(m).w2overlay(i(s),s):(y("#w2ui-overlay .w2ui-colors").parent().html(i(s)),y("#w2ui-overlay").show()),y("#w2ui-overlay .w2ui-color").off(".w2color").on("mousedown.w2color",function(e){var t=y(e.originalEvent.target).attr("name");l=y(e.originalEvent.target).attr("index").split(":"),"INPUT"===m.tagName.toUpperCase()?(s.fireChange&&y(m).change(),y(m).next().find(">div").css("background-color",t)):y(m).data("_color",t),"function"==typeof s.onSelect&&s.onSelect(t)}).on("mouseup.w2color",function(){setTimeout(function(){0<y("#w2ui-overlay").length&&y("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)}),y("#w2ui-overlay .color-original").off(".w2color").on("click.w2color",function(e){e=w2utils.parseColor(y(e.target).css("background-color"));null!=e&&(a=e,r=w2utils.rgb2hsv(a),d(r),u(),c())}),y("#w2ui-overlay input").off(".w2color").on("mousedown.w2color",function(e){y("#w2ui-overlay").data("keepOpen",!0),setTimeout(function(){y("#w2ui-overlay").data("keepOpen",!0)},10),e.stopPropagation()}).on("change.w2color",function(){var e=y(this),t=parseFloat(e.val()),i=parseFloat(e.attr("max"));isNaN(t)&&(t=0),1<i&&(t=parseInt(t)),0<i&&i<t&&(e.val(i),t=i),t<0&&(e.val(0),t=0);i=e.attr("name"),e={};-1!==["r","g","b","a"].indexOf(i)?(a[i]=t,r=w2utils.rgb2hsv(a)):-1!==["h","s","v"].indexOf(i)&&(e[i]=t),d(e),u(),c()}),null==(a=w2utils.parseColor(s.color))&&(a={r:140,g:150,b:160,a:1},r=w2utils.rgb2hsv(a)),r=w2utils.rgb2hsv(a),d=function(e,t){null!=e.h&&(r.h=e.h),null!=e.s&&(r.s=e.s),null!=e.v&&(r.v=e.v),null!=e.a&&(a.a=e.a,r.a=e.a);var e="rgba("+(a=w2utils.hsv2rgb(r)).r+","+a.g+","+a.b+","+a.a+")",i=[Number(a.r).toString(16).toUpperCase(),Number(a.g).toString(16).toUpperCase(),Number(a.b).toString(16).toUpperCase(),Math.round(255*Number(a.a)).toString(16).toUpperCase()];i.forEach(function(e,t){1===e.length&&(i[t]="0"+e)}),e=i[0]+i[1]+i[2]+i[3],1===a.a&&(e=i[0]+i[1]+i[2]),y("#w2ui-overlay .color-preview").css("background-color","#"+e),y("#w2ui-overlay input").each(function(e,t){t.name&&(null!=a[t.name]&&(t.value=a[t.name]),null!=r[t.name]&&(t.value=r[t.name]),"a"===t.name&&(t.value=a.a))}),t?y("#w2ui-overlay .color-original").css("background-color","#"+e):("INPUT"===m.tagName.toUpperCase()?(y(m).val(e).data("skipInit",!0),s.fireChange&&y(m).change(),y(m).next().find(">div").css("background-color","#"+e)):y(m).data("_color",e),"function"==typeof s.onSelect&&s.onSelect(e))},u=function(){var e=y("#w2ui-overlay .palette .value1"),t=y("#w2ui-overlay .rainbow .value2"),i=y("#w2ui-overlay .alpha .value2"),s=parseInt(e.width())/2,n=parseInt(t.width())/2;e.css({left:150*r.s/100-s,top:125*(100-r.v)/100-s}),t.css("left",r.h/2.4-n),i.css("left",150*a.a-n)},c=function(){var e=w2utils.hsv2rgb(r.h,100,100),e=e.r+","+e.g+","+e.b;y("#w2ui-overlay .palette").css("background-image","linear-gradient(90deg, rgba("+e+",0) 0%, rgba("+e+",1) 100%)")},e=function(e){var t=y(this).find(".value1, .value2"),i=parseInt(t.width())/2;t.hasClass("move-x")&&t.css({left:e.offsetX-i+"px"}),t.hasClass("move-y")&&t.css({top:e.offsetY-i+"px"}),n={$el:t,x:e.pageX,y:e.pageY,width:t.parent().width(),height:t.parent().height(),left:parseInt(t.css("left")),top:parseInt(t.css("top"))},p(e),y("body").off(".w2color").on(g,p).on(f,h)},h=function(e){y("body").off(".w2color")},p=function(e){var t=n.$el,i=e.pageX-n.x,s=e.pageY-n.y,e=n.left+i,i=n.top+s,s=parseInt(t.width())/2;i<-s&&(i=-s),(e=e<-s?-s:e)>n.width-s&&(e=n.width-s),i>n.height-s&&(i=n.height-s),t.hasClass("move-x")&&t.css({left:e+"px"}),t.hasClass("move-y")&&t.css({top:i+"px"});e=t.parent().attr("name"),i=parseInt(t.css("left"))+s,s=parseInt(t.css("top"))+s;"palette"===e&&d({s:Math.round(i/n.width*100),v:Math.round(100-s/n.height*100)}),"rainbow"===e&&(s=Math.round(2.4*i),d({h:s}),c()),"alpha"===e&&d({a:parseFloat(Number(i/150).toFixed(2))})},!0!==y.fn._colorAdvanced&&!0!==s.advanced||(y("#w2ui-overlay .w2ui-color-tabs :nth-child(2)").click(),y("#w2ui-overlay").removeData("keepOpen")),d({},!0),c(),u(),f="mouseup.w2color",g="mousemove.w2color",w2utils.isIOS&&(f="touchend.w2color",g="touchmove.w2color  "),y("#w2ui-overlay .palette").off(".w2color").on("mousedown.w2color",e),y("#w2ui-overlay .rainbow").off(".w2color").on("mousedown.w2color",e),y("#w2ui-overlay .alpha").off(".w2color").on("mousedown.w2color",e),m.nav=function(e){switch(e){case"up":l[0]--;break;case"down":l[0]++;break;case"right":l[1]++;break;case"left":l[1]--}return l[0]<0&&(l[0]=0),l[0]>o.length-2&&(l[0]=o.length-2),l[1]<0&&(l[1]=0),l[1]>o[0].length-1&&(l[1]=o[0].length-1),color=o[l[0]][l[1]],y(m).data("_color",color),color})}}(jQuery),function($){var w2grid=function(e){this.name=null,this.box=null,this.columns=[],this.columnGroups=[],this.records=[],this.summary=[],this.searches=[],this.sortMap={},this.toolbar={},this.ranges=[],this.menu=[],this.searchData=[],this.sortData=[],this.total=0,this.recid=null,this.last={field:"",label:"",logic:"OR",search:"",searchIds:[],selection:{indexes:[],columns:{}},multi:!1,scrollTop:0,scrollLeft:0,colStart:0,colEnd:0,sortData:null,sortCount:0,xhr:null,loaded:!1,range_start:null,range_end:null,sel_ind:null,sel_col:null,sel_type:null,edit_col:null,isSafari:/^((?!chrome|android).)*safari/i.test(navigator.userAgent)},$.extend(!0,this,w2obj.grid),this.show=$.extend(!0,{},w2grid.prototype.show),this.postData=$.extend(!0,{},w2grid.prototype.postData),this.routeData=$.extend(!0,{},w2grid.prototype.routeData),this.httpHeaders=$.extend(!0,{},w2grid.prototype.httpHeaders),this.buttons=$.extend(!0,{},w2grid.prototype.buttons),this.operators=$.extend(!0,{},w2grid.prototype.operators),this.operatorsMap=$.extend(!0,{},w2grid.prototype.operatorsMap),this.stateColProps=$.extend(!0,{},w2grid.prototype.stateColProps),this.stateColDefaults=$.extend(!0,{},w2grid.prototype.stateColDefaults),$.extend(!0,this,e)};$.fn.w2grid=function(e){if(!$.isPlainObject(e)){var t=w2ui[$(this).attr("name")];return t?0<arguments.length?(t[e]&&t[e].apply(t,Array.prototype.slice.call(arguments,1)),this):t:null}if(w2utils.checkName(e,"w2grid")){var i,s=e.columns,n=e.columnGroups,l=e.records,o=e.searches,a=e.searchData,r=e.sortData,d=new w2grid(e);if($.extend(d,{records:[],columns:[],searches:[],sortData:[],searchData:[],handlers:[]}),s)for(i=0;i<s.length;i++)d.columns[i]=$.extend(!0,{},s[i]);if(n)for(i=0;i<n.length;i++)d.columnGroups[i]=$.extend(!0,{},n[i]);if(o)for(i=0;i<o.length;i++)d.searches[i]=$.extend(!0,{},o[i]);if(a)for(i=0;i<a.length;i++)d.searchData[i]=$.extend(!0,{},a[i]);if(r)for(i=0;i<r.length;i++)d.sortData[i]=$.extend(!0,{},r[i]);if(l)for(var u=0;u<l.length;u++){if(null!=l[u][d.recid]&&(l[u].recid=l[u][d.recid]),null==l[u].recid)return void console.log("ERROR: Cannot add records without recid. (obj: "+d.name+")");d.records[u]=$.extend(!0,{},l[u])}for(var c=0;c<d.columns.length;c++){var h,p=d.columns[c],f=p.searchable;null!=f&&!1!==f&&null==d.getSearch(p.field)&&($.isPlainObject(f)?d.addSearch($.extend({field:p.field,label:p.text,type:"text"},f)):(h=p.searchable,!(f="")===p.searchable&&(h="text",f='size="20"'),d.addSearch({field:p.field,label:p.text,type:h,attr:f})))}return(w2ui[d.name]=d).initToolbar(),d.updateToolbar(),0!==$(this).length&&d.render($(this)[0]),d}},w2grid.prototype={header:"",url:"",limit:100,offset:0,postData:{},routeData:{},httpHeaders:{},show:{header:!1,toolbar:!1,footer:!1,columnHeaders:!0,lineNumbers:!1,orderColumn:!1,expandColumn:!1,selectColumn:!1,emptyRecords:!0,toolbarReload:!0,toolbarColumns:!0,toolbarSearch:!0,toolbarInput:!0,toolbarAdd:!1,toolbarEdit:!1,toolbarDelete:!1,toolbarSave:!1,searchAll:!0,searchHiddenMsg:!1,statusRange:!0,statusBuffered:!1,statusRecordID:!0,statusSelection:!0,statusResponse:!0,statusSort:!1,statusSearch:!1,recordTitles:!0,selectionBorder:!0,skipRecords:!0,saveRestoreState:!0},stateId:null,hasFocus:!1,autoLoad:!0,fixedBody:!0,recordHeight:24,lineNumberWidth:null,keyboard:!0,selectType:"row",multiSearch:!0,multiSelect:!0,multiSort:!0,reorderColumns:!1,reorderRows:!1,showExtraOnSearch:0,markSearch:!0,columnTooltip:"top|bottom",disableCVS:!1,textSearch:"begins",nestedFields:!0,vs_start:150,vs_extra:15,style:"",method:null,dataType:null,parser:null,stateColProps:{text:!1,field:!0,size:!0,min:!1,max:!1,gridMinWidth:!1,sizeCorrected:!1,sizeCalculated:!0,sizeOriginal:!0,sizeType:!0,hidden:!0,sortable:!1,searchable:!1,clipboardCopy:!1,resizable:!1,hideable:!1,attr:!1,style:!1,render:!1,title:!1,editable:!1,frozen:!0,info:!1},stateColDefaults:{text:"",field:"",size:null,min:20,max:null,gridMinWidth:null,sizeCorrected:null,sizeCalculated:null,sizeOriginal:null,sizeType:null,hidden:!1,sortable:!1,searchable:!1,clipboardCopy:!1,resizable:!0,hideable:!0,attr:"",style:"",render:null,title:null,editable:{},frozen:!1,info:null},msgDelete:"Are you sure you want to delete NN records?",msgDeleteBtn:"Delete",msgNotJSON:"Returned data is not in valid JSON format.",msgAJAXerror:"AJAX error. See console for more details.",msgRefresh:"Refreshing...",msgNeedReload:"Your remote data source record count has changed, reloading from the first record.",msgEmpty:"",buttons:{reload:{type:"button",id:"w2ui-reload",icon:"w2ui-icon-reload",tooltip:"Reload data in the list"},columns:{type:"drop",id:"w2ui-column-on-off",icon:"w2ui-icon-columns",tooltip:"Show/hide columns",arrow:!1,html:""},search:{type:"html",id:"w2ui-search",html:"<div class=\"w2ui-icon icon-search-down w2ui-search-down\" onclick=\"var grid = w2ui[jQuery(this).parents('div.w2ui-grid').attr('name')]; grid.searchShowFields()\"></div>"},"search-go":{type:"drop",id:"w2ui-search-advanced",icon:"w2ui-icon-search",text:"Search",tooltip:"Open Search Fields"},add:{type:"button",id:"w2ui-add",text:"Add New",tooltip:"Add new record",icon:"w2ui-icon-plus"},edit:{type:"button",id:"w2ui-edit",text:"Edit",tooltip:"Edit selected record",icon:"w2ui-icon-pencil",disabled:!0},delete:{type:"button",id:"w2ui-delete",text:"Delete",tooltip:"Delete selected records",icon:"w2ui-icon-cross",disabled:!0},save:{type:"button",id:"w2ui-save",text:"Save",tooltip:"Save changed records",icon:"w2ui-icon-check"}},operators:{text:["is","begins","contains","ends"],number:["=","between",">","<",">=","<="],date:["is","between",{oper:"less",text:"before"},{oper:"more",text:"after"}],list:["is"],hex:["is","between"],color:["is","begins","contains","ends"],enum:["in","not in"]},operatorsMap:{text:"text",int:"number",float:"number",money:"number",currency:"number",percent:"number",hex:"hex",alphanumeric:"text",color:"color",date:"date",time:"date",datetime:"date",list:"list",combo:"text",enum:"enum",file:"enum",select:"list",radio:"list",checkbox:"list",toggle:"list"},onAdd:null,onEdit:null,onRequest:null,onLoad:null,onDelete:null,onSave:null,onSelect:null,onUnselect:null,onClick:null,onDblClick:null,onContextMenu:null,onMenuClick:null,onColumnClick:null,onColumnDblClick:null,onColumnResize:null,onColumnAutoResize:null,onSort:null,onSearch:null,onSearchOpen:null,onChange:null,onRestore:null,onExpand:null,onCollapse:null,onError:null,onKeydown:null,onToolbar:null,onColumnOnOff:null,onCopy:null,onPaste:null,onSelectionExtend:null,onEditField:null,onRender:null,onRefresh:null,onReload:null,onResize:null,onDestroy:null,onStateSave:null,onStateRestore:null,onFocus:null,onBlur:null,onReorderRow:null,add:function(e,t){$.isArray(e)||(e=[e]);for(var i=0,s=0;s<e.length;s++){var n=e[s];null!=n[this.recid]&&(n.recid=n[this.recid]),null!=n.recid?(n.w2ui&&!0===n.w2ui.summary?t?this.summary.unshift(n):this.summary.push(n):t?this.records.unshift(n):this.records.push(n),i++):console.log("ERROR: Cannot add record without recid. (obj: "+this.name+")")}return("object"!=typeof this.url?this.url:this.url.get)?this.refresh():(this.total=this.records.length,this.localSort(!1,!0),this.localSearch(),this.refreshBody(),this.resizeRecords()),i},find:function(e,t){var i=[],s=!1;for(l in e=null==e?{}:e)-1!=String(l).indexOf(".")&&(s=!0);for(var n=0;n<this.records.length;n++){var l,o=!0;for(l in e){var a=this.records[n][l];s&&-1!=String(l).indexOf(".")&&(a=this.parseField(this.records[n],l)),"not-null"==e[l]?null!=a&&""!==a||(o=!1):e[l]!=a&&(o=!1)}o&&!0!==t&&i.push(this.records[n].recid),o&&!0===t&&i.push(n)}return i},set:function(e,t,i){if("object"==typeof e&&null!==e&&(i=t,t=e,e=null),null==e){for(var s=0;s<this.records.length;s++)$.extend(!0,this.records[s],t);!0!==i&&this.refresh()}else{var n=this.get(e,!0);if(null==n)return!1;!this.records[n]||this.records[n].recid!=e?$.extend(!0,this.summary[n],t):$.extend(!0,this.records[n],t),!0!==i&&this.refreshRow(e,n)}return!0},get:function(e,t){if($.isArray(e)){for(var i=[],s=0;s<e.length;s++){var n=this.get(e[s],t);null!==n&&i.push(n)}return i}var l=this.last.idCache;if(l||(this.last.idCache=l={}),"number"==typeof(s=l[e])){if(0<=s&&s<this.records.length&&this.records[s].recid==e)return!0===t?s:this.records[s];if(0<=(s=~s)&&s<this.summary.length&&this.summary[s].recid==e)return!0===t?s:this.summary[s];this.last.idCache=l={}}for(s=0;s<this.records.length;s++)if(this.records[s].recid==e)return l[e]=s,!0===t?s:this.records[s];for(s=0;s<this.summary.length;s++)if(this.summary[s].recid==e)return l[e]=~s,!0===t?s:this.summary[s];return null},getFirst:function(){if(0==this.records.length)return null;var e=this.records[0].recid,t=this.last.searchIds;return e=0<this.searchData.length?Array.isArray(t)&&0<t.length?this.records[t[0]].recid:null:e},remove:function(){for(var e=0,t=0;t<arguments.length;t++){for(var i=this.records.length-1;0<=i;i--)this.records[i].recid==arguments[t]&&(this.records.splice(i,1),e++);for(i=this.summary.length-1;0<=i;i--)this.summary[i].recid==arguments[t]&&(this.summary.splice(i,1),e++)}return("object"!=typeof this.url?this.url:this.url.get)||(this.localSort(!1,!0),this.localSearch()),this.refresh(),e},addColumn:function(e,t){var i=0;1==arguments.length?(t=e,e=this.columns.length):null==(e="string"==typeof e?this.getColumn(e,!0):e)&&(e=this.columns.length),$.isArray(t)||(t=[t]);for(var s,n,l=0;l<t.length;l++)this.columns.splice(e,0,t[l]),t[l].searchable&&(s=t[l].searchable,!(n="")===t[l].searchable&&(s="text",n='size="20"'),this.addSearch({field:t[l].field,label:t[l].label,type:s,attr:n})),e++,i++;return this.refresh(),i},removeColumn:function(){for(var e=0,t=0;t<arguments.length;t++)for(var i=this.columns.length-1;0<=i;i--)this.columns[i].field==arguments[t]&&(this.columns[i].searchable&&this.removeSearch(arguments[t]),this.columns.splice(i,1),e++);return this.refresh(),e},getColumn:function(e,t){if(0===arguments.length){for(var i=[],s=0;s<this.columns.length;s++)i.push(this.columns[s].field);return i}for(s=0;s<this.columns.length;s++)if(this.columns[s].field==e)return!0===t?s:this.columns[s];return null},updateColumn:function(e,s){var t=this,n=0;return(e=Array.isArray(e)?e:[e]).forEach(function(e){t.columns.forEach(function(t){var i;t.field==e&&(i=$.extend(!0,{},s),Object.keys(i).forEach(function(e){"function"==typeof i[e]&&(i[e]=i[e](t)),t[e]!=i[e]&&n++}),$.extend(!0,t,i))})}),0<n&&this.refresh(),n},toggleColumn:function(){return this.updateColumn(Array.from(arguments),{hidden:function(e){return!e.hidden}})},showColumn:function(){return this.updateColumn(Array.from(arguments),{hidden:!1})},hideColumn:function(){return this.updateColumn(Array.from(arguments),{hidden:!0})},addSearch:function(e,t){var i=0;1==arguments.length?(t=e,e=this.searches.length):null==(e="string"==typeof e?this.getSearch(e,!0):e)&&(e=this.searches.length),$.isArray(t)||(t=[t]);for(var s=0;s<t.length;s++)this.searches.splice(e,0,t[s]),e++,i++;return this.searchClose(),i},removeSearch:function(){for(var e=0,t=0;t<arguments.length;t++)for(var i=this.searches.length-1;0<=i;i--)this.searches[i].field==arguments[t]&&(this.searches.splice(i,1),e++);return this.searchClose(),e},getSearch:function(e,t){if(0===arguments.length){for(var i=[],s=0;s<this.searches.length;s++)i.push(this.searches[s].field);return i}for(s=0;s<this.searches.length;s++)if(this.searches[s].field==e)return!0===t?s:this.searches[s];return null},toggleSearch:function(){for(var e=0,t=0;t<arguments.length;t++)for(var i=this.searches.length-1;0<=i;i--)this.searches[i].field==arguments[t]&&(this.searches[i].hidden=!this.searches[i].hidden,e++);return this.searchClose(),e},showSearch:function(){for(var e=0,t=0;t<arguments.length;t++)for(var i=this.searches.length-1;0<=i;i--)this.searches[i].field==arguments[t]&&!1!==this.searches[i].hidden&&(this.searches[i].hidden=!1,e++);return this.searchClose(),e},hideSearch:function(){for(var e=0,t=0;t<arguments.length;t++)for(var i=this.searches.length-1;0<=i;i--)this.searches[i].field==arguments[t]&&!0!==this.searches[i].hidden&&(this.searches[i].hidden=!0,e++);return this.searchClose(),e},getSearchData:function(e){for(var t=0;t<this.searchData.length;t++)if(this.searchData[t].field==e)return this.searchData[t];return null},localSort:function(e,t){if("object"!=typeof this.url?this.url:this.url.get)console.log("ERROR: grid.localSort can only be used on local data source, grid.url should be empty.");else if(!$.isEmptyObject(this.sortData)){var i=(new Date).getTime(),r=this;r.selectionSave(),r.prepareData(),t||r.reset();for(var s=0;s<this.sortData.length;s++){var n=this.getColumn(this.sortData[s].field);if(!n)return;"string"==typeof n.render&&(-1!=["date","age"].indexOf(n.render.split(":")[0])&&(this.sortData[s].field_=n.field+"_"),-1!=["time"].indexOf(n.render.split(":")[0])&&(this.sortData[s].field_=n.field+"_"))}return function(){for(var e=0;e<r.records.length;e++){var t=r.records[e];t.w2ui&&null!=t.w2ui.parent_recid&&(t.w2ui._path=o(t))}}(),this.records.sort(function(e,t){if(!(e.w2ui&&null!=e.w2ui.parent_recid||t.w2ui&&null!=t.w2ui.parent_recid))return a(e,t);for(var i=o(e),s=o(t),n=0;n<Math.min(i.length,s.length);n++){var l=a(i[n],s[n]);if(0!==l)return l}return i.length>s.length?1:i.length<s.length?-1:(console.log("ERROR: two paths should not be equal."),0)}),function(){for(var e=0;e<r.records.length;e++){var t=r.records[e];t.w2ui&&null!=t.w2ui.parent_recid&&(t.w2ui._path=null)}}(),r.selectionRestore(t),i=(new Date).getTime()-i,!0!==e&&r.show.statusSort&&setTimeout(function(){r.status(w2utils.lang("Sorting took")+" "+i/1e3+" "+w2utils.lang("sec"))},10),i}function o(e){if(!e.w2ui||null==e.w2ui.parent_recid)return[e];if(e.w2ui._path)return e.w2ui._path;var t=r.get(e.w2ui.parent_recid);return t?o(t).concat(e):(console.log("ERROR: no parent record: "+e.w2ui.parent_recid),[e])}function a(e,t){if(e===t)return 0;for(var i=0;i<r.sortData.length;i++){var s=r.sortData[i].field,n=r.sortData[i].field_||s,l=e[n],o=t[n];-1!=String(s).indexOf(".")&&(l=r.parseField(e,n),o=r.parseField(t,n));var a,s=r.getColumn(s);if(s&&null!=s.editable&&($.isPlainObject(l)&&l.text&&(l=l.text),$.isPlainObject(o)&&o.text&&(o=o.text)),0!==(a=d(l,o,0,r.sortData[i].direction,s.sortMode||"default")))return a}return a=d(e.recid,t.recid,0,"asc")}function d(e,t,i,s,n){if(e===t)return 0;if((null==e||""===e)&&null!=t&&""!==t)return 1;if(null!=e&&""!==e&&(null==t||""===t))return-1;var l="asc"===s.toLowerCase()?1:-1;if(typeof e!=typeof t)return typeof t<typeof e?l:-l;if(e.constructor.name!=t.constructor.name)return e.constructor.name>t.constructor.name?l:-l;e&&"object"==typeof e&&(e=e.valueOf()),t&&"object"==typeof t&&(t=t.valueOf());s={}.toString;return e&&"object"==typeof e&&e.toString!=s&&(e=String(e)),t&&"object"==typeof t&&t.toString!=s&&(t=String(t)),"string"==typeof e&&(e=$.trim(e.toLowerCase())),"string"==typeof t&&(t=$.trim(t.toLowerCase())),"function"==typeof(n="natural"===n?w2utils.naturalCompare:n)?n(e,t)*l:t<e?l:e<t?-l:0}},localSearch:function(e){var t="object"!=typeof this.url?this.url:this.url.get;if(!t){var i=(new Date).getTime(),g=this,m={}.toString,s={};if(this.total=this.records.length,this.last.searchIds=[],this.prepareData(),0<this.searchData.length&&!t){for(var n=this.total=0;n<this.records.length;n++){var l=this.records[n];if(function e(t){var i=0;var s=!1;for(var n=0;n<g.searchData.length;n++){var l=g.searchData[n],o=g.getSearch(l.field);if(null!=l){null==o&&(o={field:l.field,type:l.type});var a,r,d=g.parseField(t,o.field),u=null==d||"object"==typeof d&&d.toString==m?"":String(d).toLowerCase();switch(null!=l.value&&($.isArray(l.value)?(r=l.value[0],a=l.value[1]):r=String(l.value).toLowerCase()),l.operator){case"=":case"is":g.parseField(t,o.field)==l.value?i++:"date"==o.type?(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.formatDate(c,"yyyy-mm-dd"),r=w2utils.formatDate(w2utils.isDate(r,w2utils.settings.dateFormat,!0),"yyyy-mm-dd"),u==r&&i++):"time"==o.type?(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.formatTime(c,"hh24:mi"),r=w2utils.formatTime(r,"hh24:mi"),u==r&&i++):"datetime"==o.type&&(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.formatDateTime(c,"yyyy-mm-dd|hh24:mm:ss"),r=w2utils.formatDateTime(w2utils.isDateTime(r,w2utils.settings.datetimeFormat,!0),"yyyy-mm-dd|hh24:mm:ss"),u==r&&i++);break;case"between":-1!=["int","float","money","currency","percent"].indexOf(o.type)?parseFloat(g.parseField(t,o.field))>=parseFloat(r)&&parseFloat(g.parseField(t,o.field))<=parseFloat(a)&&i++:"date"==o.type?(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.isDate(c,w2utils.settings.dateFormat,!0),r=w2utils.isDate(r,w2utils.settings.dateFormat,!0),null!=(a=w2utils.isDate(a,w2utils.settings.dateFormat,!0))&&(a=new Date(a.getTime()+864e5)),r<=u&&u<a&&i++):"time"==o.type?(u=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),r=w2utils.isTime(r,!0),a=w2utils.isTime(a,!0),r=(new Date).setHours(r.hours,r.minutes,r.seconds||0,0),a=(new Date).setHours(a.hours,a.minutes,a.seconds||0,0),r<=u&&u<a&&i++):"datetime"==o.type&&(u=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),r=w2utils.isDateTime(r,w2utils.settings.datetimeFormat,!0),a=(a=w2utils.isDateTime(a,w2utils.settings.datetimeFormat,!0))&&new Date(a.getTime()+864e5),r<=u&&u<a&&i++);break;case"<=":s=!0;case"<":case"less":-1!=["int","float","money","currency","percent"].indexOf(o.type)?(u=parseFloat(g.parseField(t,o.field)),r=parseFloat(l.value),(u<r||s&&u===r)&&i++):"date"==o.type?(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.isDate(c,w2utils.settings.dateFormat,!0),r=w2utils.isDate(r,w2utils.settings.dateFormat,!0),(u<r||s&&u===r)&&i++):"time"==o.type?(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.formatTime(c,"hh24:mi"),r=w2utils.formatTime(r,"hh24:mi"),(u<r||s&&u===r)&&i++):"datetime"==o.type&&(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.formatDateTime(c,"yyyy-mm-dd|hh24:mm:ss"),r=w2utils.formatDateTime(w2utils.isDateTime(r,w2utils.settings.datetimeFormat,!0),"yyyy-mm-dd|hh24:mm:ss"),u.length==r.length&&(u<r||s&&u===r)&&i++);break;case">=":s=!0;case">":case"more":-1!=["int","float","money","currency","percent"].indexOf(o.type)?(u=parseFloat(g.parseField(t,o.field)),((r=parseFloat(l.value))<u||s&&u===r)&&i++):"date"==o.type?(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.isDate(c,w2utils.settings.dateFormat,!0),((r=w2utils.isDate(r,w2utils.settings.dateFormat,!0))<u||s&&u===r)&&i++):"time"==o.type?(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.formatTime(c,"hh24:mi"),((r=w2utils.formatTime(r,"hh24:mi"))<u||s&&u===r)&&i++):"datetime"==o.type&&(c=g.parseField(t,o.field+"_")instanceof Date?g.parseField(t,o.field+"_"):g.parseField(t,o.field),u=w2utils.formatDateTime(c,"yyyy-mm-dd|hh24:mm:ss"),r=w2utils.formatDateTime(w2utils.isDateTime(r,w2utils.settings.datetimeFormat,!0),"yyyy-mm-dd|hh24:mm:ss"),u.length==r.length&&(r<u||s&&u===r)&&i++);break;case"in":var c=l.value;-1===(c=l.svalue?l.svalue:c).indexOf(w2utils.isFloat(d)?parseFloat(d):d)&&-1===c.indexOf(u)||i++;break;case"not in":var c=l.value;-1===(c=l.svalue?l.svalue:c).indexOf(w2utils.isFloat(d)?parseFloat(d):d)&&-1===c.indexOf(u)&&i++;break;case"begins":case"begins with":0===u.indexOf(r)&&i++;break;case"contains":0<=u.indexOf(r)&&i++;break;case"null":null==g.parseField(t,o.field)&&i++;break;case"not null":null!=g.parseField(t,o.field)&&i++;break;case"ends":case"ends with":var h=u.lastIndexOf(r);-1!==h&&h==u.length-r.length&&i++}}}if("OR"==g.last.logic&&0!==i||"AND"==g.last.logic&&i==g.searchData.length)return!0;if(t.w2ui&&t.w2ui.children&&!0!==t.w2ui.expanded)for(var p=0;p<t.w2ui.children.length;p++){var f=t.w2ui.children[p];if(e(f))return!0}return!1}(l))if(l&&l.w2ui&&!function e(t){if(void 0===t)return;if(s[t])return;s[t]=!0;var i=g.get(t,!0);if(null==i)return;if(-1!=$.inArray(i,g.last.searchIds))return;var t=g.records[i];t&&t.w2ui&&e(t.w2ui.parent_recid);g.last.searchIds.push(i)}(l.w2ui.parent_recid),0<this.showExtraOnSearch){var l=this.showExtraOnSearch,o=this.showExtraOnSearch;if(n<l&&(l=n),n+o>this.records.length&&(o=this.records.length-n),0<l)for(var a=n-l;a<n;a++)this.last.searchIds.indexOf(a)<0&&this.last.searchIds.push(a);if(this.last.searchIds.indexOf(n)<0&&this.last.searchIds.push(n),0<o)for(a=n+1;a<=n+o;a++)this.last.searchIds.indexOf(a)<0&&this.last.searchIds.push(a)}else this.last.searchIds.push(n)}this.total=this.last.searchIds.length}return i=(new Date).getTime()-i,!0!==e&&g.show.statusSearch&&setTimeout(function(){g.status(w2utils.lang("Search took")+" "+i/1e3+" "+w2utils.lang("sec"))},10),i}console.log("ERROR: grid.localSearch can only be used on local data source, grid.url should be empty.")},getRangeData:function(e,t){var i=this.get(e[0].recid,!0),s=this.get(e[1].recid,!0),n=e[0].column,l=e[1].column,o=[];if(n==l)for(var a=i;a<=s;a++){var r=(d=this.records[a])[this.columns[n].field]||null;!0!==t?o.push(r):o.push({data:r,column:n,index:a,record:d})}else if(i==s)for(var d=this.records[i],u=n;u<=l;u++){r=d[this.columns[u].field]||null;!0!==t?o.push(r):o.push({data:r,column:u,index:i,record:d})}else for(a=i;a<=s;a++){d=this.records[a];o.push([]);for(u=n;u<=l;u++){r=d[this.columns[u].field];!0!==t?o[o.length-1].push(r):o[o.length-1].push({data:r,column:u,index:a,record:d})}}return o},addRange:function(e){var t=0;if("row"==this.selectType)return t;$.isArray(e)||(e=[e]);for(var i=0;i<e.length;i++){if("object"!=typeof e[i]&&(e[i]={name:"selection"}),"selection"==e[i].name){if(!1===this.show.selectionBorder)continue;var s=this.getSelection();if(0===s.length){this.removeRange("selection");continue}var n=s[0],l=s[s.length-1]}else n=e[i].range[0],l=e[i].range[1];if(n){for(var s={name:e[i].name,range:[{recid:n.recid,column:n.column},{recid:l.recid,column:l.column}],style:e[i].style||""},o=!1,a=0;a<this.ranges.length;a++)if(this.ranges[a].name==e[i].name){o=a;break}!1!==o?this.ranges[o]=s:this.ranges.push(s),t++}}return this.refreshRanges(),t},removeRange:function(){for(var e=0,t=0;t<arguments.length;t++){var i=arguments[t];$("#grid_"+this.name+"_"+i).remove(),$("#grid_"+this.name+"_f"+i).remove();for(var s=this.ranges.length-1;0<=s;s--)this.ranges[s].name==i&&(this.ranges.splice(s,1),e++)}return e},refreshRanges:function(){if(0!==this.ranges.length){for(var n=this,e=(new Date).getTime(),t=$("#grid_"+this.name+"_frecords"),i=$("#grid_"+this.name+"_records"),s=0;s<this.ranges.length;s++){var l=this.ranges[s],o=l.range[0],a=l.range[1];null==o.index&&(o.index=this.get(o.recid,!0)),null==a.index&&(a.index=this.get(a.recid,!0));var r=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(o.recid)+' td[col="'+o.column+'"]'),d=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(a.recid)+' td[col="'+a.column+'"]'),u=$("#grid_"+this.name+"_frec_"+w2utils.escapeId(o.recid)+' td[col="'+o.column+'"]'),c=$("#grid_"+this.name+"_frec_"+w2utils.escapeId(a.recid)+' td[col="'+a.column+'"]'),h=a.column;o.column<this.last.colStart&&a.column>this.last.colStart&&(r=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(o.recid)+' td[col="start"]')),o.column<this.last.colEnd&&a.column>this.last.colEnd&&(h='"end"',d=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(a.recid)+' td[col="end"]'));var p=parseInt($("#grid_"+this.name+"_rec_top").next().attr("index")),f=parseInt($("#grid_"+this.name+"_rec_bottom").prev().attr("index")),g=parseInt($("#grid_"+this.name+"_frec_top").next().attr("index")),m=parseInt($("#grid_"+this.name+"_frec_bottom").prev().attr("index"));0===r.length&&o.index<p&&a.index>p&&(r=$("#grid_"+this.name+"_rec_top").next().find("td[col="+o.column+"]")),0===d.length&&a.index>f&&o.index<f&&(d=$("#grid_"+this.name+"_rec_bottom").prev().find("td[col="+h+"]")),0===u.length&&o.index<g&&a.index>g&&(u=$("#grid_"+this.name+"_frec_top").next().find("td[col="+o.column+"]")),0===c.length&&a.index>m&&o.index<m&&(c=$("#grid_"+this.name+"_frec_bottom").prev().find("td[col="+a.column+"]"));var w,v,g=$(this.box).find("#grid_"+this.name+"_editable").find(".w2ui-input"),m=g.attr("recid"),g=g.attr("column");"selection"==l.name&&l.range[0].recid==m&&l.range[0].column==g||(g=$("#grid_"+this.name+"_f"+l.name),0<u.length||0<c.length?(0===g.length?(t.append('<div id="grid_'+this.name+"_f"+l.name+'" class="w2ui-selection" style="'+l.style+'">'+("selection"==l.name?'<div id="grid_'+this.name+'_resizer" class="w2ui-selection-resizer"></div>':"")+"</div>"),g=$("#grid_"+this.name+"_f"+l.name)):(g.attr("style",l.style),g.find(".w2ui-selection-resizer").show()),0===c.length&&(0===(c=$("#grid_"+this.name+"_frec_"+w2utils.escapeId(a.recid)+" td:last-child")).length&&(c=$("#grid_"+this.name+"_frec_bottom td:first-child")),g.css("border-right","0px"),g.find(".w2ui-selection-resizer").hide()),null!=o.recid&&null!=a.recid&&0<u.length&&0<c.length?(w=+u.position().left+t.scrollLeft(),v=+u.position().top+t.scrollTop(),g.show().css({left:(0<w?w:0)+"px",top:(0<v?v:0)+"px",width:c.position().left-u.position().left+c.width()+2+"px",height:c.position().top-u.position().top+c.height()+1+"px"})):g.hide()):g.hide(),g=$("#grid_"+this.name+"_"+l.name),0<r.length||0<d.length?(0===g.length?(i.append('<div id="grid_'+this.name+"_"+l.name+'" class="w2ui-selection" style="'+l.style+'">'+("selection"==l.name?'<div id="grid_'+this.name+'_resizer" class="w2ui-selection-resizer"></div>':"")+"</div>"),g=$("#grid_"+this.name+"_"+l.name)):g.attr("style",l.style),0===r.length&&0===(r=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(o.recid)+" td:first-child")).length&&(r=$("#grid_"+this.name+"_rec_top td:first-child")),0!==c.length&&g.css("border-left","0px"),null!=o.recid&&null!=a.recid&&0<r.length&&0<d.length?(w=+r.position().left+i.scrollLeft(),v=+r.position().top+i.scrollTop(),g.show().css({left:(0<w?w:0)+"px",top:(0<v?v:0)+"px",width:d.position().left-r.position().left+d.width()+2+"px",height:d.position().top-r.position().top+d.height()+1+"px"})):g.hide()):g.hide())}$(this.box).find(".w2ui-selection-resizer").off("mousedown").on("mousedown",function(e){var t=n.getSelection();n.last.move={type:"expand",x:e.screenX,y:e.screenY,divX:0,divY:0,recid:t[0].recid,column:t[0].column,originalRange:[{recid:t[0].recid,column:t[0].column},{recid:t[t.length-1].recid,column:t[t.length-1].column}],newRange:[{recid:t[0].recid,column:t[0].column},{recid:t[t.length-1].recid,column:t[t.length-1].column}]},$(document).off(".w2ui-"+n.name).on("mousemove.w2ui-"+n.name,b).on("mouseup.w2ui-"+n.name,x),e.preventDefault()}).off("dblclick").on("dblclick",function(e){e=n.trigger({phase:"before",type:"resizerDblClick",target:n.name,originalEvent:e});!0!==e.isCancelled&&n.trigger($.extend(e,{phase:"after"}))});var y={phase:"before",type:"selectionExtend",target:n.name,originalRange:null,newRange:null};return(new Date).getTime()-e}function b(e){var t,i,s=n.last.move;s&&"expand"==s.type&&(s.divX=e.screenX-s.x,s.divY=e.screenY-s.y,"TD"!=(i=e.originalEvent.target).tagName.toUpperCase()&&(i=$(i).parents("td")[0]),null!=$(i).attr("col")&&(t=parseInt($(i).attr("col"))),i=$(i).parents("tr")[0],e=$(i).attr("recid"),s.newRange[1].recid==e&&s.newRange[1].column==t||(i=$.extend({},s.newRange),s.newRange=[{recid:s.recid,column:s.column},{recid:e,column:t}],!0===(y=n.trigger($.extend(y,{originalRange:s.originalRange,newRange:s.newRange}))).isCancelled?(s.newRange=i,y.newRange=i):(n.removeRange("grid-selection-expand"),n.addRange({name:"grid-selection-expand",range:y.newRange,style:"background-color: rgba(100,100,100,0.1); border: 2px dotted rgba(100,100,100,0.5);"}))))}function x(e){n.removeRange("grid-selection-expand"),delete n.last.move,$(document).off(".w2ui-"+n.name),n.trigger($.extend(y,{phase:"after"}))}},select:function(){if(0===arguments.length)return 0;(new Date).getTime();var e=0,t=this.last.selection;this.multiSelect||this.selectNone();var i=Array.prototype.slice.call(arguments);Array.isArray(i[0])&&(i=i[0]);var s={phase:"before",type:"select",target:this.name};1==i.length?(s.multiple=!1,$.isPlainObject(i[0])?(s.recid=i[0].recid,s.column=i[0].column):s.recid=i[0]):(s.multiple=!0,s.recids=i);var n=this.trigger(s);if(!0===n.isCancelled)return 0;if("row"==this.selectType)for(var l=0;l<i.length;l++){var o="object"==typeof i[l]?i[l].recid:i[l];null!=(u=this.get(o,!0))&&(p=h=null,(0!==this.searchData.length||u+1>=this.last.range_start&&u+1<=this.last.range_end)&&(h=$("#grid_"+this.name+"_frec_"+w2utils.escapeId(o)),p=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(o))),"row"==this.selectType&&-1==t.indexes.indexOf(u)&&(t.indexes.push(u),h&&p&&(h.addClass("w2ui-selected").data("selected","yes").find(".w2ui-col-number").addClass("w2ui-row-selected"),p.addClass("w2ui-selected").data("selected","yes").find(".w2ui-col-number").addClass("w2ui-row-selected"),h.find(".w2ui-grid-select-check").prop("checked",!0)),e++))}else{for(var a={},l=0;l<i.length;l++){var o="object"==typeof i[l]?i[l].recid:i[l],r="object"==typeof i[l]?i[l].column:null;if(a[o]=a[o]||[],$.isArray(r))a[o]=r;else if(w2utils.isInt(r))a[o].push(r);else for(var d=0;d<this.columns.length;d++)this.columns[d].hidden||a[o].push(parseInt(d))}var u,c=[];for(o in a)if(null!=(u=this.get(o,!0))){var h=null,p=null;u+1>=this.last.range_start&&u+1<=this.last.range_end&&(h=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(o)),p=$("#grid_"+this.name+"_frec_"+w2utils.escapeId(o)));var f=t.columns[u]||[];-1==t.indexes.indexOf(u)&&t.indexes.push(u);for(var g=0;g<a[o].length;g++)-1==f.indexOf(a[o][g])&&f.push(a[o][g]);f.sort(function(e,t){return e-t});for(g=0;g<a[o].length;g++){var m=a[o][g];-1==c.indexOf(m)&&c.push(m),h&&(h.find("#grid_"+this.name+"_data_"+u+"_"+m).addClass("w2ui-selected"),h.find(".w2ui-col-number").addClass("w2ui-row-selected"),h.data("selected","yes"),h.find(".w2ui-grid-select-check").prop("checked",!0)),p&&(p.find("#grid_"+this.name+"_data_"+u+"_"+m).addClass("w2ui-selected"),p.find(".w2ui-col-number").addClass("w2ui-row-selected"),p.data("selected","yes"),p.find(".w2ui-grid-select-check").prop("checked",!0)),e++}t.columns[u]=f}for(var w=0;w<c.length;w++)$(this.box).find("#grid_"+this.name+"_column_"+c[w]+" .w2ui-col-header").addClass("w2ui-col-selected")}t.indexes.sort(function(e,t){return e-t});var v=0<this.records.length&&t.indexes.length==this.records.length,s=0<t.indexes.length&&0!==this.searchData.length&&t.indexes.length==this.last.searchIds.length;return v||s?$("#grid_"+this.name+"_check_all").prop("checked",!0):$("#grid_"+this.name+"_check_all").prop("checked",!1),this.status(),this.addRange("selection"),this.updateToolbar(t,v),this.trigger($.extend(n,{phase:"after"})),e},unselect:function(){var e=0,t=this.last.selection,i=Array.prototype.slice.call(arguments);Array.isArray(i[0])&&(i=i[0]);var s={phase:"before",type:"unselect",target:this.name};1==i.length?(s.multiple=!1,$.isPlainObject(i[0])?(s.recid=i[0].recid,s.column=i[0].column):s.recid=i[0]):(s.multiple=!0,s.recids=i);var n=this.trigger(s);if(!0===n.isCancelled)return 0;for(var l=0;l<i.length;l++){var o="object"==typeof i[l]?i[l].recid:i[l],a=this.get(o);if(null!=a){var r=this.get(a.recid,!0),d=$("#grid_"+this.name+"_frec_"+w2utils.escapeId(o)),u=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(o));if("row"==this.selectType)-1!=t.indexes.indexOf(r)&&(t.indexes.splice(t.indexes.indexOf(r),1),d.removeClass("w2ui-selected w2ui-inactive").removeData("selected").find(".w2ui-col-number").removeClass("w2ui-row-selected"),u.removeClass("w2ui-selected w2ui-inactive").removeData("selected").find(".w2ui-col-number").removeClass("w2ui-row-selected"),0!=d.length&&(d[0].style.cssText="height: "+this.recordHeight+"px; "+d.attr("custom_style"),u[0].style.cssText="height: "+this.recordHeight+"px; "+u.attr("custom_style")),d.find(".w2ui-grid-select-check").prop("checked",!1),e++);else{var c=i[l].column;if(!w2utils.isInt(c)){for(var h=[],p=0;p<this.columns.length;p++)this.columns[p].hidden||h.push({recid:o,column:p});return this.unselect(h)}a=t.columns[r];if($.isArray(a)&&-1!=a.indexOf(c)){a.splice(a.indexOf(c),1),$("#grid_"+this.name+"_rec_"+w2utils.escapeId(o)).find(" > td[col="+c+"]").removeClass("w2ui-selected w2ui-inactive"),$("#grid_"+this.name+"_frec_"+w2utils.escapeId(o)).find(" > td[col="+c+"]").removeClass("w2ui-selected w2ui-inactive");for(var f=!1,g=!1,s=this.getSelection(),p=0;p<s.length;p++)s[p].column==c&&(f=!0),s[p].recid==o&&(g=!0);f||$(this.box).find(".w2ui-grid-columns td[col="+c+"] .w2ui-col-header, .w2ui-grid-fcolumns td[col="+c+"] .w2ui-col-header").removeClass("w2ui-col-selected"),g||$("#grid_"+this.name+"_frec_"+w2utils.escapeId(o)).find(".w2ui-col-number").removeClass("w2ui-row-selected"),e++,0===a.length&&(delete t.columns[r],t.indexes.splice(t.indexes.indexOf(r),1),d.removeData("selected"),d.find(".w2ui-grid-select-check").prop("checked",!1),u.removeData("selected"))}}}}var m=0<this.records.length&&t.indexes.length==this.records.length,w=0<t.indexes.length&&0!==this.searchData.length&&t.indexes.length==this.last.searchIds.length;return m||w?$("#grid_"+this.name+"_check_all").prop("checked",!0):$("#grid_"+this.name+"_check_all").prop("checked",!1),this.status(),this.addRange("selection"),this.updateToolbar(t,m),this.trigger($.extend(n,{phase:"after"})),e},selectAll:function(){var e=(new Date).getTime();if(!1!==this.multiSelect){var t=this.trigger({phase:"before",type:"select",target:this.name,all:!0});if(!0!==t.isCancelled){for(var i="object"!=typeof this.url?this.url:this.url.get,s=this.last.selection,n=[],l=0;l<this.columns.length;l++)n.push(l);if(s.indexes=[],i||0===this.searchData.length){var o=this.records.length;0==this.searchData.length||i||(o=this.last.searchIds.length);for(l=0;l<o;l++)s.indexes.push(l),"row"!=this.selectType&&(s.columns[l]=n.slice())}else for(var l=0;l<this.last.searchIds.length;l++)s.indexes.push(this.last.searchIds[l]),"row"!=this.selectType&&(s.columns[this.last.searchIds[l]]=n.slice());return"row"==this.selectType?($(this.box).find(".w2ui-grid-records tr").not(".w2ui-empty-record").addClass("w2ui-selected").data("selected","yes").find(".w2ui-col-number").addClass("w2ui-row-selected"),$(this.box).find(".w2ui-grid-frecords tr").not(".w2ui-empty-record").addClass("w2ui-selected").data("selected","yes").find(".w2ui-col-number").addClass("w2ui-row-selected")):($(this.box).find(".w2ui-grid-columns td .w2ui-col-header, .w2ui-grid-fcolumns td .w2ui-col-header").addClass("w2ui-col-selected"),$(this.box).find(".w2ui-grid-records tr .w2ui-col-number").addClass("w2ui-row-selected"),$(this.box).find(".w2ui-grid-records tr").not(".w2ui-empty-record").find(".w2ui-grid-data").not(".w2ui-col-select").addClass("w2ui-selected").data("selected","yes"),$(this.box).find(".w2ui-grid-frecords tr .w2ui-col-number").addClass("w2ui-row-selected"),$(this.box).find(".w2ui-grid-frecords tr").not(".w2ui-empty-record").find(".w2ui-grid-data").not(".w2ui-col-select").addClass("w2ui-selected").data("selected","yes")),$(this.box).find("input.w2ui-grid-select-check").prop("checked",!0),1==(s=this.getSelection(!0)).length?this.toolbar.enable("w2ui-edit"):this.toolbar.disable("w2ui-edit"),1<=s.length?this.toolbar.enable("w2ui-delete"):this.toolbar.disable("w2ui-delete"),this.addRange("selection"),$("#grid_"+this.name+"_check_all").prop("checked",!0),this.status(),this.updateToolbar({indexes:s},!0),this.trigger($.extend(t,{phase:"after"})),(new Date).getTime()-e}}},selectNone:function(){var e=(new Date).getTime(),t=this.trigger({phase:"before",type:"unselect",target:this.name,all:!0});if(!0!==t.isCancelled){var i=this.last.selection;return"row"==this.selectType?($(this.box).find(".w2ui-grid-records tr.w2ui-selected").removeClass("w2ui-selected w2ui-inactive").removeData("selected").find(".w2ui-col-number").removeClass("w2ui-row-selected"),$(this.box).find(".w2ui-grid-frecords tr.w2ui-selected").removeClass("w2ui-selected w2ui-inactive").removeData("selected").find(".w2ui-col-number").removeClass("w2ui-row-selected")):($(this.box).find(".w2ui-grid-columns td .w2ui-col-header, .w2ui-grid-fcolumns td .w2ui-col-header").removeClass("w2ui-col-selected"),$(this.box).find(".w2ui-grid-records tr .w2ui-col-number").removeClass("w2ui-row-selected"),$(this.box).find(".w2ui-grid-frecords tr .w2ui-col-number").removeClass("w2ui-row-selected"),$(this.box).find(".w2ui-grid-data.w2ui-selected").removeClass("w2ui-selected w2ui-inactive").removeData("selected")),$(this.box).find("input.w2ui-grid-select-check").prop("checked",!1),i.indexes=[],i.columns={},this.toolbar.disable("w2ui-edit","w2ui-delete"),this.removeRange("selection"),$("#grid_"+this.name+"_check_all").prop("checked",!1),this.status(),this.updateToolbar(i,!1),this.trigger($.extend(t,{phase:"after"})),(new Date).getTime()-e}},updateToolbar:function(i,e){var s=this,n=i&&i.indexes?i.indexes.length:0;function l(e,t){0===e.batch&&(0<n?s.toolbar.enable(t+e.id):s.toolbar.disable(t+e.id)),null!=e.batch&&!isNaN(e.batch)&&0<e.batch&&(n==e.batch?s.toolbar.enable(t+e.id):s.toolbar.disable(t+e.id)),"function"==typeof e.batch&&e.batch("cell"==s.selectType?i:i?i.indexes:null)}this.toolbar.items.forEach(function(t){l(t,""),Array.isArray(t.items)&&t.items.forEach(function(e){l(e,t.id+":")})})},getSelection:function(e){var t=[],i=this.last.selection;if("row"==this.selectType){for(var s=0;s<i.indexes.length;s++)this.records[i.indexes[s]]&&(!0===e?t.push(i.indexes[s]):t.push(this.records[i.indexes[s]].recid));return t}for(s=0;s<i.indexes.length;s++){var n=i.columns[i.indexes[s]];if(this.records[i.indexes[s]])for(var l=0;l<n.length;l++)t.push({recid:this.records[i.indexes[s]].recid,index:parseInt(i.indexes[s]),column:n[l]})}return t},search:function(e,t){for(var i="object"!=typeof this.url?this.url:this.url.get,s=[],n=this.last.multi,l=this.last.logic,o=this.last.field,a=this.last.search,r=!1,d=0;d<this.searches.length;d++)this.searches[d].hidden&&null!=this.searches[d].value&&(s.push({field:this.searches[d].field,operator:this.searches[d].operator||"is",type:this.searches[d].type,value:this.searches[d].value||""}),r=!0);if(0===arguments.length){for(a="",d=0;d<this.searches.length;d++){var u,c=this.searches[d],h=$("#grid_"+this.name+"_operator_"+d).val(),p=$("#grid_"+this.name+"_field_"+d),f=$("#grid_"+this.name+"_field2_"+d),g=p.val(),m=f.val(),w=null,v=null;if(-1!=["int","float","money","currency","percent"].indexOf(c.type)&&(u=p.data("w2field"),f=f.data("w2field"),u&&(g=u.clean(g)),f&&(m=f.clean(m))),-1!=["list","enum"].indexOf(c.type))if(g=p.data("selected")||{},$.isArray(g)){for(var w=[],y=0;y<g.length;y++)w.push(w2utils.isFloat(g[y].id)?parseFloat(g[y].id):String(g[y].id).toLowerCase()),delete g[y].hidden;$.isEmptyObject(g)&&(g="")}else v=g.text||"",g=g.id||"";if(""!==g&&null!=g||null!=m&&""!==m){var b={field:c.field,type:c.type,operator:h};"between"==h?$.extend(b,{value:[g,m]}):"in"==h&&"string"==typeof g||"not in"==h&&"string"==typeof g?$.extend(b,{value:g.split(",")}):$.extend(b,{value:g}),w&&$.extend(b,{svalue:w}),v&&$.extend(b,{text:v});try{"date"==c.type&&"between"==h&&(b.value[0]=g,b.value[1]=m),"date"==c.type&&"is"==h&&(b.value=g)}catch(e){}s.push(b),n=!0}}l="AND"}if("string"==typeof e&&(1==arguments.length&&(t=e,e="all"),o=e,n=!1,l=r?"AND":"OR",null!=(a=t)))if("all"==e.toLowerCase())if(0<this.searches.length){for(var x,d=0;d<this.searches.length;d++)if(("text"==(c=this.searches[d]).type||"alphanumeric"==c.type&&w2utils.isAlphaNumeric(t)||"int"==c.type&&w2utils.isInt(t)||"float"==c.type&&w2utils.isFloat(t)||"percent"==c.type&&w2utils.isFloat(t)||("hex"==c.type||"color"==c.type)&&w2utils.isHex(t)||"currency"==c.type&&w2utils.isMoney(t)||"money"==c.type&&w2utils.isMoney(t)||"date"==c.type&&w2utils.isDate(t)||"time"==c.type&&w2utils.isTime(t)||"datetime"==c.type&&w2utils.isDateTime(t)||"datetime"==c.type&&w2utils.isDate(t)||"enum"==c.type&&w2utils.isAlphaNumeric(t)||"list"==c.type&&w2utils.isAlphaNumeric(t))&&(b={field:c.field,type:c.type,operator:null!=c.operator?c.operator:"text"==c.type?this.textSearch:"is",value:t},""!=$.trim(t)&&s.push(b)),-1!=["int","float","money","currency","percent"].indexOf(c.type)&&2==$.trim(String(t)).split("-").length&&(x=$.trim(String(t)).split("-"),b={field:c.field,type:c.type,operator:null!=c.operator?c.operator:"between",value:[x[0],x[1]]},s.push(b)),-1!=["list","enum"].indexOf(c.type)){var _=[];null==c.options&&(c.options={}),Array.isArray(c.options.items)||(c.options.items=[]);for(y=0;y<c.options.items;y++){var b=c.options.items[y];try{var k=new RegExp(t,"i");k.test(b)&&_.push(y),b.text&&k.test(b.text)&&_.push(b.id)}catch(e){}}0<_.length&&(b={field:c.field,type:c.type,operator:null!=c.operator?c.operator:"in",value:_},s.push(b))}}else for(d=0;d<this.columns.length;d++){b={field:this.columns[d].field,type:"text",operator:this.textSearch,value:t};s.push(b)}else{var C=$("#grid_"+this.name+"_search_all");if((c=null==(c=this.getSearch(e))?{field:e,type:"text"}:c).field==e&&(this.last.label=c.label),""!==t){var T=this.textSearch,S=t;if(-1!=["date","time","datetime"].indexOf(c.type)&&(T="is"),-1!=["list","enum"].indexOf(c.type)&&(T="is",S=(b=C.data("selected"))&&!$.isEmptyObject(b)?b.id:""),"int"==c.type&&""!==t)if(T="is",-1!=String(t).indexOf("-")&&2==(b=t.split("-")).length&&(T="between",S=[parseInt(b[0]),parseInt(b[1])]),-1!=String(t).indexOf(",")){b=t.split(",");T="in",S=[];for(d=0;d<b.length;d++)S.push(b[d])}null!=c.operator&&(T=c.operator);b={field:c.field,type:c.type,operator:T,value:S};s.push(b)}}if($.isArray(e))for(T="AND",n=!(a=""),l=T="string"==typeof t&&"OR"!=(T=t.toUpperCase())&&"AND"!=T?"AND":T,d=0;d<e.length;d++){var O=e[d];"number"==typeof O.value&&(O.operator="is"),"string"==typeof O.value&&(O.operator=this.textSearch),$.isArray(O.value)&&(O.operator="in"),s.push(O)}l=this.trigger({phase:"before",type:"search",target:this.name,multi:0===arguments.length,searchField:e||"multi",searchValue:e?t:"multi",searchData:s,searchLogic:l});!0!==l.isCancelled&&(this.searchData=l.searchData,this.last.field=o,this.last.search=a,this.last.multi=n,this.last.logic=l.searchLogic,this.last.scrollTop=0,this.last.scrollLeft=0,this.last.selection.indexes=[],this.last.selection.columns={},this.searchClose(),i?(this.last.xhr_offset=0,this.reload()):(this.localSearch(),this.refresh()),this.trigger($.extend(l,{phase:"after"})))},searchOpen:function(){var t,i,s,n;this.box&&0!==this.searches.length&&(i=(t=this).toolbar.get("w2ui-search-advanced"),s="#tb_"+t.toolbar.name+"_item_"+w2utils.escapeId(i.id)+" table.w2ui-button",!0!==(n=this.trigger({phase:"before",type:"searchOpen",target:this.name})).isCancelled?$("#tb_"+this.name+"_toolbar_item_w2ui-search-advanced").w2overlay({html:this.getSearchesHTML(),name:this.name+"-searchOverlay",left:-10,class:"w2ui-grid-searches",onShow:function(){t.initSearches(),$("#w2ui-overlay-"+t.name+"-searchOverlay .w2ui-grid-searches").data("grid-name",t.name);var e=$("#w2ui-overlay-"+this.name+"-searchOverlay .w2ui-grid-searches *[rel=search]");0<e.length&&e[0].focus(),i.checked||(i.checked=!0,$(s).addClass("checked")),t.trigger($.extend(n,{phase:"after"}))},onHide:function(){i.checked=!1,$(s).removeClass("checked")}}):setTimeout(function(){t.toolbar.uncheck("w2ui-search-advanced")},1))},searchClose:function(){this.box&&0!==this.searches.length&&(this.toolbar&&this.toolbar.uncheck("w2ui-search-advanced","w2ui-column-on-off"),$().w2overlay({name:this.name+"-searchOverlay"}))},searchReset:function(e){for(var t=[],i=!1,s=0;s<this.searches.length;s++)this.searches[s].hidden&&null!=this.searches[s].value&&(t.push({field:this.searches[s].field,operator:this.searches[s].operator||"is",type:this.searches[s].type,value:this.searches[s].value||""}),i=!0);var n=this.trigger({phase:"before",type:"search",reset:!0,target:this.name,searchData:t});if(!0!==n.isCancelled){if(this.searchData=n.searchData,this.last.search="",this.last.logic=i?"AND":"OR",0<this.searches.length)if(this.multiSearch&&this.show.searchAll)this.last.field="all",this.last.label=w2utils.lang("All Fields");else{for(var l=0;l<this.searches.length&&(this.searches[l].hidden||!1===this.searches[l].simple);)l++;l>=this.searches.length?(this.last.field="",this.last.label=""):(this.last.field=this.searches[l].field,this.last.label=this.searches[l].label)}this.last.multi=!1,this.last.xhr_offset=0,this.last.scrollTop=0,this.last.scrollLeft=0,this.last.selection.indexes=[],this.last.selection.columns={},this.searchClose(),$("#grid_"+this.name+"_search_all").val("").removeData("selected"),e||this.reload(),this.trigger($.extend(n,{phase:"after"}))}},searchShowFields:function(e){var t=$("#grid_"+this.name+"_search_all");if(!0!==e){for(var i='<div class="w2ui-select-field"><table><tbody>',s=-1;s<this.searches.length;s++){var n=this.searches[s],l=n?n.field:null,o=this.getColumn(l),a=!1,l="This column is hidden";if(1==this.show.searchHiddenMsg&&-1!=s&&(null==o||!0===o.hidden&&!1!==o.hideable)&&(a=!0,null==o&&(l="This column does not exist")),-1==s){if(!this.multiSearch||!this.show.searchAll)continue;n={field:"all",label:w2utils.lang("All Fields")}}else{if(null!=o&&!1===o.hideable)continue;if(!0===this.searches[s].hidden||!1===this.searches[s].simple)continue}null==n.label&&null!=n.caption&&(console.log("NOTICE: grid search.caption property is deprecated, please use search.label. Search ->",n),n.label=n.caption),i+='<tr style="'+(a?"color: silver":"")+'" '+(a?"onmouseenter=\"jQuery(this).w2tag({ top: 4, html: '"+l+'\' })" onmouseleave="jQuery(this).w2tag()"':"")+(w2utils.isIOS?"onTouchStart":"onClick")+"=\"event.stopPropagation();            w2ui['"+this.name+"'].initAllField('"+n.field+"');           jQuery('#grid_"+this.name+"_search_all').w2overlay({ name: '"+this.name+"-searchFields' });\"           jQuery(this).addClass('w2ui-selected');      onmousedown=\"jQuery(this).parent().find('tr').removeClass('w2ui-selected'); jQuery(this).addClass('w2ui-selected')\"       onmouseup=\"jQuery(this).removeClass('w2ui-selected')\"    >   <td>       <span class=\"w2ui-column-check w2ui-icon-"+(n.field==this.last.field?"check":"empty")+'"></span>   </td>   <td>'+n.label+"</td></tr>"}i+="</tbody></table></div>";var r=this.name+"-searchFields";1==$("#w2ui-overlay-"+r).length&&(i=""),setTimeout(function(){$(t).w2overlay({html:i,name:r,left:-10})},1)}else $(t).w2overlay({name:this.name+"-searchFields"})},initAllField:function(e,t){var i=$("#grid_"+this.name+"_search_all");if("all"==e){var s={field:"all",label:w2utils.lang("All Fields")};i.w2field("clear")}else{if(null==(s=this.getSearch(e)))return;e=s.type;-1!=["enum","select"].indexOf(e)&&(e="list"),i.w2field(e,$.extend({},s.options,{suffix:"",autoFormat:!1,selected:t})),-1!=["list","enum","date","time","datetime"].indexOf(s.type)&&(this.last.search="",this.last.item="",i.val(""),$("#grid_"+this.name+"_searchClear").hide())}""!=this.last.search?(this.last.label=s.label,this.search(s.field,this.last.search)):(this.last.field=s.field,this.last.label=s.label),i.attr("placeholder",w2utils.lang(s.label||s.caption||s.field)),$().w2overlay({name:this.name+"-searchFields"})},clear:function(e){this.total=0,this.records=[],this.summary=[],this.last.xhr_offset=0,this.last.idCache={},this.reset(!0),e||this.refresh()},reset:function(e){this.last.scrollTop=0,this.last.scrollLeft=0,this.last.selection={indexes:[],columns:{}},this.last.range_start=null,this.last.range_end=null,$("#grid_"+this.name+"_records").prop("scrollTop",0),e||this.refresh()},skip:function(e,t){("object"!=typeof this.url?this.url:this.url.get)?(this.offset=parseInt(e),this.offset>this.total&&(this.offset=this.total-this.limit),(this.offset<0||!w2utils.isInt(this.offset))&&(this.offset=0),this.clear(!0),this.reload(t)):console.log("ERROR: grid.skip() can only be called when you have remote data source.")},load:function(e,t){null!=e?(this.clear(!0),this.request("get",{},e,t)):console.log('ERROR: You need to provide url argument when calling .load() method of "'+this.name+'" object.')},reload:function(e){var t=this,i="object"!=typeof this.url?this.url:this.url.get;t.selectionSave(),i?this.load(i,function(){t.selectionRestore(),"function"==typeof e&&e()}):(this.reset(!0),this.localSearch(),this.selectionRestore(),"function"==typeof e&&e({status:"success"}))},request:function(n,e,t,l){if(null==e&&(e={}),""!=(t=""==t||null==t?this.url:t)&&null!=t){w2utils.isInt(this.offset)||(this.offset=0),w2utils.isInt(this.last.xhr_offset)||(this.last.xhr_offset=0);var i={limit:this.limit,offset:parseInt(this.offset)+parseInt(this.last.xhr_offset),searchLogic:this.last.logic,search:this.searchData.map(function(e){e=$.extend({},e);return this.searchMap&&this.searchMap[e.field]&&(e.field=this.searchMap[e.field]),e}.bind(this)),sort:this.sortData.map(function(e){e=$.extend({},e);return this.sortMap&&this.sortMap[e.field]&&(e.field=this.sortMap[e.field]),e}.bind(this))};if(0===this.searchData.length&&(delete i.search,delete i.searchLogic),0===this.sortData.length&&delete i.sort,$.extend(i,this.postData),$.extend(i,e),"delete"!=n&&"save"!=n||(delete i.limit,delete i.offset,"delete"==(i.action=n)&&(i[this.recid||"recid"]=this.getSelection())),"get"==n){if(!0===(s=this.trigger({phase:"before",type:"request",target:this.name,url:t,postData:i,httpHeaders:this.httpHeaders})).isCancelled)return void("function"==typeof l&&l({status:"error",message:"Request aborted."}))}else var s={url:t,postData:i,httpHeaders:this.httpHeaders};var o=this;if(0===this.last.xhr_offset&&this.lock(w2utils.lang(this.msgRefresh),!0),this.last.xhr)try{this.last.xhr.abort()}catch(e){}if(t="object"!=typeof s.url?s.url:s.url.get,"save"==n&&"object"==typeof s.url&&(t=s.url.save),"delete"==n&&"object"==typeof s.url&&(t=s.url.remove),!$.isEmptyObject(o.routeData)){var a=w2utils.parseRoute(t);if(0<a.keys.length)for(var r=0;r<a.keys.length;r++)null!=o.routeData[a.keys[r].name]&&(t=t.replace(new RegExp(":"+a.keys[r].name,"g"),o.routeData[a.keys[r].name]))}var d={type:"GET",url:t,data:s.postData,headers:s.httpHeaders,dataType:"json"};switch(this.dataType||w2utils.settings.dataType){case"HTTP":d.data="object"==typeof d.data?String($.param(d.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]"):d.data;break;case"HTTPJSON":d.data={request:JSON.stringify(d.data)};break;case"RESTFULL":d.type="GET","save"==n&&(d.type="PUT"),"delete"==n&&(d.type="DELETE"),d.data="object"==typeof d.data?String($.param(d.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]"):d.data;break;case"RESTFULLJSON":d.type="GET","save"==n&&(d.type="PUT"),"delete"==n&&(d.type="DELETE"),d.data=JSON.stringify(d.data),d.contentType="application/json";break;case"JSON":d.type="POST",d.data=JSON.stringify(d.data),d.contentType="application/json"}this.method&&(d.type=this.method),this.last.xhr_cmd=n,this.last.xhr_start=(new Date).getTime(),this.last.loaded=!1,this.last.xhr=$.ajax(d).done(function(e,t,i){o.requestComplete(t,i,n,l)}).fail(function(e,t,i){var s,i={status:t,error:i,rawResponseText:e.responseText},i=o.trigger({phase:"before",type:"error",error:i,xhr:e});if(!0!==i.isCancelled){if("abort"!=t){try{s="object"==typeof e.responseJSON?e.responseJSON:$.parseJSON(e.responseText)}catch(e){}console.log("ERROR: Server communication failed.","\n   EXPECTED:",{status:"success",total:5,records:[{recid:1,field:"value"}]},"\n         OR:",{status:"error",message:"error message"},"\n   RECEIVED:","object"==typeof s?s:e.responseText),o.requestComplete("error",e,n,l)}o.trigger($.extend(i,{phase:"after"}))}}),"get"==n&&this.trigger($.extend(s,{phase:"after"}))}},requestComplete:function(e,t,i,s){var n=this;this.unlock(),setTimeout(function(){n.show.statusResponse&&n.status(w2utils.lang("Server Response")+" "+((new Date).getTime()-n.last.xhr_start)/1e3+" "+w2utils.lang("sec"))},10),this.last.pull_more=!1,this.last.pull_refresh=!0;var l="load";"save"==this.last.xhr_cmd&&(l="save"),"delete"==this.last.xhr_cmd&&(l="delete");var o,l=this.trigger({phase:"before",target:this.name,type:l,xhr:t,status:e});if(!0!==l.isCancelled){if("error"!=e)if("function"==typeof n.parser?"object"!=typeof(o=n.parser(t.responseJSON))&&console.log("ERROR: Your parser did not return proper object"):null==(o=t.responseJSON)?o={status:"error",message:w2utils.lang(this.msgNotJSON),responseText:t.responseText}:Array.isArray(o)&&(o={status:"success",records:o,total:o.length}),Array.isArray(o.records)&&o.records.forEach(function(e,t){n.recid&&(e.recid=n.parseField(e,n.recid)),null==e.recid&&(e.recid="recid-"+t)}),"error"==o.status)n.error(o.message);else{if("get"==i){if(null==o.total&&(o.total=-1),null==o.records&&(o.records=[]),o.records.length==this.limit?(e=this.records.length+o.records.length,this.last.xhr_hasMore=e!=this.total):(this.last.xhr_hasMore=!1,this.total=this.offset+this.last.xhr_offset+o.records.length),this.last.xhr_hasMore||$("#grid_"+this.name+"_rec_more, #grid_"+this.name+"_frec_more").hide(),0===this.last.xhr_offset)this.records=[],this.summary=[],w2utils.isInt(o.total)&&(this.total=parseInt(o.total));else if(-1!=o.total&&parseInt(o.total)!=parseInt(this.total))return void this.message(w2utils.lang(this.msgNeedReload),function(){delete this.last.xhr_offset,this.reload()}.bind(this));if(o.records)for(var a=0;a<o.records.length;a++)this.records.push(o.records[a]);if(o.summary){this.summary=[];for(a=0;a<o.summary.length;a++)this.summary.push(o.summary[a])}}if("delete"==i)return this.reset(),void this.reload()}else o={status:"error",message:w2utils.lang(this.msgAJAXerror),responseText:t.responseText},n.error(w2utils.lang(this.msgAJAXerror));("object"!=typeof this.url?this.url:this.url.get)||(this.localSort(),this.localSearch()),this.total=parseInt(this.total),0===this.last.xhr_offset?this.refresh():(this.scroll(),this.resize()),"function"==typeof s&&s(o),this.trigger($.extend(l,{phase:"after"})),this.last.loaded=!0}else"function"==typeof s&&s({status:"error",message:"Request aborted."})},error:function(e){var t=this.trigger({target:this.name,type:"error",message:e,xhr:this.last.xhr});!0!==t.isCancelled?(this.message(e),this.trigger($.extend(t,{phase:"after"}))):"function"==typeof callBack&&callBack({status:"error",message:"Request aborted."})},getChanges:function(e){var t=[];void 0===e&&(e=this.records);for(var i=0;i<e.length;i++){var s,n=e[i];n.w2ui&&(null!=n.w2ui.changes&&((s={})[this.recid||"recid"]=n.recid,t.push($.extend(!0,s,n.w2ui.changes))),!0!==n.w2ui.expanded&&n.w2ui.children&&n.w2ui.children.length&&$.merge(t,this.getChanges(n.w2ui.children)))}return t},mergeChanges:function(){for(var changes=this.getChanges(),c=0;c<changes.length;c++){var record=this.get(changes[c].recid),s;for(s in changes[c])if("recid"!=s){"object"==typeof changes[c][s]&&(changes[c][s]=changes[c][s].text);try{-1!=s.indexOf(".")?eval("record['"+s.replace(/\./g,"']['")+"'] = changes[c][s]"):record[s]=changes[c][s]}catch(e){console.log("ERROR: Cannot merge. ",e.message||"",e)}record.w2ui&&delete record.w2ui.changes}}this.refresh()},save:function(t){var i=this,e=this.getChanges(),s="object"!=typeof this.url?this.url:this.url.save,n=this.trigger({phase:"before",target:this.name,type:"save",changes:e});!0!==n.isCancelled?s?this.request("save",{changes:n.changes},null,function(e){"error"!==e.status&&i.mergeChanges(),i.trigger($.extend(n,{phase:"after"})),"function"==typeof t&&t(e)}):(this.mergeChanges(),this.trigger($.extend(n,{phase:"after"}))):s&&"function"==typeof t&&t({status:"error",message:"Request aborted."})},editField:function(r,d,e,t){var i,u=this;if(!0!==this.last.inEditMode){var c=u.get(r,!0),s=u.getCellEditable(c,d);if(s){var h=u.records[c],p=u.columns[d],n=!0===p.frozen?"_f":"_";if(-1==["enum","file"].indexOf(s.type)){var l=u.trigger({phase:"before",type:"editField",target:u.name,recid:r,column:d,value:e,index:c,originalEvent:t});if(!0!==l.isCancelled&&(e=l.value,this.last.inEditMode=!0,this.last._edit={value:e,index:c,column:d,recid:r},this.selectNone(),this.select({recid:r,column:d}),-1==["checkbox","check"].indexOf(s.type))){var o=$("#grid_"+u.name+n+"rec_"+w2utils.escapeId(r)),a=o.find("[col="+d+"] > div");$(this.box).find("div.w2ui-edit-box").remove(),"row"!=this.selectType&&($("#grid_"+this.name+n+"selection").attr("id","grid_"+this.name+"_editable").removeClass("w2ui-selection").addClass("w2ui-edit-box").prepend('<div style="position: absolute; top: 0px; bottom: 0px; left: 0px; right: 0px;"></div>').find(".w2ui-selection-resizer").remove(),a=$("#grid_"+this.name+"_editable >div:first-child")),null==s.inTag&&(s.inTag=""),null==s.outTag&&(s.outTag=""),null==s.style&&(s.style=""),null==s.items&&(s.items=[]);var f=h.w2ui&&h.w2ui.changes&&null!=h.w2ui.changes[p.field]?w2utils.stripTags(h.w2ui.changes[p.field]):w2utils.stripTags(h[p.field]),g="object"!=typeof(f=null==f?"":f)?f:"";null!=l.old_value&&(g=l.old_value),null!=e&&(f=e);var m=null!=p.style?p.style+";":"";switch("string"==typeof p.render&&-1!=["number","int","float","money","percent","size"].indexOf(p.render.split(":")[0])&&(m+="text-align: right;"),0<s.items.length&&!$.isPlainObject(s.items[0])&&(s.items=w2obj.field.prototype.normMenu(s.items)),s.type){case"select":for(var w="",v=0;v<s.items.length;v++)w+='<option value="'+s.items[v].id+'"'+(s.items[v].id==f?' selected="selected"':"")+">"+s.items[v].text+"</option>";a.addClass("w2ui-editable").html('<select id="grid_'+u.name+"_edit_"+r+"_"+d+'" column="'+d+'" class="w2ui-input"    style="width: 100%; pointer-events: auto; padding: 0 0 0 3px; margin: 0px; border-left: 0; border-right: 0; border-radius: 0px;            outline: none; font-family: inherit;'+m+s.style+'"     field="'+p.field+'" recid="'+r+'"     '+s.inTag+">"+w+"</select>"+s.outTag),setTimeout(function(){a.find("select").on("change",function(e){delete u.last.move}).on("blur",function(e){1!=$(this).data("keep-open")&&u.editChange.call(u,this,c,d,e)})},10);break;case"div":var y="font-family: "+(x=o.find("[col="+d+"] > div")).css("font-family")+"; font-size: "+x.css("font-size")+";";a.addClass("w2ui-editable").html('<div id="grid_'+u.name+"_edit_"+r+"_"+d+'" class="w2ui-input"    contenteditable style="'+y+m+s.style+'" autocorrect="off" autocomplete="off" spellcheck="false"     field="'+p.field+'" recid="'+r+'" column="'+d+'" '+s.inTag+"></div>"+s.outTag),null==e&&a.find("div.w2ui-input").text("object"!=typeof f?f:"");var b=a.find("div.w2ui-input").get(0);setTimeout(function(){var t=b;$(t).on("blur",function(e){1!=$(this).data("keep-open")&&u.editChange.call(u,t,c,d,e)})},10),null!=e&&$(b).text("object"!=typeof f?f:"");break;default:var x,y="font-family: "+(x=o.find("[col="+d+"] > div")).css("font-family")+"; font-size: "+x.css("font-size");a.addClass("w2ui-editable").html('<input id="grid_'+u.name+"_edit_"+r+"_"+d+'" autocorrect="off" autocomplete="off" spellcheck="false" type="text"     style="'+y+"; width: 100%; height: 100%; padding: 3px; border-color: transparent; outline: none; border-radius: 0;        pointer-events: auto; "+m+s.style+'"     field="'+p.field+'" recid="'+r+'" column="'+d+'" class="w2ui-input"'+s.inTag+"/>"+s.outTag),"number"==s.type&&(f=w2utils.formatNumber(f)),"date"==s.type&&(f=w2utils.formatDate(w2utils.isDate(f,s.format,!0)||new Date,s.format)),null==e&&a.find("input").val("object"!=typeof f?f:"");b=a.find("input").get(0);$(b).w2field(s.type,$.extend(s,{selected:f})),setTimeout(function(){var e=b;"list"==s.type&&(e=$($(b).data("w2field").helpers.focus).find("input"),"object"!=typeof f&&""!=f&&e.val(f).css({opacity:1}).prev().css({opacity:1}),a.find("input").on("change",function(e){u.editChange.call(u,b,c,d,e)})),$(e).on("blur",function(e){1!=$(this).data("keep-open")&&u.editChange.call(u,b,c,d,e)})},10),null!=e&&$(b).val("object"!=typeof f?f:"")}setTimeout(function(){u.last.inEditMode&&(a.find("input, select, div.w2ui-input").data("old_value",g).on("mousedown",function(e){e.stopPropagation()}).on("click",function(e){"div"==s.type?_.call(a.find("div.w2ui-input")[0],null):_.call(a.find("input, select")[0],null)}).on("paste",function(e){var t=e.originalEvent;e.preventDefault();t=t.clipboardData.getData("text/plain");document.execCommand("insertHTML",!1,t)}).on("keydown",function(o){var a=this,e="DIV"==a.tagName.toUpperCase()?$(a).text():$(a).val();switch(o.keyCode){case 8:"list"!=s.type||$(b).data("w2field")||o.preventDefault();break;case 9:case 13:o.preventDefault();break;case 37:0===w2utils.getCursorPosition(a)&&o.preventDefault();break;case 39:w2utils.getCursorPosition(a)==e.length&&(w2utils.setCursorPosition(a,e.length),o.preventDefault())}setTimeout(function(){switch(o.keyCode){case 9:var e=r,t=o.shiftKey?u.prevCell(c,d,!0):u.nextCell(c,d,!0);if(null==t){var i=o.shiftKey?u.prevRow(c,d):u.nextRow(c,d);if(null!=i&&i!=c)for(var e=u.records[i].recid,s=0;s<u.columns.length;s++){var n=u.getCellEditable(c,s);if(null!=n&&-1==["checkbox","check"].indexOf(n.type)&&(t=parseInt(s),!o.shiftKey))break}}!1===e&&(e=r),null==t&&(t=d),a.blur(),setTimeout(function(){"row"!=u.selectType?(u.selectNone(),u.select({recid:e,column:t})):u.editField(e,t,null,o)},1),o.preventDefault&&o.preventDefault();break;case 13:a.blur();var l=o.shiftKey?u.prevRow(c,d):u.nextRow(c,d);null!=l&&l!=c&&setTimeout(function(){"row"!=u.selectType?(u.selectNone(),u.select({recid:u.records[l].recid,column:d})):u.editField(u.records[l].recid,d,null,o)},1),"DIV"==a.tagName.toUpperCase()&&o.preventDefault();break;case 27:i=u.parseField(h,p.field);h.w2ui&&h.w2ui.changes&&null!=h.w2ui.changes[p.field]&&(i=h.w2ui.changes[p.field]),null!=$(a).data("old_value")&&(i=$(a).data("old_value")),"DIV"==a.tagName.toUpperCase()?$(a).text(null!=i?i:""):a.value=null!=i?i:"",a.blur(),setTimeout(function(){u.select({recid:r,column:d})},1)}_.call(a,o)},1)}).on("keyup",function(e){_.call(this,e)}),setTimeout(function(){var e,t;u.last.inEditMode&&(e=a.find(".w2ui-input"),t=null!=$(e).val()?$(e).val().length:0,"div"==s.type&&(t=$(e).text().length),0<e.length&&(e.focus(),clearTimeout(u.last.kbd_timer),"SELECT"!=e[0].tagName.toUpperCase()&&w2utils.setCursorPosition(e[0],t),(e[0].resize=_).call(e[0],null)))},50),u.trigger($.extend(l,{phase:"after",input:a.find("input, select, div.w2ui-input")})))},5)}}else console.log('ERROR: input types "enum" and "file" are not supported in inline editing.')}}else 13==t.keyCode?(c=this.last._edit.index,d=this.last._edit.column,r=this.last._edit.recid,this.editChange({type:"custom",value:this.last._edit.value},this.get(r,!0),d,t),null!=(i=t.shiftKey?this.prevRow(c,d):this.nextRow(c,d))&&i!=c&&setTimeout(function(){"row"!=u.selectType?(u.selectNone(),u.select({recid:u.records[i].recid,column:d})):u.editField(u.records[i].recid,d,null,t)},1),this.last.inEditMode=!1):0<(n=$(this.box).find("div.w2ui-edit-box .w2ui-input")).length&&"DIV"==n[0].tagName&&(n.text(n.text()+e),w2utils.setCursorPosition(n[0],n.text().length));function _(e){try{var t="DIV"==this.tagName.toUpperCase()?$(this).text():this.value,i=$("#grid_"+u.name+"_editable"),s="font-family: "+$(this).css("font-family")+"; font-size: "+$(this).css("font-size")+"; white-space: pre;",s=w2utils.getStrWidth(t,s);s+20>i.width()&&i.width(s+20)}catch(e){}}},editChange:function(e,t,i,s){var n=this;setTimeout(function(){var e=$(n.box).find("#grid_"+n.name+"_focus");e.is(":focus")||e.focus()},10);var l=t<0;t=t<0?-t-1:t;var o=(l?this.summary:this.records)[t],a=this.columns[i],r=$("#grid_"+this.name+(!0===a.frozen?"_frec_":"_rec_")+w2utils.escapeId(o.recid)),d=e.tagName&&"DIV"==e.tagName.toUpperCase()?$(e).text():e.value,u=this.parseField(o,a.field),c=$(e).data("w2field");c&&("list"==c.type&&(d=$(e).data("selected")),!$.isEmptyObject(d)&&null!=d||(d=""),$.isPlainObject(d)||(d=c.clean(d))),"checkbox"==e.type&&(o.w2ui&&!1===o.w2ui.editable&&(e.checked=!e.checked),d=e.checked);var h={phase:"before",type:"change",target:this.name,input_id:e.id,recid:o.recid,index:t,column:i,originalEvent:s.originalEvent||s,value_new:d,value_previous:o.w2ui&&o.w2ui.changes&&o.w2ui.changes.hasOwnProperty(a.field)?o.w2ui.changes[a.field]:u,value_original:u};for(null!=$(s.target).data("old_value")&&(h.value_previous=$(s.target).data("old_value"));;){if("object"!=typeof(d=h.value_new)&&String(u)!=String(d)||"object"==typeof d&&d&&d.id!=u&&("object"!=typeof u||null==u||d.id!=u.id)){if(!0!==(h=this.trigger($.extend(h,{type:"change",phase:"before"}))).isCancelled){if(d!==h.value_new)continue;o.w2ui=o.w2ui||{},o.w2ui.changes=o.w2ui.changes||{},o.w2ui.changes[a.field]=h.value_new,this.trigger($.extend(h,{phase:"after"}))}}else if(!0!==(h=this.trigger($.extend(h,{type:"restore",phase:"before"}))).isCancelled){if(d!==h.value_new)continue;o.w2ui&&o.w2ui.changes&&delete o.w2ui.changes[a.field],o.w2ui&&$.isEmptyObject(o.w2ui.changes)&&delete o.w2ui.changes,this.trigger($.extend(h,{phase:"after"}))}break}r=$(r).find("[col="+i+"]");l||(o.w2ui&&o.w2ui.changes&&null!=o.w2ui.changes[a.field]?r.addClass("w2ui-changed"):r.removeClass("w2ui-changed"),r.replaceWith(this.getCellHTML(t,i,l))),$(this.box).find("div.w2ui-edit-box").remove(),this.show.toolbarSave&&(0<this.getChanges().length?this.toolbar.enable("w2ui-save"):this.toolbar.disable("w2ui-save")),n.last.inEditMode=!1},delete:function(e){(new Date).getTime();var i=this,t=this.trigger({phase:"before",target:this.name,type:"delete",force:e});if(e&&this.message(),!0!==t.isCancelled){e=t.force,setTimeout(function(){$().w2tag()},20);var s=this.getSelection();if(0!==s.length)if(""==this.msgDelete||e){if("object"!=typeof this.url?this.url:this.url.remove)this.request("delete");else if("object"!=typeof s[0])this.selectNone(),this.remove.apply(this,s);else{for(var n=0;n<s.length;n++){var l=this.columns[s[n].column].field,o=this.get(s[n].recid,!0),a=this.records[o];null!=o&&"recid"!=l&&(this.records[o][l]="",a.w2ui&&a.w2ui.changes&&delete a.w2ui.changes[l])}this.update()}this.trigger($.extend(t,{phase:"after"}))}else this.message({width:380,height:170,body:'<div class="w2ui-centered">'+w2utils.lang(i.msgDelete).replace("NN",s.length).replace("records",1==s.length?"record":"records")+"</div>",buttons:w2utils.settings.macButtonOrder?'<button type="button" class="w2ui-btn btn-default" onclick="w2ui[\''+this.name+"'].message()\">"+w2utils.lang("Cancel")+'</button><button type="button" class="w2ui-btn" onclick="w2ui[\''+this.name+"'].delete(true)\">"+w2utils.lang(this.msgDeleteBtn)+"</button>":'<button type="button" class="w2ui-btn" onclick="w2ui[\''+this.name+"'].delete(true)\">"+w2utils.lang(this.msgDeleteBtn)+'</button><button type="button" class="w2ui-btn btn-default" onclick="w2ui[\''+this.name+"'].message()\">"+w2utils.lang("Cancel")+"</button>",onOpen:function(e){var t=$(this.box).find("input, textarea, select, button");t.off(".message").on("blur.message",function(e){t.index(e.target)+1===t.length&&(t.get(0).focus(),e.preventDefault())}).on("keydown.message",function(e){27==e.keyCode&&i.message()}),setTimeout(function(){$(this.box).find(".w2ui-btn.btn-default").focus(),clearTimeout(i.last.kbd_timer)}.bind(this),50)}})}},click:function(e,t){var i=(new Date).getTime(),s=null;if(!(1==this.last.cancelClick||t&&t.altKey))if("object"==typeof e&&null!==e&&(s=e.column,e=e.recid),null==t&&(t={}),i-parseInt(this.last.click_time)<350&&this.last.click_recid==e&&"click"==t.type)this.dblClick(e,t);else{this.last.bubbleEl&&($(this.last.bubbleEl).w2tag(),this.last.bubbleEl=null),this.last.click_time=i;var n=this.last.click_recid;this.last.click_recid=e,null==s&&t.target&&("TD"!=(v=t.target).tagName.toUpperCase()&&(v=$(v).parents("td")[0]),null!=$(v).attr("col")&&(s=parseInt($(v).attr("col"))));var l=this.trigger({phase:"before",target:this.name,type:"click",recid:e,column:s,originalEvent:t});if(!0!==l.isCancelled){var o=this,a=this.getSelection();$("#grid_"+this.name+"_check_all").prop("checked",!1);var i=this.get(e,!0),r=(this.records[i],[]);if(o.last.sel_ind=i,o.last.sel_col=s,o.last.sel_recid=e,o.last.sel_type="click",t.shiftKey&&0<a.length&&o.multiSelect){if(a[0].recid){var d,u,c=this.get(a[0].recid,!0),h=this.get(e,!0);u=s>a[0].column?(d=a[0].column,s):(d=s,a[0].column);for(var p=d;p<=u;p++)r.push(p)}else c=this.get(n,!0),h=this.get(e,!0);var f=[];h<c&&(v=c,c=h,h=v);for(var g="object"!=typeof this.url?this.url:this.url.get,m=c;m<=h;m++)if(!(0<this.searchData.length)||g||-1!=$.inArray(m,this.last.searchIds))if("row"==this.selectType)f.push(this.records[m].recid);else for(var w=0;w<r.length;w++)f.push({recid:this.records[m].recid,column:r[w]});this.select(f)}else{var n=this.last.selection,v=-1!=n.indexes.indexOf(i),c=!1;$(t.target).parents("td").hasClass("w2ui-col-select")&&(c=!0),(t.ctrlKey||t.shiftKey||t.metaKey||c)&&this.multiSelect||this.showSelectColumn?(t=$(t.target).parents("tr").find(".w2ui-grid-select-check").is(":checked"),!0===(v="row"!=this.selectType&&-1==$.inArray(s,n.columns[i])&&!t?!1:v)?this.unselect({recid:e,column:s}):this.select({recid:e,column:s})):("row"!=this.selectType&&-1==$.inArray(s,n.columns[i])&&(v=!1),300<a.length?this.selectNone():this.unselect(a),!0===v&&1==a.length?this.unselect({recid:e,column:s}):this.select({recid:e,column:s}))}this.status(),o.initResize(),this.trigger($.extend(l,{phase:"after"}))}}},columnClick:function(e,t){if(!0!==(i=this.trigger({phase:"before",type:"columnClick",target:this.name,field:e,originalEvent:t})).isCancelled){if("row"==this.selectType)(s=this.getColumn(e))&&s.sortable&&this.sort(e,null,!(!t||!t.ctrlKey&&!t.metaKey)),"line-number"==i.field&&(this.getSelection().length>=this.records.length?this.selectNone():this.selectAll());else if(t.altKey&&(s=this.getColumn(e))&&s.sortable&&this.sort(e,null,!(!t||!t.ctrlKey&&!t.metaKey)),"line-number"==i.field)this.getSelection().length>=this.records.length?this.selectNone():this.selectAll();else{t.shiftKey||t.metaKey||t.ctrlKey||this.selectNone();var i,e=this.getSelection(),s=this.getColumn(i.field,!0),n=[],l=[];if(0!=e.length&&t.shiftKey){var t=s,o=e[0].column;o<t&&(t=e[0].column,o=s);for(var a=t;a<=o;a++)l.push(a)}else l.push(s);if(!0!==(i=this.trigger({phase:"before",type:"columnSelect",target:this.name,columns:l})).isCancelled){for(a=0;a<this.records.length;a++)n.push({recid:this.records[a].recid,column:l});this.select(n)}this.trigger($.extend(i,{phase:"after"}))}this.trigger($.extend(i,{phase:"after"}))}},columnDblClick:function(e,t){t=this.trigger({phase:"before",type:"columnDblClick",target:this.name,field:e,originalEvent:t});!0!==t.isCancelled&&this.trigger($.extend(t,{phase:"after"}))},focus:function(e){var t=this,e=this.trigger({phase:"before",type:"focus",target:this.name,originalEvent:e});if(!0===e.isCancelled)return!1;this.hasFocus=!0,$(this.box).removeClass("w2ui-inactive").find(".w2ui-inactive").removeClass("w2ui-inactive"),setTimeout(function(){var e=$(t.box).find("#grid_"+t.name+"_focus");e.is(":focus")||e.focus()},10),this.trigger($.extend(e,{phase:"after"}))},blur:function(e){e=this.trigger({phase:"before",type:"blur",target:this.name,originalEvent:e});if(!0===e.isCancelled)return!1;this.hasFocus=!1,$(this.box).addClass("w2ui-inactive").find(".w2ui-selected").addClass("w2ui-inactive"),$(this.box).find(".w2ui-selection").addClass("w2ui-inactive"),this.trigger($.extend(e,{phase:"after"}))},keydown:function(i){var s=this,e="object"!=typeof this.url?this.url:this.url.get;if(!0===s.keyboard){var t=s.trigger({phase:"before",type:"keydown",target:s.name,originalEvent:i});if(!0!==t.isCancelled)if(0<$(this.box).find(">.w2ui-message").length)27==i.keyCode&&this.message();else{var n=!1,l=$("#grid_"+s.name+"_records"),o=s.getSelection();0===o.length&&(n=!0);var a=o[0]||null,r=[],d=o[o.length-1];if("object"==typeof a&&null!=a){for(var a=o[0].recid,r=[],u=0;o[u]&&o[u].recid==a;)r.push(o[u].column),u++;d=o[o.length-1].recid}var c=s.get(a,!0),h=s.get(d,!0),p=(s.get(a),$("#grid_"+s.name+"_rec_"+(null!=c?w2utils.escapeId(s.records[c].recid):"none"))),f=!1,g=i.keyCode,m=i.shiftKey;switch(g){case 8:case 46:s.delete(),f=!0,i.stopPropagation();break;case 27:s.selectNone(),f=!0;break;case 65:if(!i.metaKey&&!i.ctrlKey)break;s.selectAll(),f=!0;break;case 13:if("row"==this.selectType&&!0===s.show.expandColumn){if(p.length<=0)break;s.toggle(a,i),f=!0}else{for(var w=0;w<this.columns.length;w++)if(this.getCellEditable(c,w)){r.push(parseInt(w));break}0<(r="row"==this.selectType&&this.last._edit&&this.last._edit.column?[this.last._edit.column]:r).length&&(s.editField(a,r[0],null,i),f=!0)}break;case 37:if(n){T();break}if("row"==this.selectType){if(p.length<=0)break;!(y=this.records[c].w2ui||{})||null==y.parent_recid||Array.isArray(y.children)&&0!==y.children.length&&y.expanded?s.collapse(a,i):(s.unselect(a),s.collapse(y.parent_recid,i),s.select(y.parent_recid))}else{var v=s.prevCell(c,r[0]);if(m||null!=v||(this.selectNone(),v=0),null!=v)if(m&&s.multiSelect){if(S())return;var y=[],b=[],x=[];if(0===r.indexOf(this.last.sel_col)&&1<r.length){for(var _=0;_<o.length;_++)-1==y.indexOf(o[_].recid)&&y.push(o[_].recid),x.push({recid:o[_].recid,column:r[r.length-1]});s.unselect(x),s.scrollIntoView(c,r[r.length-1],!0)}else{for(_=0;_<o.length;_++)-1==y.indexOf(o[_].recid)&&y.push(o[_].recid),b.push({recid:o[_].recid,column:v});s.select(b),s.scrollIntoView(c,v,!0)}}else i.metaKey=!1,s.click({recid:a,column:v},i),s.scrollIntoView(c,v,!0);else if(!m)if(1<o.length)s.selectNone();else for(var k=1;k<o.length;k++)s.unselect(o[k])}f=!0;break;case 39:if(n){T();break}if("row"==this.selectType){if(p.length<=0)break;s.expand(a,i)}else{var C=s.nextCell(c,r[r.length-1]);if(m||null!=C||(this.selectNone(),C=this.columns.length-1),null!=C)if(m&&39==g&&s.multiSelect){if(S())return;y=[],b=[],x=[];if(r.indexOf(this.last.sel_col)==r.length-1&&1<r.length){for(_=0;_<o.length;_++)-1==y.indexOf(o[_].recid)&&y.push(o[_].recid),x.push({recid:o[_].recid,column:r[0]});s.unselect(x),s.scrollIntoView(c,r[0],!0)}else{for(_=0;_<o.length;_++)-1==y.indexOf(o[_].recid)&&y.push(o[_].recid),b.push({recid:o[_].recid,column:C});s.select(b),s.scrollIntoView(c,C,!0)}}else i.metaKey=!1,s.click({recid:a,column:C},i),s.scrollIntoView(c,C,!0);else if(!m)if(1<o.length)s.selectNone();else for(k=0;k<o.length-1;k++)s.unselect(o[k])}f=!0;break;case 38:if(n&&T(),p.length<=0)break;v=s.prevRow(c,r[0]);if(null!=(v=!m&&null==v?0==this.searchData.length||e?0:this.last.searchIds[0]:v)){if(m&&s.multiSelect){if(S())return;if("row"==s.selectType)s.last.sel_ind>v&&s.last.sel_ind!=h?s.unselect(s.records[h].recid):s.select(s.records[v].recid);else if(s.last.sel_ind>v&&s.last.sel_ind!=h){v=h;for(y=[],w=0;w<r.length;w++)y.push({recid:s.records[v].recid,column:r[w]});s.unselect(y)}else{for(y=[],w=0;w<r.length;w++)y.push({recid:s.records[v].recid,column:r[w]});s.select(y)}}else 300<o.length?this.selectNone():this.unselect(o),s.click({recid:s.records[v].recid,column:r[0]},i);s.scrollIntoView(v),i.preventDefault&&i.preventDefault()}else if(!m)if(1<o.length)s.selectNone();else for(k=1;k<o.length;k++)s.unselect(o[k]);break;case 40:if(n&&T(),p.length<=0)break;C=s.nextRow(h,r[0]);if(null!=(C=!m&&null==C?0==this.searchData.length||e?this.records.length-1:this.last.searchIds[this.last.searchIds.length-1]:C)){if(m&&s.multiSelect){if(S())return;if("row"==s.selectType)this.last.sel_ind<C&&this.last.sel_ind!=c?s.unselect(s.records[c].recid):s.select(s.records[C].recid);else if(this.last.sel_ind<C&&this.last.sel_ind!=c){C=c;for(y=[],w=0;w<r.length;w++)y.push({recid:s.records[C].recid,column:r[w]});s.unselect(y)}else{for(y=[],w=0;w<r.length;w++)y.push({recid:s.records[C].recid,column:r[w]});s.select(y)}}else 300<o.length?this.selectNone():this.unselect(o),s.click({recid:s.records[C].recid,column:r[0]},i);s.scrollIntoView(C),f=!0}else if(!m)if(1<o.length)s.selectNone();else for(k=0;k<o.length-1;k++)s.unselect(o[k]);break;case 17:case 91:if(n)break;s.last.isSafari&&(s.last.copy_event=s.copy(!1,i),$("#grid_"+s.name+"_focus").val(s.last.copy_event.text).select());break;case 67:(i.metaKey||i.ctrlKey)&&(s.last.isSafari||(s.last.copy_event=s.copy(!1,i),$("#grid_"+s.name+"_focus").val(s.last.copy_event.text).select()),s.copy(s.last.copy_event,i));break;case 88:if(n)break;(i.ctrlKey||i.metaKey)&&(s.last.isSafari||(s.last.copy_event=s.copy(!1,i),$("#grid_"+s.name+"_focus").val(s.last.copy_event.text).select()),s.copy(s.last.copy_event,i))}for(y=[32,187,189,192,219,220,221,186,222,188,190,191],_=48;_<=111;_++)y.push(_);-1==y.indexOf(g)||i.ctrlKey||i.metaKey||f||(0===r.length&&r.push(0),f=!1,setTimeout(function(){var e=$("#grid_"+s.name+"_focus"),t=e.val();e.val(""),s.editField(a,r[0],t,i)},1)),f&&i.preventDefault&&i.preventDefault(),s.trigger($.extend(t,{phase:"after"}))}}function T(){var e=Math.floor(l[0].scrollTop/s.recordHeight)+1;(!s.records[e]||e<2)&&(e=0),s.select({recid:s.records[e].recid,column:0})}function S(){if("click"==s.last.sel_type){if("row"==s.selectType)return s.last.sel_type="key",1<o.length?(o.splice(o.indexOf(s.records[s.last.sel_ind].recid),1),s.unselect(o),1):void 0;if(s.last.sel_type="key",1<o.length){for(var e=0;e<o.length;e++)if(o[e].recid==s.last.sel_recid&&o[e].column==s.last.sel_col){o.splice(e,1);break}return s.unselect(o),1}}}},scrollIntoView:function(e,t,i){var s=this.records.length;if(0!==(s=0!=this.searchData.length&&!this.url?this.last.searchIds.length:s)){if(null==e){var n=this.getSelection();if(0===n.length)return;$.isPlainObject(n[0])?(e=n[0].index,t=n[0].column):e=this.get(n[0],!0)}var l=$("#grid_"+this.name+"_records"),n=this.last.searchIds.length;if(0<n&&(e=this.last.searchIds.indexOf(e)),l.height()<this.recordHeight*(0<n?n:s)&&0<l.length&&(s=(n=Math.floor(l[0].scrollTop/this.recordHeight))+Math.floor(l.height()/this.recordHeight),e==n&&(!0===i?l.prop({scrollTop:l.scrollTop()-l.height()/1.3}):(l.stop(),l.animate({scrollTop:l.scrollTop()-l.height()/1.3},250,"linear"))),e==s&&(!0===i?l.prop({scrollTop:l.scrollTop()+l.height()/1.3}):(l.stop(),l.animate({scrollTop:l.scrollTop()+l.height()/1.3},250,"linear"))),(e<n||s<e)&&(!0===i?l.prop({scrollTop:(e-1)*this.recordHeight}):(l.stop(),l.animate({scrollTop:(e-1)*this.recordHeight},250,"linear")))),null!=t){for(var o=0,a=0,e=w2utils.scrollBarSize(),r=0;r<=t;r++){var d=this.columns[r];d.frozen||d.hidden||(o=a,a+=parseInt(d.sizeCalculated))}l.width()<a-l.scrollLeft()?!0===i?l.prop({scrollLeft:o-e}):l.animate({scrollLeft:o-e},250,"linear"):o<l.scrollLeft()&&(!0===i?l.prop({scrollLeft:a-l.width()+2*e}):l.animate({scrollLeft:a-l.width()+2*e},250,"linear"))}}},dblClick:function(e,t){var i=null;"object"==typeof e&&null!==e&&(i=e.column,e=e.recid),null==t&&(t={}),null==i&&t.target&&("TD"!=(l=t.target).tagName.toUpperCase()&&(l=$(l).parents("td")[0]),i=parseInt($(l).attr("col")));var s=this.get(e,!0),n=this.records[s],l=this.trigger({phase:"before",target:this.name,type:"dblClick",recid:e,column:i,originalEvent:t});!0!==l.isCancelled&&(this.selectNone(),this.getCellEditable(s,i)?this.editField(e,i,null,t):(this.select({recid:e,column:i}),(this.show.expandColumn||n.w2ui&&Array.isArray(n.w2ui.children))&&this.toggle(e)),this.trigger($.extend(l,{phase:"after"})))},contextMenu:function(t,e,i){var s=this;if("text"!=s.last.userSelect){null==(i=null==i?{offsetX:0,offsetY:0,target:$("#grid_"+s.name+"_rec_"+t)[0]}:i).offsetX&&(i.offsetX=i.layerX-i.target.offsetLeft,i.offsetY=i.layerY-i.target.offsetTop),w2utils.isFloat(t)&&(t=parseFloat(t));var n=this.getSelection();if("row"==this.selectType)-1==n.indexOf(t)&&s.click(t);else{var l=$(i.target),o=!1;e=(l="TD"!=l[0].tagName.toUpperCase()?$(i.target).parents("td"):l).attr("col");for(var a=0;a<n.length;a++)n[a].recid!=t&&n[a].column!=e||(o=!0);o||null==t||s.click({recid:t,column:e}),o||null==e||s.columnClick(this.columns[e].field,i)}l=s.trigger({phase:"before",type:"contextMenu",target:s.name,originalEvent:i,recid:t,column:e});!0!==l.isCancelled&&(0<s.menu.length&&$(s.box).find(i.target).w2menu(s.menu,{originalEvent:i,contextMenu:!0,onSelect:function(e){s.menuClick(t,e)}}),i.preventDefault&&i.preventDefault(),s.trigger($.extend(l,{phase:"after"})))}},menuClick:function(e,t){t=this.trigger({phase:"before",type:"menuClick",target:this.name,originalEvent:t.originalEvent,menuEvent:t,recid:e,menuIndex:t.index,menuItem:t.item});!0!==t.isCancelled&&this.trigger($.extend(t,{phase:"after"}))},toggle:function(e){var t=this.get(e);return t.w2ui=t.w2ui||{},!0===t.w2ui.expanded?this.collapse(e):this.expand(e)},expand:function(e){var t=this.get(e,!0),i=this.records[t];i.w2ui=i.w2ui||{};var s,n=w2utils.escapeId(e),l=i.w2ui.children;if(Array.isArray(l)){if(!0===i.w2ui.expanded||0===l.length)return!1;if(!0===(s=this.trigger({phase:"before",type:"expand",target:this.name,recid:e})).isCancelled)return!1;i.w2ui.expanded=!0,l.forEach(function(e){e.w2ui=e.w2ui||{},e.w2ui.parent_recid=i.recid,null==e.w2ui.children&&(e.w2ui.children=[])}),this.records.splice.apply(this.records,[t+1,0].concat(l)),this.total+=l.length,("object"!=typeof this.url?this.url:this.url.get)||(this.localSort(!0,!0),0<this.searchData.length&&this.localSearch(!0)),this.refresh(),this.trigger($.extend(s,{phase:"after"}))}else{if(0<$("#grid_"+this.name+"_rec_"+n+"_expanded_row").length||!0!==this.show.expandColumn)return!1;if("none"==i.w2ui.expanded)return!1;if($("#grid_"+this.name+"_rec_"+n).after('<tr id="grid_'+this.name+"_rec_"+e+'_expanded_row" class="w2ui-expanded-row">    <td colspan="100" class="w2ui-expanded2">        <div id="grid_'+this.name+"_rec_"+e+'_expanded"></div>    </td>    <td class="w2ui-grid-data-last"></td></tr>'),$("#grid_"+this.name+"_frec_"+n).after('<tr id="grid_'+this.name+"_frec_"+e+'_expanded_row" class="w2ui-expanded-row">'+(this.show.lineNumbers?'<td class="w2ui-col-number"></td>':"")+'    <td class="w2ui-grid-data w2ui-expanded1" colspan="100">       <div id="grid_'+this.name+"_frec_"+e+'_expanded"></div>   </td></tr>'),!0===(s=this.trigger({phase:"before",type:"expand",target:this.name,recid:e,box_id:"grid_"+this.name+"_rec_"+e+"_expanded",fbox_id:"grid_"+this.name+"_frec_"+n+"_expanded"})).isCancelled)return $("#grid_"+this.name+"_rec_"+n+"_expanded_row").remove(),$("#grid_"+this.name+"_frec_"+n+"_expanded_row").remove(),!1;var o=$(this.box).find("#grid_"+this.name+"_rec_"+e+"_expanded"),t=$(this.box).find("#grid_"+this.name+"_frec_"+e+"_expanded"),l=o.find("> div:first-child").height();o.height()<l&&o.css({height:l+"px"}),t.height()<l&&t.css({height:l+"px"}),$("#grid_"+this.name+"_rec_"+n).attr("expanded","yes").addClass("w2ui-expanded"),$("#grid_"+this.name+"_frec_"+n).attr("expanded","yes").addClass("w2ui-expanded"),$("#grid_"+this.name+"_cell_"+this.get(e,!0)+"_expand div").html("-"),i.w2ui.expanded=!0,this.trigger($.extend(s,{phase:"after"})),this.resizeRecords()}return!0},collapse:function(e){var t=this,i=this.get(e,!0),s=this.records[i];s.w2ui=s.w2ui||{};var n,l=w2utils.escapeId(e),o=s.w2ui.children;if(Array.isArray(o)){if(!0!==s.w2ui.expanded)return!1;if(!0===(n=this.trigger({phase:"before",type:"collapse",target:this.name,recid:e})).isCancelled)return!1;!function e(t){t.w2ui.expanded=!1;for(var i=0;i<t.w2ui.children.length;i++){var s=t.w2ui.children[i];s.w2ui.expanded&&e(s)}}(s);for(var a=[],r=s;null!=r;r=this.get(r.w2ui.parent_recid))a.push(r.w2ui.parent_recid);for(var i=i+1,d=i;!(this.records.length<=d+1||null==this.records[d+1].w2ui||0<=a.indexOf(this.records[d+1].w2ui.parent_recid));)d++;this.records.splice(i,d-i+1),this.total-=d-i+1,("object"!=typeof this.url?this.url:this.url.get)||0<this.searchData.length&&this.localSearch(!0),this.refresh(),t.trigger($.extend(n,{phase:"after"}))}else{if(0===$("#grid_"+this.name+"_rec_"+l+"_expanded_row").length||!0!==this.show.expandColumn)return!1;if(!0===(n=this.trigger({phase:"before",type:"collapse",target:this.name,recid:e,box_id:"grid_"+this.name+"_rec_"+l+"_expanded",fbox_id:"grid_"+this.name+"_frec_"+l+"_expanded"})).isCancelled)return!1;$("#grid_"+this.name+"_rec_"+l).removeAttr("expanded").removeClass("w2ui-expanded"),$("#grid_"+this.name+"_frec_"+l).removeAttr("expanded").removeClass("w2ui-expanded"),$("#grid_"+this.name+"_cell_"+this.get(e,!0)+"_expand div").html("+"),$("#grid_"+t.name+"_rec_"+l+"_expanded").css("height","0px"),$("#grid_"+t.name+"_frec_"+l+"_expanded").css("height","0px"),setTimeout(function(){$("#grid_"+t.name+"_rec_"+l+"_expanded_row").remove(),$("#grid_"+t.name+"_frec_"+l+"_expanded_row").remove(),s.w2ui.expanded=!1,t.trigger($.extend(n,{phase:"after"})),t.resizeRecords()},300)}return!0},sort:function(e,t,i){var s=this.trigger({phase:"before",type:"sort",target:this.name,field:e,direction:t,multiField:i});if(!0!==s.isCancelled){if(null!=e){for(var n=this.sortData.length,l=0;l<this.sortData.length;l++)if(this.sortData[l].field==e){n=l;break}if(null==t)if(null==this.sortData[n])t="asc";else switch(null==this.sortData[n].direction&&(this.sortData[n].direction=""),this.sortData[n].direction.toLowerCase()){case"asc":t="desc";break;case"desc":default:t="asc"}!1===this.multiSort&&(this.sortData=[],n=0),1!=i&&(this.sortData=[],n=0),null==this.sortData[n]&&(this.sortData[n]={}),this.sortData[n].field=e,this.sortData[n].direction=t}else this.sortData=[];("object"!=typeof this.url?this.url:this.url.get)?(this.trigger($.extend(s,{phase:"after",direction:t})),this.last.xhr_offset=0,this.reload()):(this.localSort(!0,!0),0<this.searchData.length&&this.localSearch(!0),this.last.scrollTop=0,$("#grid_"+this.name+"_records").prop("scrollTop",0),this.trigger($.extend(s,{phase:"after",direction:t})),this.refresh())}},copy:function(e,t){if($.isPlainObject(e))return this.trigger($.extend(e,{phase:"after"})),e.text;var i=this.getSelection();if(0===i.length)return"";var s,n="";if("object"==typeof i[0]){for(var l=i[0].column,o=i[0].column,a=[],r=0;r<i.length;r++)i[r].column<l&&(l=i[r].column),i[r].column>o&&(o=i[r].column),-1==a.indexOf(i[r].index)&&a.push(i[r].index);a.sort(function(e,t){return e-t});for(var d=0;d<a.length;d++){for(var u=a[d],c=l;c<=o;c++)!0!==(p=this.columns[c]).hidden&&(n+=this.getCellCopy(u,c)+"\t");n=n.substr(0,n.length-1),n+="\n"}}else{for(var h,c=0;c<this.columns.length;c++)!0!==(p=this.columns[c]).hidden&&(h=p.text||p.field,p.text&&p.text.length<3&&p.tooltip&&(h=p.tooltip),n+='"'+w2utils.stripTags(h)+'"\t');n=n.substr(0,n.length-1),n+="\n";for(r=0;r<i.length;r++){for(var p,u=this.get(i[r],!0),c=0;c<this.columns.length;c++)!0!==(p=this.columns[c]).hidden&&(n+='"'+this.getCellCopy(u,c)+'"\t');n=n.substr(0,n.length-1),n+="\n"}}return n=n.substr(0,n.length-1),null==e?!0===(s=this.trigger({phase:"before",type:"copy",target:this.name,text:n,cut:88==t.keyCode,originalEvent:t})).isCancelled?"":(n=s.text,this.trigger($.extend(s,{phase:"after"})),n):!1===e?!0===(s=this.trigger({phase:"before",type:"copy",target:this.name,text:n,cut:88==t.keyCode,originalEvent:t})).isCancelled?"":(n=s.text,s):void 0},getCellCopy:function(e,t){return w2utils.stripTags(this.getCellHTML(e,t))},paste:function(e){var t=this.getSelection(),i=this.get(t[0].recid,!0),s=t[0].column,n=this.trigger({phase:"before",type:"paste",target:this.name,text:e,index:i,column:s});if(!0!==n.isCancelled){if(e=n.text,"row"==this.selectType||0===t.length)return console.log("ERROR: You can paste only if grid.selectType = 'cell' and when at least one cell selected."),void this.trigger($.extend(n,{phase:"after"}));for(var l=[],e=e.split("\n"),o=0;o<e.length;o++){var a=e[o].split("\t"),r=0,d=this.records[i],u=[];if(null!=d){for(var c=0;c<a.length;c++)this.columns[s+r]&&(this.setCellPaste(d,s+r,a[c]),u.push(s+r),r++);for(var h=0;h<u.length;h++)l.push({recid:d.recid,column:u[h]});i++}}this.selectNone(),this.select(l),this.refresh(),this.trigger($.extend(n,{phase:"after"}))}},setCellPaste:function(e,t,i){t=this.columns[t].field;e.w2ui=e.w2ui||{},e.w2ui.changes=e.w2ui.changes||{},e.w2ui.changes[t]=i},resize:function(){var e=this,t=(new Date).getTime();if(this.box&&$(this.box).attr("name")==this.name){$(this.box).find("> div.w2ui-grid-box").css("width",$(this.box).width()).css("height",$(this.box).height());var i=this.trigger({phase:"before",type:"resize",target:this.name});if(!0!==i.isCancelled)return e.resizeBoxes(),e.resizeRecords(),e.toolbar&&e.toolbar.resize&&e.toolbar.resize(),this.trigger($.extend(i,{phase:"after"})),(new Date).getTime()-t}},update:function(e){var t=(new Date).getTime();if(null==this.box)return 0;if(null==e){for(var i=this.last.range_start-1;i<=this.last.range_end-1;i++)if(!(i<0)){(o=this.records[i]||{}).w2ui||(o.w2ui={});for(var s=0;s<this.columns.length;s++){var n=$(this.box).find("#grid_"+this.name+"_rec_"+w2utils.escapeId(o.recid));(a=$(this.box).find("#grid_"+this.name+"_data_"+i+"_"+s)).replaceWith(this.getCellHTML(i,s,!1)),a=$(this.box).find("#grid_"+this.name+"_data_"+i+"_"+s),null==o.w2ui.style||$.isEmptyObject(o.w2ui.style)?a.attr("style",""):("string"==typeof o.w2ui.style&&n.attr("style",o.w2ui.style),$.isPlainObject(o.w2ui.style)&&"string"==typeof o.w2ui.style[s]&&a.attr("style",o.w2ui.style[s])),null==o.w2ui.class||$.isEmptyObject(o.w2ui.class)||("string"==typeof o.w2ui.class&&n.addClass(o.w2ui.class),$.isPlainObject(o.w2ui.class)&&"string"==typeof o.w2ui.class[s]&&a.addClass(o.w2ui.class[s]))}}}else for(var l=0;l<e.length;l++){var o,a,i=e[l].index,s=e[l].column;i<0||(null!=i&&null!=s?(o=this.records[i]||{},n=$(this.box).find("#grid_"+this.name+"_rec_"+w2utils.escapeId(o.recid)),a=$(this.box).find("#grid_"+this.name+"_data_"+i+"_"+s),o.w2ui||(o.w2ui={}),a.replaceWith(this.getCellHTML(i,s,!1)),a=$(this.box).find("#grid_"+this.name+"_data_"+i+"_"+s),null==o.w2ui.style||$.isEmptyObject(o.w2ui.style)?a.attr("style",""):("string"==typeof o.w2ui.style&&n.attr("style",o.w2ui.style),$.isPlainObject(o.w2ui.style)&&"string"==typeof o.w2ui.style[s]&&a.attr("style",o.w2ui.style[s])),null==o.w2ui.class||$.isEmptyObject(o.w2ui.class)||("string"==typeof o.w2ui.class&&n.addClass(o.w2ui.class),$.isPlainObject(o.w2ui.class)&&"string"==typeof o.w2ui.class[s]&&a.addClass(o.w2ui.class[s]))):console.log("ERROR: Wrong argument for grid.update(cells), cells should be [{ index: X, column: Y }, ...]"))}return(new Date).getTime()-t},refreshCell:function(e,t){var i=this.get(e,!0),s=this.getColumn(t,!0),t=!this.records[i]||this.records[i].recid!=e,e=$(this.box).find((t?".w2ui-grid-summary ":"")+"#grid_"+this.name+"_data_"+i+"_"+s);if(0==e.length)return!1;e.replaceWith(this.getCellHTML(i,s,t))},refreshRow:function(e,t){var i=$(this.box).find("#grid_"+this.name+"_frec_"+w2utils.escapeId(e)),s=$(this.box).find("#grid_"+this.name+"_rec_"+w2utils.escapeId(e));if(0<i.length){null==t&&(t=this.get(e,!0));var n=i.attr("line"),l=!this.records[t]||this.records[t].recid!=e,o="object"!=typeof this.url?this.url:this.url.get;if(0<this.searchData.length&&!o)for(var a=0;a<this.last.searchIds.length;a++)this.last.searchIds[a]==t&&(t=a);n=this.getRecordHTML(t,n,l);$(i).replaceWith(n[0]),$(s).replaceWith(n[1]);n=this.records[t].w2ui?this.records[t].w2ui.style:"";"string"==typeof n&&(i=$(this.box).find("#grid_"+this.name+"_frec_"+w2utils.escapeId(e)),s=$(this.box).find("#grid_"+this.name+"_rec_"+w2utils.escapeId(e)),i.attr("custom_style",n),s.attr("custom_style",n),i.hasClass("w2ui-selected")&&(n=n.replace("background-color","none")),i[0].style.cssText="height: "+this.recordHeight+"px;"+n,s[0].style.cssText="height: "+this.recordHeight+"px;"+n),l&&this.resize()}},refresh:function(){var n=this,e=(new Date).getTime(),t="object"!=typeof this.url?this.url:this.url.get;if(this.total<=0&&!t&&0===this.searchData.length&&(this.total=this.records.length),this.toolbar.disable("w2ui-edit","w2ui-delete"),this.box){var i=this.trigger({phase:"before",target:this.name,type:"refresh"});if(!0!==i.isCancelled){if(this.show.header?$("#grid_"+this.name+"_header").html(this.header+"&#160;").show():$("#grid_"+this.name+"_header").hide(),this.show.toolbar){if(!(this.toolbar&&this.toolbar.get("w2ui-column-on-off")&&this.toolbar.get("w2ui-column-on-off").checked)&&($("#grid_"+this.name+"_toolbar").show(),"object"==typeof this.toolbar))for(var s=this.toolbar.items,l=0;l<s.length;l++)"w2ui-search"!=s[l].id&&"break"!=s[l].type&&this.toolbar.refresh(s[l].id)}else $("#grid_"+this.name+"_toolbar").hide();this.searchClose();t=$("#grid_"+n.name+"_search_all");!this.multiSearch&&"all"==this.last.field&&0<this.searches.length&&(this.last.field=this.searches[0].field,this.last.label=this.searches[0].label);for(var o=0;o<this.searches.length;o++)this.searches[o].field==this.last.field&&(this.last.label=this.searches[o].label);this.last.multi?(t.attr("placeholder","["+w2utils.lang("Multiple Fields")+"]"),t.w2field("clear")):t.attr("placeholder",w2utils.lang(this.last.label)),t.val()!=this.last.search&&(r=this.last.search,(s=t.data("w2field"))&&(r=s.format(r)),t.val(r)),n.refreshBody(),this.show.footer?$("#grid_"+this.name+"_footer").html(this.getFooterHTML()).show():$("#grid_"+this.name+"_footer").hide();var a=$("#grid_"+this.name+"_searchClear");a.hide(),this.searchData.some(function(e){e=n.getSearch(e.field);if(n.last.multi||e&&!e.hidden&&-1==["list","enum"].indexOf(e.type))return a.show(),!0});var t=this.last.selection,r=0<this.records.length&&t.indexes.length==this.records.length,t=0<t.indexes.length&&0!==this.searchData.length&&t.indexes.length==this.last.searchIds.length;r||t?$("#grid_"+this.name+"_check_all").prop("checked",!0):$("#grid_"+this.name+"_check_all").prop("checked",!1),this.status();for(var d=n.find({"w2ui.expanded":!0},!0),u=0;u<d.length;u++)(s=n.records[d[u]].w2ui)&&!Array.isArray(s.children)&&(s.expanded=!1);return n.markSearch&&setTimeout(function(){for(var e=[],t=0;t<n.searchData.length;t++){var i=n.searchData[t],s=n.getSearch(i.field);s&&!s.hidden&&(s=n.getColumn(i.field,!0),e.push({field:i.field,search:i.value,col:s}))}0<e.length&&e.forEach(function(e){$(n.box).find('td[col="'+e.col+'"]').not(".w2ui-head").w2marker(e.search)})},50),this.show.toolbarSave&&(0<this.getChanges().length?this.toolbar.enable("w2ui-save"):this.toolbar.disable("w2ui-save")),this.trigger($.extend(i,{phase:"after"})),n.resize(),n.addRange("selection"),setTimeout(function(){n.resize(),n.scroll()},1),n.reorderColumns&&!n.last.columnDrag?n.last.columnDrag=n.initColumnDrag():!n.reorderColumns&&n.last.columnDrag&&n.last.columnDrag.remove(),(new Date).getTime()-e}}},refreshBody:function(){var n=this,e=this.find({"w2ui.summary":!0},!0);if(0<e.length){for(var t=0;t<e.length;t++)this.summary.push(this.records[e[t]]);for(t=e.length-1;0<=t;t--)this.records.splice(e[t],1)}this.scroll();var i=this.getRecordsHTML(),s=this.getColumnsHTML(),s='<div id="grid_'+this.name+'_frecords" class="w2ui-grid-frecords" style="margin-bottom: '+(w2utils.scrollBarSize()-1)+'px;">'+i[0]+'</div><div id="grid_'+this.name+'_records" class="w2ui-grid-records" onscroll="w2ui[\''+this.name+"'].scroll(event);\">"+i[1]+'</div><div id="grid_'+this.name+'_scroll1" class="w2ui-grid-scroll1" style="height: '+w2utils.scrollBarSize()+'px"></div><div id="grid_'+this.name+'_fcolumns" class="w2ui-grid-fcolumns">    <table><tbody>'+s[0]+'</tbody></table></div><div id="grid_'+this.name+'_columns" class="w2ui-grid-columns">    <table><tbody>'+s[1]+"</tbody></table></div>",l=$("#grid_"+this.name+"_body",n.box).html(s),o=$("#grid_"+this.name+"_records",n.box),s=$("#grid_"+this.name+"_frecords",n.box),a=this;"row"==this.selectType&&(o.on("mouseover mouseout","tr",function(e){$("#grid_"+a.name+"_frec_"+w2utils.escapeId($(this).attr("recid"))).toggleClass("w2ui-record-hover","mouseover"==e.type)}),s.on("mouseover mouseout","tr",function(e){$("#grid_"+a.name+"_rec_"+w2utils.escapeId($(this).attr("recid"))).toggleClass("w2ui-record-hover","mouseover"==e.type)})),w2utils.isIOS?o.add(s).on("click","tr",function(e){a.dblClick($(this).attr("recid"),e)}):o.add(s).on("click","tr",function(e){a.click($(this).attr("recid"),e)}).on("contextmenu","tr",function(e){a.contextMenu($(this).attr("recid"),null,e)}),l.data("scrolldata",{lastTime:0,lastDelta:0,time:0}).find(".w2ui-grid-frecords").on("mousewheel DOMMouseScroll ",function(e){e.preventDefault();var t=e.originalEvent,i=l.data("scrolldata"),s=$(this).siblings(".w2ui-grid-records").addBack().filter(".w2ui-grid-records"),e=null!=typeof t.wheelDelta?-1*t.wheelDelta/120:(t.detail||t.deltaY)/3,t=s.scrollTop();i.time=+new Date,i.lastTime<i.time-150&&(i.lastDelta=0),i.lastTime=i.time,i.lastDelta+=e,e=Math.abs(i.lastDelta)<1?0:Math.round(i.lastDelta),l.data("scrolldata",i),e*=(Math.round(o.height()/n.recordHeight)-1)*n.recordHeight/4,s.stop().animate({scrollTop:t+e},250,"linear")}),0===this.records.length&&this.msgEmpty?$("#grid_"+this.name+"_body").append('<div id="grid_'+this.name+'_empty_msg" class="w2ui-grid-empty-msg"><div>'+this.msgEmpty+"</div></div>"):0<$("#grid_"+this.name+"_empty_msg").length&&$("#grid_"+this.name+"_empty_msg").remove(),0<this.summary.length?(s=this.getSummaryHTML(),$("#grid_"+this.name+"_fsummary").html(s[0]).show(),$("#grid_"+this.name+"_summary").html(s[1]).show()):($("#grid_"+this.name+"_fsummary").hide(),$("#grid_"+this.name+"_summary").hide())},render:function(e){var x=this,t=(new Date).getTime();if(null!=e&&(0<$(this.box).find("#grid_"+this.name+"_body").length&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-grid w2ui-inactive").html(""),this.box=e),this.box){var i,s="object"!=typeof this.url?this.url:this.url.get,e=this.trigger({phase:"before",target:this.name,type:"render",box:e});if(!0!==e.isCancelled){if(this.reset(!0),!this.last.field)if(this.multiSearch&&this.show.searchAll)this.last.field="all",this.last.label=w2utils.lang("All Fields");else{for(var n=0;n<this.searches.length&&(this.searches[n].hidden||!1===this.searches[n].simple);)n++;n>=this.searches.length?(this.last.field="",this.last.label=""):(this.last.field=this.searches[n].field,this.last.label=this.searches[n].label)}$(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-grid w2ui-inactive").html('<div class="w2ui-grid-box">    <div id="grid_'+this.name+'_header" class="w2ui-grid-header"></div>    <div id="grid_'+this.name+'_toolbar" class="w2ui-grid-toolbar"></div>    <div id="grid_'+this.name+'_body" class="w2ui-grid-body"></div>    <div id="grid_'+this.name+'_fsummary" class="w2ui-grid-body w2ui-grid-summary"></div>    <div id="grid_'+this.name+'_summary" class="w2ui-grid-body w2ui-grid-summary"></div>    <div id="grid_'+this.name+'_footer" class="w2ui-grid-footer"></div>    <textarea id="grid_'+this.name+'_focus" class="w2ui-grid-focus-input" '+(w2utils.isIOS?"readonly":"")+"></textarea></div>"),"row"!=this.selectType&&$(this.box).addClass("w2ui-ss"),0<$(this.box).length&&($(this.box)[0].style.cssText+=this.style),this.initToolbar(),null!=this.toolbar&&this.toolbar.render($("#grid_"+this.name+"_toolbar")[0]),this.last.field&&"all"!=this.last.field&&(i=this.searchData,setTimeout(function(){x.initAllField(x.last.field,1==i.length?i[0].value:null)},1)),$("#grid_"+this.name+"_footer").html(this.getFooterHTML()),this.last.state||(this.last.state=this.stateSave(!0)),this.stateRestore(),s&&(this.clear(),this.refresh());for(var _,l=!1,o=0;o<this.searches.length;o++)if(this.searches[o].hidden){l=!0;break}return l?(this.searchReset(!1),s||setTimeout(function(){x.searchReset()},1)):this.reload(),$(this.box).find("#grid_"+this.name+"_focus").on("focus",function(e){clearTimeout(x.last.kbd_timer),x.hasFocus||x.focus()}).on("blur",function(e){clearTimeout(x.last.kbd_timer),x.last.kbd_timer=setTimeout(function(){x.hasFocus&&x.blur()},100)}).on("paste",function(e){var t,i=e.originalEvent.clipboardData||null;i&&i.types&&-1!=i.types.indexOf("text/plain")?(e.preventDefault(),-1!=(i=i.getData("text/plain")).indexOf("\r")&&-1==i.indexOf("\n")&&(i=i.replace(/\r/g,"\n")),w2ui[x.name].paste(i)):(t=this,setTimeout(function(){w2ui[x.name].paste(t.value),t.value=""},1))}).on("keydown",function(e){w2ui[x.name].keydown.call(w2ui[x.name],e)}),$(this.box).off("mousedown").on("mousedown",function(e){if(1!=e.which)return;"text"==x.last.userSelect&&(delete x.last.userSelect,$(x.box).find(".w2ui-grid-body").css(w2utils.cssPrefix("user-select","none")));if("row"==x.selectType&&($(e.target).parents().hasClass("w2ui-head")||$(e.target).hasClass("w2ui-head")))return;if(x.last.move&&"expand"==x.last.move.type)return;if(e.altKey)$(x.box).find(".w2ui-grid-body").css(w2utils.cssPrefix("user-select","text")),x.selectNone(),x.last.move={type:"text-select"},x.last.userSelect="text";else{for(var t=e.target,i={x:e.offsetX-10,y:e.offsetY-10},s=!1;t&&(!t.classList||!t.classList.contains("w2ui-grid"));)t.tagName&&"TD"==t.tagName.toUpperCase()&&(s=!0),t.tagName&&"TR"!=t.tagName.toUpperCase()&&1==s&&(i.x+=t.offsetLeft,i.y+=t.offsetTop),t=t.parentNode;x.last.move={x:e.screenX,y:e.screenY,divX:0,divY:0,focusX:i.x,focusY:i.y,recid:$(e.target).parents("tr").attr("recid"),column:parseInt(("TD"==e.target.tagName.toUpperCase()?$(e.target):$(e.target).parents("td")).attr("col")),type:"select",ghost:!1,start:!0},null==x.last.move.recid&&(x.last.move.type="select-column");var n,l=e.target,o=$(x.box).find("#grid_"+x.name+"_focus");x.last.move&&(r=x.last.move.focusX,a=x.last.move.focusY,((n=$(l).parents("table").parent()).hasClass("w2ui-grid-records")||n.hasClass("w2ui-grid-frecords")||n.hasClass("w2ui-grid-columns")||n.hasClass("w2ui-grid-fcolumns")||n.hasClass("w2ui-grid-summary"))&&(r=x.last.move.focusX-$(x.box).find("#grid_"+x.name+"_records").scrollLeft(),a=x.last.move.focusY-$(x.box).find("#grid_"+x.name+"_records").scrollTop()),($(l).hasClass("w2ui-grid-footer")||0<$(l).parents("div.w2ui-grid-footer").length)&&(a=$(x.box).find("#grid_"+x.name+"_footer").position().top),n.hasClass("w2ui-scroll-wrapper")&&n.parent().hasClass("w2ui-toolbar")&&(r=x.last.move.focusX-n.scrollLeft()),o.css({left:r-10,top:a})),setTimeout(function(){-1!=["INPUT","TEXTAREA","SELECT"].indexOf(l.tagName.toUpperCase())?$(l).focus():o.is(":focus")||o.focus()},50),x.multiSelect||x.reorderRows||"drag"!=x.last.move.type||delete x.last.move}{var a,r,d,u;1==x.reorderRows&&("TD"!=(r=e.target).tagName.toUpperCase()&&(r=$(r).parents("td")[0]),$(r).hasClass("w2ui-col-number")||$(r).hasClass("w2ui-col-order")?(x.selectNone(),x.last.move.reorder=!0,a=$(x.box).find(".w2ui-even.w2ui-empty-record").css("background-color"),r=$(x.box).find(".w2ui-odd.w2ui-empty-record").css("background-color"),$(x.box).find(".w2ui-even td").not(".w2ui-col-number").css("background-color",a),$(x.box).find(".w2ui-odd td").not(".w2ui-col-number").css("background-color",r),(r=x.last.move).ghost||(d=$("#grid_"+x.name+"_rec_"+r.recid),t=d.parents("table").find("tr:first-child").clone(),r.offsetY=e.offsetY,r.from=r.recid,r.pos=d.position(),r.ghost=$(d).clone(!0),r.ghost.removeAttr("id"),d.find("td").remove(),d.append('<td colspan="1000"><div style="height: '+x.recordHeight+'px; background-color: #eee; border-bottom: 1px dashed #aaa; border-top: 1px dashed #aaa;"></div></td>'),(u=$(x.box).find(".w2ui-grid-records")).append('<div id="grid_'+x.name+'_ghost_line" style="position: absolute; z-index: 999999; pointer-events: none; width: 100%;"></div>'),u.append('<table id="grid_'+x.name+'_ghost" style="position: absolute; z-index: 999998; opacity: 0.9; pointer-events: none;"></table>'),$("#grid_"+x.name+"_ghost").append(t).append(r.ghost)),d=$("#grid_"+x.name+"_ghost"),u=$(x.box).find(".w2ui-grid-records"),d.css({top:r.pos.top+u.scrollTop(),left:r.pos.left,"border-top":"1px solid #aaa","border-bottom":"1px solid #aaa"})):x.last.move.reorder=!1)}$(document).on("mousemove.w2ui-"+x.name,c).on("mouseup.w2ui-"+x.name,h),e.stopPropagation()}),this.updateToolbar(),this.trigger($.extend(e,{phase:"after"})),0===$(".w2ui-layout").length&&$(window).off("resize.w2ui-"+x.name).on("resize.w2ui-"+x.name,function(e){null==w2ui[x.name]?$(window).off("resize.w2ui-"+x.name):w2ui[x.name].resize()}),(new Date).getTime()-t}}function c(e){var t=x.last.move;if(t&&-1!=["select","select-column"].indexOf(t.type)&&(t.divX=e.screenX-t.x,t.divY=e.screenY-t.y,!(Math.abs(t.divX)<=1&&Math.abs(t.divY)<=1))){if(x.last.cancelClick=!0,1==x.reorderRows&&x.last.move.reorder){var i,s,n=$(x.box).find(".w2ui-grid-records");return(l="-none-"==(l=(d=$(e.target).parents("tr")).attr("recid"))?"bottom":l)!=t.from&&($("#grid_"+x.name+"_rec_"+t.recid),s=$("#grid_"+x.name+"_rec_"+l),$(x.box).find(".insert-before"),s.addClass("insert-before"),t.lastY=e.screenY,t.to=l,i=s.position(),s=$("#grid_"+x.name+"_ghost_line"),i?s.css({top:i.top+n.scrollTop(),left:t.pos.left,"border-top":"2px solid #769EFC"}):s.css({"border-top":"2px solid transparent"})),void $("#grid_"+x.name+"_ghost").css({top:t.pos.top+t.divY+n.scrollTop(),left:t.pos.left})}t.start&&t.recid&&(x.selectNone(),t.start=!1);var l,o=[];if(null==(l=("TR"==e.target.tagName.toUpperCase()?$(e.target):$(e.target).parents("tr")).attr("recid"))){if("row"!=x.selectType&&(!x.last.move||"select"!=x.last.move.type)){n=parseInt($(e.target).parents("td").attr("col"));if(isNaN(n))x.removeRange("column-selection"),$(x.box).find(".w2ui-grid-columns .w2ui-col-header, .w2ui-grid-fcolumns .w2ui-col-header").removeClass("w2ui-col-selected"),$(x.box).find(".w2ui-col-number").removeClass("w2ui-row-selected"),delete t.colRange;else{var a=n+"-"+n;t.column<n&&(a=t.column+"-"+n);for(var r=[],d=(a=t.column>n?n+"-"+t.column:a).split("-"),u=parseInt(d[0]);u<=parseInt(d[1]);u++)r.push(u);if(t.colRange!=a&&!0!==(_=x.trigger({phase:"before",type:"columnSelect",target:x.name,columns:r,isCancelled:!1})).isCancelled){null==t.colRange&&x.selectNone();d=a.split("-");$(x.box).find(".w2ui-grid-columns .w2ui-col-header, .w2ui-grid-fcolumns .w2ui-col-header").removeClass("w2ui-col-selected");for(var c=parseInt(d[0]);c<=parseInt(d[1]);c++)$(x.box).find("#grid_"+x.name+"_column_"+c+" .w2ui-col-header").addClass("w2ui-col-selected");$(x.box).find(".w2ui-col-number").not(".w2ui-head").addClass("w2ui-row-selected"),t.colRange=a,x.removeRange("column-selection"),x.addRange({name:"column-selection",range:[{recid:x.records[0].recid,column:d[0]},{recid:x.records[x.records.length-1].recid,column:d[1]}],style:"background-color: rgba(90, 145, 234, 0.1)"})}}}}else{a=x.get(t.recid,!0);if(!(null==a||x.records[a]&&x.records[a].recid!=t.recid))if(null!=(h=x.get(l,!0))){var h,p=parseInt(t.column),f=parseInt(("TD"==e.target.tagName.toUpperCase()?$(e.target):$(e.target).parents("td")).attr("col"));isNaN(p)&&isNaN(f)&&(p=0,f=x.columns.length-1),h<a&&(d=a,a=h,h=d);d="ind1:"+a+",ind2;"+h+",col1:"+p+",col2:"+f;if(t.range!=d){t.range=d;for(var g=a;g<=h;g++)if(!(0<x.last.searchIds.length&&-1==x.last.searchIds.indexOf(g)))if("row"!=x.selectType){f<p&&(d=p,p=f,f=d);for(var d=[],m=p;m<=f;m++)x.columns[m].hidden||o.push({recid:x.records[g].recid,column:parseInt(m)})}else o.push(x.records[g].recid);if("row"!=x.selectType){for(var w=x.getSelection(),d=[],v=0;v<o.length;v++){for(var y=!1,b=0;b<w.length;b++)o[v].recid==w[b].recid&&o[v].column==w[b].column&&(y=!0);y||d.push({recid:o[v].recid,column:o[v].column})}x.select(d);for(d=[],b=0;b<w.length;b++){for(y=!1,v=0;v<o.length;v++)o[v].recid==w[b].recid&&o[v].column==w[b].column&&(y=!0);y||d.push({recid:w[b].recid,column:w[b].column})}x.unselect(d)}else if(x.multiSelect){for(w=x.getSelection(),v=0;v<o.length;v++)-1==w.indexOf(o[v])&&x.select(o[v]);for(b=0;b<w.length;b++)-1==o.indexOf(w[b])&&x.unselect(w[b])}}}}}}function h(e){var t=x.last.move;if(setTimeout(function(){delete x.last.cancelClick},1),!$(e.target).parents().hasClass(".w2ui-head")&&!$(e.target).hasClass(".w2ui-head")){if(t&&-1!=["select","select-column"].indexOf(t.type)){if(null!=t.colRange&&!0!==_.isCancelled){for(var i=t.colRange.split("-"),s=[],n=0;n<x.records.length;n++){for(var l=[],o=parseInt(i[0]);o<=parseInt(i[1]);o++)l.push(o);s.push({recid:x.records[n].recid,column:l})}x.removeRange("column-selection"),x.trigger($.extend(_,{phase:"after"})),x.select(s)}if(1==x.reorderRows&&x.last.move.reorder){var a=x.trigger({phase:"before",target:x.name,type:"reorderRow",recid:t.from,moveAfter:t.to});if(!0===a.isCancelled)return $("#grid_"+x.name+"_ghost").remove(),$("#grid_"+x.name+"_ghost_line").remove(),x.refresh(),void delete x.last.move;var r=x.get(t.from,!0),e=x.get(t.to,!0);"bottom"==t.to&&(e=x.records.length);i=x.records[r];null!=r&&null!=e&&(x.records.splice(r,1),e<r?x.records.splice(e,0,i):x.records.splice(e-1,0,i)),$("#grid_"+x.name+"_ghost").remove(),$("#grid_"+x.name+"_ghost_line").remove(),x.refresh(),x.trigger($.extend(a,{phase:"after"}))}}delete x.last.move,$(document).off(".w2ui-"+x.name)}}},destroy:function(){var e=this.trigger({phase:"before",target:this.name,type:"destroy"});!0!==e.isCancelled&&($(this.box).off(),"object"==typeof this.toolbar&&this.toolbar.destroy&&this.toolbar.destroy(),0<$(this.box).find("#grid_"+this.name+"_body").length&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-grid w2ui-inactive").html(""),delete w2ui[this.name],this.trigger($.extend(e,{phase:"after"})))},initColumnOnOff:function(){if(this.show.toolbarColumns){for(var e=this,t='<div class="w2ui-col-on-off"><table><tbody><tr id="grid_'+this.name+'_column_ln_check" onclick="w2ui[\''+e.name+'\'].columnOnOff(event, \'line-numbers\'); event.stopPropagation();">   <td style="width: 30px; text-align: center; padding-right: 3px; color: #888;">      <span class="w2ui-column-check w2ui-icon-'+(e.show.lineNumbers?"check":"empty")+'"></span>   </td>   <td>      <label>'+w2utils.lang("Line #")+"</label>   </td></tr>",i=0;i<this.columns.length;i++){var s=this.columns[i],n=this.columns[i].text;!1!==s.hideable&&(n=(n=!n&&this.columns[i].tooltip?this.columns[i].tooltip:n)||"- column "+(parseInt(i)+1)+" -",t+='<tr id="grid_'+this.name+"_column_"+i+'_check"        onclick="w2ui[\''+e.name+"'].columnOnOff(event, '"+s.field+'\'); event.stopPropagation();">   <td style="width: 30px; text-align: center; padding-right: 3px; color: #888;">      <span class="w2ui-column-check w2ui-icon-'+(s.hidden?"empty":"check")+'"></span>   </td>   <td>       <label>'+w2utils.stripTags(n)+"</label>   </td></tr>")}var l="object"!=typeof this.url?this.url:this.url.get;(l&&e.show.skipRecords||e.show.saveRestoreState)&&(t+='<tr style="pointer-events: none"><td colspan="2"><div style="border-top: 1px solid #ddd;"></div></td></tr>'),l&&e.show.skipRecords&&(t+='<tr><td colspan="2" style="padding: 0px">    <div style="cursor: pointer; padding: 2px 8px; cursor: default">'+w2utils.lang("Skip")+'        <input type="text" style="width: 60px" value="'+this.offset+'"             onkeydown="if ([48,49,50,51,52,53,54,55,56,57,58,13,8,46,37,39].indexOf(event.keyCode) == -1) { event.preventDefault() }"            onkeypress="if (event.keyCode == 13) {                w2ui[\''+e.name+"'].skip(this.value);                jQuery('.w2ui-overlay')[0].hide();             }\"/> "+w2utils.lang("Records")+"    </div></td></tr>"),e.show.saveRestoreState&&(t+='<tr><td colspan="2" onclick="var obj = w2ui[\''+e.name+"']; obj.toolbar.uncheck('w2ui-column-on-off'); obj.stateSave();\">    <div style=\"cursor: pointer; padding: 4px 8px; cursor: default\">"+w2utils.lang("Save Grid State")+'</div></td></tr><tr><td colspan="2" onclick="var obj = w2ui[\''+e.name+"']; obj.toolbar.uncheck('w2ui-column-on-off'); obj.stateReset();\">    <div style=\"cursor: pointer; padding: 4px 8px; cursor: default\">"+w2utils.lang("Restore Default State")+"</div></td></tr>"),t+="</tbody></table></div>",this.toolbar.get("w2ui-column-on-off").html=t}},initColumnDrag:function(e){if(this.columnGroups&&this.columnGroups.length)throw"Draggable columns are not currently supported with column groups.";var r=this,d={};function t(){d.pressed=!1,clearTimeout(d.timeout)}function i(o){d.timeout&&clearTimeout(d.timeout);var a=this;d.pressed=!0,d.timeout=setTimeout(function(){if(d.pressed&&0!==d.numberPreColumnsPresent){var e,t,i,s=["w2ui-col-number","w2ui-col-expand","w2ui-col-select"].concat(["w2ui-head-last"]);if($(o.originalEvent.target).parents().hasClass("w2ui-head")){for(var n=0,l=s.length;n<l;n++)if($(o.originalEvent.target).parents().hasClass(s[n]))return;if(d.numberPreColumnsPresent=$(r.box).find(".w2ui-head.w2ui-col-number, .w2ui-head.w2ui-col-expand, .w2ui-head.w2ui-col-select").length,d.columnHead=t=$(o.originalEvent.target).parents(".w2ui-head"),d.originalPos=i=parseInt(t.attr("col"),10),!0===(i=r.trigger({type:"columnDragStart",phase:"before",originalEvent:o,origColumnNumber:i,target:t[0]})).isCancelled)return!1;e=d.columns=$(r.box).find(".w2ui-head:not(.w2ui-head-last)"),$(document).on("mouseup",c),$(document).on("mousemove",u),d.ghost=$(a).clone(!0),$(d.ghost).find('[col]:not([col="'+d.originalPos+'"]), .w2ui-toolbar, .w2ui-grid-header').remove(),$(d.ghost).find(".w2ui-col-number, .w2ui-col-expand, .w2ui-col-select").remove(),$(d.ghost).find(".w2ui-grid-body").css({top:0}),t=$(d.ghost).find('[col="'+d.originalPos+'"]'),$(document.body).append(d.ghost),$(d.ghost).css({width:0,height:0,margin:0,position:"fixed",zIndex:999999,opacity:0}).addClass(".w2ui-grid-ghost").animate({width:t.width(),height:$(r.box).find(".w2ui-grid-body:first").height(),left:o.pageX,top:o.pageY,opacity:.8},0),d.offsets=[];for(n=0,l=e.length;n<l;n++)d.offsets.push($(e[n]).offset().left);r.trigger($.extend(i,{phase:"after"}))}}},150)}function u(e){var t,i,s;d.pressed&&(i=e.originalEvent.pageX,s=e.originalEvent.pageY,t=d.offsets,e=$(".w2ui-head:not(.w2ui-head-last)").width(),d.targetInt=Math.max(d.numberPreColumnsPresent,function(e,t,i){{if(e<=t[0])return 0;if(e>=t[t.length-1]+i)return t.length;for(var s=0,n=t.length;s<n;s++){var l=t[s],o=t[s+1]||t[s]+i,a=(o-t[s])/2+t[s];if(l<e&&e<=a)return s;if(a<e&&e<=o)return s+1}return intersection}}(i,t,e)),function(e){d.marker||d.markerLeft||(d.marker=$('<div class="col-intersection-marker"><div class="top-marker"></div><div class="bottom-marker"></div></div>'),d.markerLeft=$('<div class="col-intersection-marker"><div class="top-marker"></div><div class="bottom-marker"></div></div>'));d.lastInt&&d.lastInt===e||(d.lastInt=e,d.marker.remove(),d.markerLeft.remove(),$(".w2ui-head").removeClass("w2ui-col-intersection"),e>=d.columns.length?($(d.columns[d.columns.length-1]).children("div:last").append(d.marker.addClass("right").removeClass("left")),$(d.columns[d.columns.length-1]).addClass("w2ui-col-intersection")):e<=d.numberPreColumnsPresent?($(d.columns[d.numberPreColumnsPresent]).prepend(d.marker.addClass("left").removeClass("right")).css({position:"relative"}),$(d.columns[d.numberPreColumnsPresent]).prev().addClass("w2ui-col-intersection")):($(d.columns[e]).children("div:last").prepend(d.marker.addClass("left").removeClass("right")),$(d.columns[e]).prev().children("div:last").append(d.markerLeft.addClass("right").removeClass("left")).css({position:"relative"}),$(d.columns[e-1]).addClass("w2ui-col-intersection")))}(d.targetInt),i=i,s=s,$(d.ghost).css({left:i-10,top:s-10}))}function c(e){d.pressed=!1;var t,i,s,n=$(".w2ui-grid-ghost"),l=r.trigger({type:"columnDragEnd",phase:"before",originalEvent:e,target:d.columnHead[0]});if(!0===l.isCancelled)return!1;t=r.columns[d.originalPos],i=r.columns,s=$(d.columns[Math.min(d.lastInt,d.columns.length-1)]),(e=d.lastInt<d.columns.length?parseInt(s.attr("col")):i.length)!==d.originalPos+1&&e!==d.originalPos&&s&&s.length?($(d.ghost).animate({top:$(r.box).offset().top,left:s.offset().left,width:0,height:0,opacity:.2},300,function(){$(this).remove(),n.remove()}),i.splice(e,0,$.extend({},t)),i.splice(i.indexOf(t),1)):($(d.ghost).remove(),n.remove()),$(document).off("mouseup",c),$(document).off("mousemove",u),d.marker&&d.marker.remove(),d={},r.refresh(),r.trigger($.extend(l,{phase:"after",targetColumnNumber:e-1}))}return d.lastInt=null,d.pressed=!1,d.timeout=null,d.columnHead=null,$(r.box).on("mousedown",i),$(r.box).on("mouseup",t),{remove:function(){$(r.box).off("mousedown",i),$(r.box).off("mouseup",t),$(r.box).find(".w2ui-head").removeAttr("draggable"),r.last.columnDrag=!1}}},columnOnOff:function(e,t){var i=$(e.target).parents("tr").find(".w2ui-column-check"),s=this.trigger({phase:"before",target:this.name,type:"columnOnOff",field:t,originalEvent:e});if(!0!==s.isCancelled){for(var n,l=this,e=!(e.shiftKey||e.metaKey||e.ctrlKey||$(e.target).hasClass("w2ui-column-check")),o=l.find({"w2ui.expanded":!0},!0),a=0;a<o.length;a++){var r=this.records[a].w2ui;r&&!Array.isArray(r.children)&&(this.records[a].w2ui.expanded=!1)}"line-numbers"==t?(this.show.lineNumbers=!this.show.lineNumbers,this.show.lineNumbers?i.addClass("w2ui-icon-check").removeClass("w2ui-icon-empty"):i.addClass("w2ui-icon-empty").removeClass("w2ui-icon-check"),this.refreshBody(),this.resizeRecords()):(n=this.getColumn(t)).hidden?(i.addClass("w2ui-icon-check").removeClass("w2ui-icon-empty"),setTimeout(function(){l.showColumn(n.field)},e?0:50)):(i.addClass("w2ui-icon-empty").removeClass("w2ui-icon-check"),setTimeout(function(){l.hideColumn(n.field)},e?0:50)),e&&setTimeout(function(){$().w2overlay({name:l.name+"_toolbar"})},40),this.trigger($.extend(s,{phase:"after"}))}},scrollToColumn:function(e){if(null!=e){for(var t=0,i=!1,s=0;s<this.columns.length;s++){var n=this.columns[s];if(n.field==e){i=!0;break}n.frozen||n.hidden||(t+=parseInt(n.sizeCalculated||n.size))}i&&(this.last.scrollLeft=t+1,this.scroll())}},initToolbar:function(){if(null==this.toolbar.render){var e,t=this.toolbar.items||[];if(this.toolbar.items=[],this.toolbar=$().w2toolbar($.extend(!0,{},this.toolbar,{name:this.name+"_toolbar",owner:this})),this.show.toolbarReload&&this.toolbar.items.push($.extend(!0,{},this.buttons.reload)),this.show.toolbarColumns&&this.toolbar.items.push($.extend(!0,{},this.buttons.columns)),(this.show.toolbarReload||this.show.toolbarColumns)&&this.toolbar.items.push({type:"break",id:"w2ui-break0"}),this.show.toolbarInput&&(e='<div class="w2ui-toolbar-search"><table cellpadding="0" cellspacing="0"><tbody><tr>    <td>'+this.buttons.search.html+'</td>    <td>        <input type="text" id="grid_'+this.name+'_search_all" class="w2ui-search-all" tabindex="-1"             autocapitalize="off" autocomplete="off" autocorrect="off" spellcheck="false"            placeholder="'+w2utils.lang(this.last.label)+'" value="'+this.last.search+'"            onfocus="var grid = w2ui[\''+this.name+'\']; clearTimeout(grid.last.kbd_timer); grid.searchShowFields(true); grid.searchClose()"            onkeydown="if (event.keyCode == 13 &amp;&amp; w2utils.isIE) this.onchange();"            onchange="                var grid = w2ui[\''+this.name+"'];                 var val = this.value;                 var sel = jQuery(this).data('selected');                var fld = jQuery(this).data('w2field');                 if (fld) val = fld.clean(val);                if (fld &amp;&amp; fld.type == 'list' &amp;&amp; sel &amp;&amp; typeof sel.id == 'undefined') {                   grid.searchReset();                } else {                   grid.search(grid.last.field, val);                }            \"/>    </td>    <td>        <div class=\"w2ui-search-clear\" id=\"grid_"+this.name+'_searchClear"               onclick="var obj = w2ui[\''+this.name+'\']; obj.searchReset();" style="display: none"        >&#160;&#160;</div>    </td></tr></tbody></table></div>',this.toolbar.items.push({type:"html",id:"w2ui-search",html:e})),this.show.toolbarSearch&&this.multiSearch&&0<this.searches.length&&this.toolbar.items.push($.extend(!0,{},this.buttons["search-go"])),(this.show.toolbarSearch||this.show.toolbarInput)&&(this.show.toolbarAdd||this.show.toolbarEdit||this.show.toolbarDelete||this.show.toolbarSave)&&this.toolbar.items.push({type:"break",id:"w2ui-break1"}),this.show.toolbarAdd&&Array.isArray(t)&&-1==t.map(function(e){return e.id}).indexOf(this.buttons.add.id)&&this.toolbar.items.push($.extend(!0,{},this.buttons.add)),this.show.toolbarEdit&&Array.isArray(t)&&-1==t.map(function(e){return e.id}).indexOf(this.buttons.edit.id)&&this.toolbar.items.push($.extend(!0,{},this.buttons.edit)),this.show.toolbarDelete&&Array.isArray(t)&&-1==t.map(function(e){return e.id}).indexOf(this.buttons.delete.id)&&this.toolbar.items.push($.extend(!0,{},this.buttons.delete)),this.show.toolbarSave&&Array.isArray(t)&&-1==t.map(function(e){return e.id}).indexOf(this.buttons.save.id)&&((this.show.toolbarAdd||this.show.toolbarDelete||this.show.toolbarEdit)&&this.toolbar.items.push({type:"break",id:"w2ui-break2"}),this.toolbar.items.push($.extend(!0,{},this.buttons.save))),t)for(var i=0;i<t.length;i++)this.toolbar.items.push(t[i]);var o=this;this.toolbar.on("click",function(e){var t=o.trigger({phase:"before",type:"toolbar",target:e.target,originalEvent:e});if(!0!==t.isCancelled){var i=e.target;switch(i){case"w2ui-reload":if(!0===(s=o.trigger({phase:"before",type:"reload",target:o.name})).isCancelled)return!1;o.reload(),o.trigger($.extend(s,{phase:"after"}));break;case"w2ui-column-on-off":o.initColumnOnOff(),o.initResize(),o.resize();break;case"w2ui-search-advanced":this.get(i).checked?o.searchClose():o.searchOpen(),o.toolbar.tooltipHide("w2ui-search-advanced"),e.preventDefault();break;case"w2ui-add":if(!0===(s=o.trigger({phase:"before",target:o.name,type:"add",recid:null})).isCancelled)return!1;o.trigger($.extend(s,{phase:"after"})),setTimeout(function(){$().w2tag()},20);break;case"w2ui-edit":var s,n=o.getSelection(),l=null;if(1==n.length&&(l=n[0]),!0===(s=o.trigger({phase:"before",target:o.name,type:"edit",recid:l})).isCancelled)return!1;o.trigger($.extend(s,{phase:"after"})),setTimeout(function(){$().w2tag()},20);break;case"w2ui-delete":o.delete();break;case"w2ui-save":o.save()}o.trigger($.extend(t,{phase:"after"}))}}),this.toolbar.on("refresh",function(e){var t;"w2ui-search"==e.target&&(t=o.searchData,setTimeout(function(){o.initAllField(o.last.field,1==t.length?t[0].value:null)},1))})}},initResize:function(){var n=this;$(this.box).find(".w2ui-resizer").off(".grid-col-resize").on("click.grid-col-resize",function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0,e.preventDefault&&e.preventDefault()}).on("mousedown.grid-col-resize",function(e){e=e||window.event,n.resizing=!0,n.last.tmp={x:e.screenX,y:e.screenY,gx:e.screenX,gy:e.screenY,col:parseInt($(this).attr("name"))},n.last.tmp.tds=$("#grid_"+n.name+"_body table tr:first-child td[col="+n.last.tmp.col+"]"),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0,e.preventDefault&&e.preventDefault();for(var t=0;t<n.columns.length;t++)n.columns[t].hidden||(null==n.columns[t].sizeOriginal&&(n.columns[t].sizeOriginal=n.columns[t].size),n.columns[t].size=n.columns[t].sizeCalculated);var i,s={phase:"before",type:"columnResize",target:n.name,column:n.last.tmp.col,field:n.columns[n.last.tmp.col].field},s=n.trigger($.extend(s,{resizeBy:0,originalEvent:e}));$(document).off(".grid-col-resize").on("mousemove.grid-col-resize",function(e){var t;1==n.resizing&&(e=e||window.event,!0!==(s=n.trigger($.extend(s,{resizeBy:e.screenX-n.last.tmp.gx,originalEvent:e}))).isCancelled?(n.last.tmp.x=e.screenX-n.last.tmp.x,n.last.tmp.y=e.screenY-n.last.tmp.y,t=parseInt(n.columns[n.last.tmp.col].size)+n.last.tmp.x+"px",n.columns[n.last.tmp.col].size=t,i&&clearTimeout(i),i=setTimeout(function(){n.resizeRecords(),n.scroll()},100),n.last.tmp.tds.css({width:t}),n.last.tmp.x=e.screenX,n.last.tmp.y=e.screenY):s.isCancelled=!1)}).on("mouseup.grid-col-resize",function(e){delete n.resizing,$(document).off(".grid-col-resize"),n.resizeRecords(),n.scroll(),n.trigger($.extend(s,{phase:"after",originalEvent:e}))})}).on("dblclick.grid-col-resize",function(e){var t=parseInt($(this).attr("name")),i=n.columns[t],s=0;if(!1===i.autoResize)return!0;e.stopPropagation?e.stopPropagation():e.cancelBubble=!0,e.preventDefault&&e.preventDefault(),$('.w2ui-grid-records td[col="'+t+'"] > div',n.box).each(function(){var e=this.offsetWidth-this.scrollWidth;e<s&&(s=e-3)});t={phase:"before",type:"columnAutoResize",target:n.name,column:i,field:i.field};!0!==(t=n.trigger($.extend(t,{resizeBy:Math.abs(s),originalEvent:e}))).isCancelled?(s<0&&(i.size=Math.min(parseInt(i.size)+Math.abs(s),i.max||1/0)+"px",n.resizeRecords(),n.resizeRecords(),n.scroll()),n.trigger($.extend(t,{phase:"after",originalEvent:e}))):t.isCancelled=!1}).each(function(e,t){var i=$(t).parent();$(t).css({height:i.height(),"margin-left":i.width()-3+"px"})})},resizeBoxes:function(){var e=$("#grid_"+this.name+"_header"),t=$("#grid_"+this.name+"_toolbar"),i=$("#grid_"+this.name+"_fsummary"),s=$("#grid_"+this.name+"_summary"),n=$("#grid_"+this.name+"_footer"),l=$("#grid_"+this.name+"_body");this.show.header&&e.css({top:"0px",left:"0px",right:"0px"}),this.show.toolbar&&t.css({top:0+(this.show.header?w2utils.getSize(e,"height"):0)+"px",left:"0px",right:"0px"}),0<this.summary.length&&(i.css({bottom:0+(this.show.footer?w2utils.getSize(n,"height"):0)+"px"}),s.css({bottom:0+(this.show.footer?w2utils.getSize(n,"height"):0)+"px",right:"0px"})),this.show.footer&&n.css({bottom:"0px",left:"0px",right:"0px"}),l.css({top:0+(this.show.header?w2utils.getSize(e,"height"):0)+(this.show.toolbar?w2utils.getSize(t,"height"):0)+"px",bottom:0+(this.show.footer?w2utils.getSize(n,"height"):0)+(0<this.summary.length?w2utils.getSize(s,"height"):0)+"px",left:"0px",right:"0px"})},resizeRecords:function(){var l=this;$(this.box).find(".w2ui-empty-record").remove();var e=$(this.box),t=$(this.box).find("> div.w2ui-grid-box"),i=$("#grid_"+this.name+"_header"),s=$("#grid_"+this.name+"_toolbar"),n=$("#grid_"+this.name+"_summary"),o=$("#grid_"+this.name+"_fsummary"),a=$("#grid_"+this.name+"_footer"),r=$("#grid_"+this.name+"_body"),d=$("#grid_"+this.name+"_columns"),u=$("#grid_"+this.name+"_fcolumns"),c=$("#grid_"+this.name+"_records"),h=$("#grid_"+this.name+"_frecords"),p=$("#grid_"+this.name+"_scroll1"),f=8*String(this.total).length+10;f<34&&(f=34),null!=this.lineNumberWidth&&(f=this.lineNumberWidth);for(var g,m=!1,w=!1,v=0,y=0;y<l.columns.length;y++)l.columns[y].frozen||l.columns[y].hidden||(v+=parseInt(l.columns[y].sizeCalculated||l.columns[y].size));c.width()<v&&(m=!0),r.height()-d.height()<$(c).find(">table").height()+(m?w2utils.scrollBarSize():0)&&(w=!0),this.fixedBody?(g=t.height()-(this.show.header?w2utils.getSize(i,"height"):0)-(this.show.toolbar?w2utils.getSize(s,"height"):0)-("none"!=n.css("display")?w2utils.getSize(n,"height"):0)-(this.show.footer?w2utils.getSize(a,"height"):0),r.css("height",g)):(g=w2utils.getSize(d,"height")+w2utils.getSize($("#grid_"+l.name+"_records table"),"height")+(m?w2utils.scrollBarSize():0),l.height=g+w2utils.getSize(t,"+height")+(l.show.header?w2utils.getSize(i,"height"):0)+(l.show.toolbar?w2utils.getSize(s,"height"):0)+("none"!=n.css("display")?w2utils.getSize(n,"height"):0)+(l.show.footer?w2utils.getSize(a,"height"):0),t.css("height",l.height),r.css("height",g),e.css("height",w2utils.getSize(t,"height")+w2utils.getSize(e,"+height")));t=this.records.length,e="object"!=typeof this.url?this.url:this.url.get;if(0==this.searchData.length||e||(t=this.last.searchIds.length),this.fixedBody||(w=!1),m||w?(d.find("> table > tbody > tr:nth-child(1) td.w2ui-head-last").css("width",w2utils.scrollBarSize()).show(),c.css({top:(0<this.columnGroups.length&&this.show.columns?1:0)+w2utils.getSize(d,"height")+"px","-webkit-overflow-scrolling":"touch","overflow-x":m?"auto":"hidden","overflow-y":w?"auto":"hidden"})):(d.find("> table > tbody > tr:nth-child(1) td.w2ui-head-last").hide(),c.css({top:(0<this.columnGroups.length&&this.show.columns?1:0)+w2utils.getSize(d,"height")+"px",overflow:"hidden"}),0<c.length&&(this.last.scrollTop=0,this.last.scrollLeft=0)),m?(h.css("margin-bottom",w2utils.scrollBarSize()),p.show()):(h.css("margin-bottom",0),p.hide()),h.css({overflow:"hidden",top:c.css("top")}),this.show.emptyRecords&&!w){var b=Math.floor(c.height()/this.recordHeight)-1,m=0;if((m=c[0]?c[0].scrollHeight-b*this.recordHeight:m)>=this.recordHeight&&(m-=this.recordHeight,b++),this.fixedBody){for(var x=t;x<b;x++)_(x,this.recordHeight,this);_(b,m,this)}}function _(e,t,i){var s,n="",l="";n+='<tr class="'+(e%2?"w2ui-even":"w2ui-odd")+' w2ui-empty-record" recid="-none-" style="height: '+t+'px">',l+='<tr class="'+(e%2?"w2ui-even":"w2ui-odd")+' w2ui-empty-record" recid="-none-" style="height: '+t+'px">',i.show.lineNumbers&&(n+='<td class="w2ui-col-number"></td>'),i.show.selectColumn&&(n+='<td class="w2ui-grid-data w2ui-col-select"></td>'),i.show.expandColumn&&(n+='<td class="w2ui-grid-data w2ui-col-expand"></td>'),l+='<td class="w2ui-grid-data-spacer" col="start" style="border-right: 0"></td>',i.show.orderColumn&&(l+='<td class="w2ui-grid-data w2ui-col-order" col="order"></td>');for(var o=0;o<i.columns.length;o++){var a=i.columns[o];(a.hidden||o<i.last.colStart||o>i.last.colEnd)&&!a.frozen||(s='<td class="w2ui-grid-data" '+(null!=a.attr?a.attr:"")+' col="'+o+'"></td>',a.frozen?n+=s:l+=s)}n+='<td class="w2ui-grid-data-last"></td> </tr>',l+='<td class="w2ui-grid-data-last" col="end"></td> </tr>',$("#grid_"+i.name+"_frecords > table").append(n),$("#grid_"+i.name+"_records > table").append(l)}if(0<r.length){for(var k=parseInt(r.width())-(w?w2utils.scrollBarSize():0)-(this.show.lineNumbers?f:0)-(this.show.selectColumn?26:0)-(this.show.expandColumn?26:0)-1,C=k,T=0,S=!1,y=0;y<this.columns.length;y++)0<(D=this.columns[y]).gridMinWidth&&(D.gridMinWidth>C&&!0!==D.hidden&&(S=D.hidden=!0),D.gridMinWidth<C&&!0===D.hidden&&(S=!(D.hidden=!1)));if(!0===S)return void this.refresh();for(y=0;y<this.columns.length;y++)(D=this.columns[y]).hidden||("px"==String(D.size).substr(String(D.size).length-2).toLowerCase()?(k-=parseFloat(D.size),this.columns[y].sizeCalculated=D.size,this.columns[y].sizeType="px"):(T+=parseFloat(D.size),this.columns[y].sizeType="%",delete D.sizeCorrected));if(100!=T&&0<T)for(y=0;y<this.columns.length;y++)(D=this.columns[y]).hidden||"%"==D.sizeType&&(D.sizeCorrected=Math.round(100*parseFloat(D.size)*100/T)/100+"%");for(y=0;y<this.columns.length;y++)(D=this.columns[y]).hidden||"%"==D.sizeType&&(null!=this.columns[y].sizeCorrected?this.columns[y].sizeCalculated=Math.floor(k*parseFloat(D.sizeCorrected)/100)-1+"px":this.columns[y].sizeCalculated=Math.floor(k*parseFloat(D.size)/100)-1+"px")}for(var O=0,y=0;y<this.columns.length;y++)(D=this.columns[y]).hidden||(null==D.min&&(D.min=20),parseInt(D.sizeCalculated)<parseInt(D.min)&&(D.sizeCalculated=D.min+"px"),parseInt(D.sizeCalculated)>parseInt(D.max)&&(D.sizeCalculated=D.max+"px"),O+=parseInt(D.sizeCalculated));var z=parseInt(C)-parseInt(O);if(0<z&&0<T)for(var D,y=0;;)if(null!=(D=this.columns[y]))if(D.hidden||"px"==D.sizeType)y++;else{if(D.sizeCalculated=parseInt(D.sizeCalculated)+1+"px",0===--z)break;y++}else y=0;else 0<z&&d.find("> table > tbody > tr:nth-child(1) td.w2ui-head-last").css("width",w2utils.scrollBarSize()).show();var I=1;this.show.lineNumbers&&(I+=f),this.show.selectColumn&&(I+=26),this.show.expandColumn&&(I+=26);for(y=0;y<this.columns.length;y++)this.columns[y].hidden||this.columns[y].frozen&&(I+=parseInt(this.columns[y].sizeCalculated));u.css("width",I),h.css("width",I),o.css("width",I),p.css("width",I),d.css("left",I),c.css("left",I),n.css("left",I),d.find("> table > tbody > tr:nth-child(1) td").add(u.find("> table > tbody > tr:nth-child(1) td")).each(function(e,t){$(t).hasClass("w2ui-col-number")&&$(t).css("width",f);var i=$(t).attr("col");if(null!=i){if("start"==i){for(var s=0,n=0;n<l.last.colStart;n++)!l.columns[n]||l.columns[n].frozen||l.columns[n].hidden||(s+=parseInt(l.columns[n].sizeCalculated));$(t).css("width",s+"px")}l.columns[i]&&$(t).css("width",l.columns[i].sizeCalculated)}if($(t).hasClass("w2ui-head-last"))if(l.last.colEnd+1<l.columns.length){for(s=0,n=l.last.colEnd+1;n<l.columns.length;n++)!l.columns[n]||l.columns[n].frozen||l.columns[n].hidden||(s+=parseInt(l.columns[n].sizeCalculated));$(t).css("width",s+"px")}else $(t).css("width",w2utils.scrollBarSize()+(0<z&&0===T?z:0)+"px")}),3==d.find("> table > tbody > tr").length&&d.find("> table > tbody > tr:nth-child(1) td").add(u.find("> table > tbody > tr:nth-child(1) td")).html("").css({height:"0px",border:"0px",padding:"0px",margin:"0px"}),c.find("> table > tbody > tr:nth-child(1) td").add(h.find("> table > tbody > tr:nth-child(1) td")).each(function(e,t){$(t).hasClass("w2ui-col-number")&&$(t).css("width",f);var i=$(t).attr("col");if(null!=i){if("start"==i){for(var s=0,n=0;n<l.last.colStart;n++)!l.columns[n]||l.columns[n].frozen||l.columns[n].hidden||(s+=parseInt(l.columns[n].sizeCalculated));$(t).css("width",s+"px")}l.columns[i]&&$(t).css("width",l.columns[i].sizeCalculated)}if($(t).hasClass("w2ui-grid-data-last")&&0===$(t).parents(".w2ui-grid-frecords").length)if(l.last.colEnd+1<l.columns.length){for(s=0,n=l.last.colEnd+1;n<l.columns.length;n++)!l.columns[n]||l.columns[n].frozen||l.columns[n].hidden||(s+=parseInt(l.columns[n].sizeCalculated));$(t).css("width",s+"px")}else $(t).css("width",(0<z&&0===T?z:0)+"px")}),n.find("> table > tbody > tr:nth-child(1) td").add(o.find("> table > tbody > tr:nth-child(1) td")).each(function(e,t){$(t).hasClass("w2ui-col-number")&&$(t).css("width",f);var i=$(t).attr("col");if(null!=i){if("start"==i){for(var s=0,n=0;n<l.last.colStart;n++)!l.columns[n]||l.columns[n].frozen||l.columns[n].hidden||(s+=parseInt(l.columns[n].sizeCalculated));$(t).css("width",s+"px")}l.columns[i]&&$(t).css("width",l.columns[i].sizeCalculated)}$(t).hasClass("w2ui-grid-data-last")&&0===$(t).parents(".w2ui-grid-frecords").length&&$(t).css("width",w2utils.scrollBarSize()+(0<z&&0===T?z:0)+"px")}),this.initResize(),this.refreshRanges(),(this.last.scrollTop||this.last.scrollLeft)&&0<c.length&&(d.prop("scrollLeft",this.last.scrollLeft),c.prop("scrollTop",this.last.scrollTop),c.prop("scrollLeft",this.last.scrollLeft))},getSearchesHTML:function(){for(var a=this,e='<table cellspacing="0"><tbody>',t=!1,i=0;i<this.searches.length;i++){var s=this.searches[i];if(s.type=String(s.type).toLowerCase(),!s.hidden){var n="";0==t&&(n='<button type="button" class="w2ui-btn close-btn" onclick="obj = w2ui[\''+this.name+"']; if (obj) obj.searchClose()\">X</button>",t=!0),null==s.inTag&&(s.inTag=""),null==s.outTag&&(s.outTag=""),null==s.style&&(s.style=""),null==s.type&&(s.type="text"),null==s.label&&null!=s.caption&&(console.log("NOTICE: grid search.caption property is deprecated, please use search.label. Search ->",s),s.label=s.caption);var l='<select id="grid_'+this.name+"_operator_"+i+'" class="w2ui-input"    onchange="w2ui[\''+this.name+"'].initOperator(this, "+i+')">'+function(e,t){var i="",s=a.operators[a.operatorsMap[e]];null!=t&&(s=t);for(var n=0;n<s.length;n++){var l=s[n],o=l;Array.isArray(l)?(o=l[1],l=l[0],null==o&&(o=l)):$.isPlainObject(l)&&(o=l.text,l=l.oper),i+='<option value="'+l+'">'+w2utils.lang(o)+"</option>\n"}return i}(s.type,s.operators)+"</select>";switch(e+='<tr>    <td class="close-btn">'+n+'</td>    <td class="caption">'+(s.label||"")+'</td>    <td class="operator">'+l+'</td>    <td class="value">',s.type){case"text":case"alphanumeric":case"hex":case"color":case"list":case"combo":case"enum":var o="width: 250px;";-1!=["hex","color"].indexOf(s.type)&&(o="width: 90px;"),e+='<input rel="search" type="text" id="grid_'+this.name+"_field_"+i+'" name="'+s.field+'"    class="w2ui-input" style="'+o+s.style+'" '+s.inTag+"/>";break;case"int":case"float":case"money":case"currency":case"percent":case"date":case"time":case"datetime":o="width: 90px;";e+='<input rel="search" type="text" class="w2ui-input" style="'+(o="datetime"==s.type?"width: 140px;":o)+s.style+'" id="grid_'+this.name+"_field_"+i+'" name="'+s.field+'" '+s.inTag+'/><span id="grid_'+this.name+"_range_"+i+'" style="display: none">&#160;-&#160;&#160;<input rel="search" type="text" class="w2ui-input" style="'+o+s.style+'" id="grid_'+this.name+"_field2_"+i+'" name="'+s.field+'" '+s.inTag+"/></span>";break;case"select":e+='<select rel="search" class="w2ui-input" style="'+s.style+'" id="grid_'+this.name+"_field_"+i+'"  name="'+s.field+'" '+s.inTag+"></select>"}e+=s.outTag+"    </td></tr>"}}return e+='<tr>    <td colspan="4" class="actions">        <div>        <button type="button" class="w2ui-btn" onclick="obj = w2ui[\''+this.name+"']; if (obj) { obj.searchReset(); }\">"+w2utils.lang("Reset")+'</button>        <button type="button" class="w2ui-btn w2ui-btn-blue" onclick="obj = w2ui[\''+this.name+"']; if (obj) { obj.search(); }\">"+w2utils.lang("Search")+"</button>        </div>    </td></tr></tbody></table>"},initOperator:function(e,t){var i=this.searches[t],s=$("#grid_"+this.name+"_range_"+t),n=$("#grid_"+this.name+"_field_"+t),l=n.parent().find("span input");switch(n.show(),s.hide(),$(e).val()){case"between":s.show(),l.w2field(i.type,i.options);break;case"not null":case"null":n.hide(),n.val("1"),n.change()}},initSearches:function(){for(var t=this,e=0;e<this.searches.length;e++){var i=this.searches[e],s=this.getSearchData(i.field);i.type=String(i.type).toLowerCase();var n=t.operators[t.operatorsMap[i.type]],l=(n=i.operators?i.operators:n)[0];$.isPlainObject(l)&&(l=l.oper),"object"!=typeof i.options&&(i.options={}),"text"==i.type&&(l=this.textSearch);for(var o=0;o<n.length;o++){var a=n[o];if($.isPlainObject(a)&&(a=a.oper),i.operator==a){l=i.operator;break}}switch(i.type){case"text":case"alphanumeric":$("#grid_"+this.name+"_field_"+e).w2field(i.type,i.options);break;case"int":case"float":case"hex":case"color":case"money":case"currency":case"percent":case"date":case"time":case"datetime":$("#grid_"+this.name+"_field_"+e).w2field(i.type,i.options),$("#grid_"+this.name+"_field2_"+e).w2field(i.type,i.options),setTimeout(function(){$("#grid_"+t.name+"_field_"+e).keydown(),$("#grid_"+t.name+"_field2_"+e).keydown()},1);break;case"list":case"combo":case"enum":var r=i.options;"list"==i.type&&(r.selected={}),"enum"==i.type&&(r.selected=[]),s&&(r.selected=s.value),$("#grid_"+this.name+"_field_"+e).w2field(i.type,$.extend({openOnFocus:!0},r)),s&&null!=s.text&&$("#grid_"+this.name+"_field_"+e).data("selected",{id:s.value,text:s.text});break;case"select":for(r='<option value="">--</option>',o=0;o<i.options.items.length;o++){var d,u,c=i.options.items[o];$.isPlainObject(i.options.items[o])?(d=c.id,u=c.text,r+='<option value="'+(d=null==(d=null==d&&null!=c.value?c.value:d)?"":d)+'">'+(u=null==u&&null!=c.text?c.text:u)+"</option>"):r+='<option value="'+c+'">'+c+"</option>"}$("#grid_"+this.name+"_field_"+e).html(r)}null!=s?("int"==s.type&&-1!=["in","not in"].indexOf(s.operator)&&$("#grid_"+this.name+"_field_"+e).w2field("clear").val(s.value),$("#grid_"+this.name+"_operator_"+e).val(s.operator).trigger("change"),$.isArray(s.value)?-1!=["in","not in"].indexOf(s.operator)?$("#grid_"+this.name+"_field_"+e).val(s.value).trigger("change"):($("#grid_"+this.name+"_field_"+e).val(s.value[0]).trigger("change"),$("#grid_"+this.name+"_field2_"+e).val(s.value[1]).trigger("change")):null!=s.value&&$("#grid_"+this.name+"_field_"+e).val(s.value).trigger("change")):$("#grid_"+this.name+"_operator_"+e).val(l).trigger("change")}$("#w2ui-overlay-"+this.name+"-searchOverlay .w2ui-grid-searches *[rel=search]").on("keypress",function(e){13==e.keyCode&&(t.search(),$().w2overlay({name:t.name+"-searchOverlay"}))})},getColumnsHTML:function(){var e,t,i,f=this,s="",n="";return this.show.columnHeaders&&(n=0<this.columnGroups.length?(e=l(!0),t=function(){var e="<tr>",t="<tr>",i="",s=f.columnGroups.length-1;null==f.columnGroups[s].text&&null!=f.columnGroups[s].caption&&(console.log("NOTICE: grid columnGroup.caption property is deprecated, please use columnGroup.text. Group -> ",f.columnGroups[s]),f.columnGroups[s].text=f.columnGroups[s].caption);""!=f.columnGroups[f.columnGroups.length-1].text&&f.columnGroups.push({text:""});f.show.lineNumbers&&(e+='<td class="w2ui-head w2ui-col-number">    <div style="height: '+(f.recordHeight+1)+'px">&#160;</div></td>');f.show.selectColumn&&(e+='<td class="w2ui-head w2ui-col-select">    <div style="height: 25px">&#160;</div></td>');f.show.expandColumn&&(e+='<td class="w2ui-head w2ui-col-expand">    <div style="height: 25px">&#160;</div></td>');var n=0;t+='<td id="grid_'+f.name+'_column_start" class="w2ui-head" col="start" style="border-right: 0"></td>',f.show.orderColumn&&(t+='<td class="w2ui-head w2ui-col-order" col="order">    <div style="height: 25px">&#160;</div></td>');for(var l=0;l<f.columnGroups.length;l++){var o=f.columnGroups[l],a=f.columns[n];null!=o.colspan&&(o.span=o.colspan),null!=o.span&&o.span==parseInt(o.span)||(o.span=1),null==a.text&&null!=a.caption&&(console.log("NOTICE: grid column.caption property is deprecated, please use column.text. Column ->",a),a.text=a.caption);for(var r=0,d=n;d<n+o.span;d++)f.columns[d]&&!f.columns[d].hidden&&r++;if(l==f.columnGroups.length-1&&(r=100),!(r<=0))if(!0===o.master){for(var u="",c=0;c<f.sortData.length;c++)f.sortData[c].field==a.field&&("asc"===(f.sortData[c].direction||"").toLowerCase()&&(u="w2ui-sort-up"),"desc"===(f.sortData[c].direction||"").toLowerCase()&&(u="w2ui-sort-down"));var h="";!1!==a.resizable&&(h='<div class="w2ui-resizer" name="'+n+'"></div>');var p="function"==typeof a.text?a.text(a):a.text;i='<td id="grid_'+f.name+"_column_"+n+'" class="w2ui-head '+u+'" col="'+n+'"     rowspan="2" colspan="'+r+'"     oncontextmenu = "w2ui[\''+f.name+"'].contextMenu(null, "+n+', event);"    onclick="w2ui[\''+f.name+"'].columnClick('"+a.field+"', event);\"    ondblclick=\"w2ui['"+f.name+"'].columnDblClick('"+a.field+"', event);\">"+h+'    <div class="w2ui-col-group w2ui-col-header '+(u?"w2ui-col-sorted":"")+'">        <div class="'+u+'"></div>'+(p||"&#160;")+"    </div></td>",a&&a.frozen?e+=i:t+=i}else{p="function"==typeof o.text?o.text(o):o.text;i='<td id="grid_'+f.name+"_column_"+n+'" class="w2ui-head" col="'+n+'"         colspan="'+r+'">    <div class="w2ui-col-group">'+(p||"&#160;")+"    </div></td>",a&&a.frozen?e+=i:t+=i}n+=o.span}return e+="<td></td></tr>",t+='<td id="grid_'+f.name+'_column_end" class="w2ui-head" col="end"></td></tr>',[e,t]}(),i=l(!1),s=e[0]+t[0]+i[0],e[1]+t[1]+i[1]):(s=(i=l(!0))[0],i[1])),[s,n];function l(e){var t="<tr>",i="<tr>";f.show.lineNumbers&&(t+='<td class="w2ui-head w2ui-col-number"        onclick="w2ui[\''+f.name+"'].columnClick('line-number', event);\"       ondblclick=\"w2ui['"+f.name+"'].columnDblClick('line-number', event);\">    <div>#</div></td>"),f.show.selectColumn&&(t+='<td class="w2ui-head w2ui-col-select"       onclick="if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;">    <div>        <input type="checkbox" id="grid_'+f.name+'_check_all" tabindex="-1"            style="'+(0==f.multiSelect?"display: none;":"")+'"            onmousedown="if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;"            onclick="var grid = w2ui[\''+f.name+"'];               if (this.checked) grid.selectAll(); else grid.selectNone();               if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;               clearTimeout(grid.last.kbd_timer); /* keep focus */            \"/>    </div></td>"),f.show.expandColumn&&(t+='<td class="w2ui-head w2ui-col-expand">    <div>&#160;</div></td>');var s,n=0,l=0;i+='<td id="grid_'+f.name+'_column_start" class="w2ui-head" col="start" style="border-right: 0"></td>',f.show.orderColumn&&(i+='<td class="w2ui-head w2ui-col-order" col="order">    <div>&#160;</div></td>');for(var o=0;o<f.columns.length;o++){var a,r=f.columns[o];null==r.text&&null!=r.caption&&(console.log("NOTICE: grid column.caption property is deprecated, please use column.text. Column -> ",r),r.text=r.caption),null==r.size&&(r.size="100%"),o==l&&(l+=(s=f.columnGroups[n++]||{}).span),(o<f.last.colStart||o>f.last.colEnd)&&!r.frozen||r.hidden||!0===s.master&&!e||(a=f.getColumnCellHTML(o),r&&r.frozen?t+=a:i+=a)}return t+='<td class="w2ui-head w2ui-head-last"><div>&#160;</div></td>',i+='<td class="w2ui-head w2ui-head-last" col="end"><div>&#160;</div></td>',[t+="</tr>",i+="</tr>"]}},getColumnCellHTML:function(e){var t=this.columns[e];if(null==t)return"";for(var i=!this.reorderColumns||this.columnGroups&&this.columnGroups.length?"":" w2ui-reorder-cols-head ",s="",n=0;n<this.sortData.length;n++)this.sortData[n].field==t.field&&("asc"===(this.sortData[n].direction||"").toLowerCase()&&(s="w2ui-sort-up"),"desc"===(this.sortData[n].direction||"").toLowerCase()&&(s="w2ui-sort-down"));var l,o=this.last.selection.columns,a=!1;for(l in o)for(n=0;n<o[l].length;n++)o[l][n]==e&&(a=!0);var r="function"==typeof t.text?t.text(t):t.text;return'<td id="grid_'+this.name+"_column_"+e+'" col="'+e+'" class="w2ui-head '+s+i+'" '+("normal"==this.columnTooltip&&t.tooltip?'title="'+t.tooltip+'" ':"")+"    onmouseEnter = \"w2ui['"+this.name+"'].columnTooltipShow('"+e+"', event);\"    onmouseLeave  = \"w2ui['"+this.name+"'].columnTooltipHide('"+e+"', event);\"    oncontextmenu = \"w2ui['"+this.name+"'].contextMenu(null, "+e+', event);"    onclick="w2ui[\''+this.name+"'].columnClick('"+t.field+"', event);\"    ondblclick=\"w2ui['"+this.name+"'].columnDblClick('"+t.field+"', event);\">"+(!1!==t.resizable?'<div class="w2ui-resizer" name="'+e+'"></div>':"")+'    <div class="w2ui-col-header '+(s?"w2ui-col-sorted":"")+" "+(a?"w2ui-col-selected":"")+'">        <div class="'+s+'"></div>'+(r||"&#160;")+"    </div></td>"},columnTooltipShow:function(e){var t,i,s;"normal"!=this.columnTooltip&&(t=$(this.box).find("#grid_"+this.name+"_column_"+e),i=this.columns[e],s=this.columnTooltip,t.prop("_mouse_over",!0),setTimeout(function(){!0===t.prop("_mouse_over")&&!0!==t.prop("_mouse_tooltip")&&(t.prop("_mouse_tooltip",!0),t.w2tag(i.tooltip,{position:s,top:5}))},1))},columnTooltipHide:function(e){var t;"normal"!=this.columnTooltip&&(t=$(this.box).find("#grid_"+this.name+"_column_"+e),this.columns[e],t.removeProp("_mouse_over"),setTimeout(function(){!0!==t.prop("_mouse_over")&&!0===t.prop("_mouse_tooltip")&&(t.removeProp("_mouse_tooltip"),t.w2tag())},1))},getRecordsHTML:function(){var e=this.records.length,t="object"!=typeof this.url?this.url:this.url.get;(e=0!=this.searchData.length&&!t?this.last.searchIds.length:e)>this.vs_start?this.last.show_extra=this.vs_extra:this.last.show_extra=this.vs_start;var t=$("#grid_"+this.name+"_records"),i=Math.floor((t.height()||0)/this.recordHeight)+this.last.show_extra+1;(!this.fixedBody||e<i)&&(i=e);var s=this.getRecordHTML(-1,0),n="<table><tbody>"+s[0],l="<table><tbody>"+s[1];n+='<tr id="grid_'+this.name+'_frec_top" line="top" style="height: 0px">    <td colspan="2000"></td></tr>',l+='<tr id="grid_'+this.name+'_rec_top" line="top" style="height: 0px">    <td colspan="2000"></td></tr>';for(var o=0;o<i;o++)n+=(s=this.getRecordHTML(o,o+1))[0],l+=s[1];e=(e-i)*this.recordHeight;return n+='<tr id="grid_'+this.name+'_frec_bottom" rec="bottom" line="bottom" style="height: '+e+'px; vertical-align: top">    <td colspan="2000" style="border-right: 1px solid #D6D5D7;"></td></tr><tr id="grid_'+this.name+'_frec_more" style="display: none; ">    <td colspan="2000" class="w2ui-load-more"></td></tr></tbody></table>',l+='<tr id="grid_'+this.name+'_rec_bottom" rec="bottom" line="bottom" style="height: '+e+'px; vertical-align: top">    <td colspan="2000" style="border: 0"></td></tr><tr id="grid_'+this.name+'_rec_more" style="display: none">    <td colspan="2000" class="w2ui-load-more"></td></tr></tbody></table>',this.last.range_start=0,this.last.range_end=i,[n,l]},getSummaryHTML:function(){if(0!==this.summary.length){for(var e=this.getRecordHTML(-1,0),t="<table><tbody>"+e[0],i="<table><tbody>"+e[1],s=0;s<this.summary.length;s++)t+=(e=this.getRecordHTML(s,s+1,!0))[0],i+=e[1];return[t+="</tbody></table>",i+="</tbody></table>"]}},scroll:function(e){(new Date).getTime();var n=this,t="object"!=typeof this.url?this.url:this.url.get,i=$("#grid_"+this.name+"_records"),s=$("#grid_"+this.name+"_frecords");e&&(p=e.target.scrollTop,h=e.target.scrollLeft,n.last.scrollTop=p,n.last.scrollLeft=h,$("#grid_"+n.name+"_columns")[0].scrollLeft=h,$("#grid_"+n.name+"_summary")[0].scrollLeft=h,s[0].scrollTop=p),this.last.bubbleEl&&($(this.last.bubbleEl).w2tag(),this.last.bubbleEl=null);var l=null,o=null;if(n.disableCVS||0<n.columnGroups.length)l=0,o=n.columns.length-1;else{for(var a,r=i.width(),d=0,u=0;u<n.columns.length;u++)n.columns[u].frozen||n.columns[u].hidden||(d+(a=parseInt(n.columns[u].sizeCalculated||n.columns[u].size))+30>n.last.scrollLeft&&null==l&&(l=u),d+a-30>n.last.scrollLeft+r&&null==o&&(o=u),d+=a);null==o&&(o=n.columns.length-1)}if(null!=l&&((l=l<0?0:l)==(o=o<0?0:o)&&(0<l?l--:o++),l!=n.last.colStart||o!=n.last.colEnd)){var c=$(n.box),h=Math.abs(l-n.last.colStart),p=Math.abs(o-n.last.colEnd);if(h<5&&p<5){var f=c.find(".w2ui-grid-columns #grid_"+n.name+"_column_start"),g=c.find(".w2ui-grid-columns .w2ui-head-last"),m=c.find("#grid_"+n.name+"_records .w2ui-grid-data-spacer"),w=c.find("#grid_"+n.name+"_records .w2ui-grid-data-last"),v=c.find("#grid_"+n.name+"_summary .w2ui-grid-data-spacer"),y=c.find("#grid_"+n.name+"_summary .w2ui-grid-data-last");if(l>n.last.colStart)for(u=n.last.colStart;u<l;u++)c.find("#grid_"+n.name+"_columns #grid_"+n.name+"_column_"+u).remove(),c.find("#grid_"+n.name+'_records td[col="'+u+'"]').remove(),c.find("#grid_"+n.name+'_summary td[col="'+u+'"]').remove();if(o<n.last.colEnd)for(u=n.last.colEnd;o<u;u--)c.find("#grid_"+n.name+"_columns #grid_"+n.name+"_column_"+u).remove(),c.find("#grid_"+n.name+'_records td[col="'+u+'"]').remove(),c.find("#grid_"+n.name+'_summary td[col="'+u+'"]').remove();if(l<n.last.colStart)for(u=n.last.colStart-1;l<=u;u--)n.columns[u]&&(n.columns[u].frozen||n.columns[u].hidden)||(f.after(n.getColumnCellHTML(u)),m.each(function(e,t){var i=$(t).parent().attr("index"),s='<td class="w2ui-grid-data" col="'+u+'" style="height: 0px"></td>';null!=i&&(s=n.getCellHTML(parseInt(i),u,!1)),$(t).after(s)}),v.each(function(e,t){var i=$(t).parent().attr("index"),s='<td class="w2ui-grid-data" col="'+u+'" style="height: 0px"></td>';null!=i&&(s=n.getCellHTML(parseInt(i),u,!0)),$(t).after(s)}));if(o>n.last.colEnd)for(u=n.last.colEnd+1;u<=o;u++)n.columns[u]&&(n.columns[u].frozen||n.columns[u].hidden)||(g.before(n.getColumnCellHTML(u)),w.each(function(e,t){var i=$(t).parent().attr("index"),s='<td class="w2ui-grid-data" col="'+u+'" style="height: 0px"></td>';null!=i&&(s=n.getCellHTML(parseInt(i),u,!1)),$(t).before(s)}),y.each(function(e,t){var i=$(t).parent().attr("index")||-1,i=n.getCellHTML(parseInt(i),u,!0);$(t).before(i)}));n.last.colStart=l,n.last.colEnd=o,n.resizeRecords()}else{n.last.colStart=l,n.last.colEnd=o;var h=this.getColumnsHTML(),b=this.getRecordsHTML(),x=this.getSummaryHTML(),p=c.find("#grid_"+this.name+"_columns"),_=c.find("#grid_"+this.name+"_records"),k=c.find("#grid_"+this.name+"_frecords"),C=c.find("#grid_"+this.name+"_summary");p.find("tbody").html(h[1]),k.html(b[0]),_.prepend(b[1]),null!=x&&C.html(x[1]),setTimeout(function(){_.find("> table").not("table:first-child").remove(),C[0]&&(C[0].scrollLeft=n.last.scrollLeft)},1),n.resizeRecords()}}k=this.records.length;if(k>this.total&&-1!==this.total&&(k=this.total),0!==(k=0!=this.searchData.length&&!t?this.last.searchIds.length:k)&&0!==i.length&&0!==i.height()){k>this.vs_start?this.last.show_extra=this.vs_extra:this.last.show_extra=this.vs_start;b=Math.round(i[0].scrollTop/this.recordHeight+1),x=b+(Math.round(i.height()/this.recordHeight)-1);if(k<b&&(b=k),k-1<=x&&(x=k),$("#grid_"+this.name+"_footer .w2ui-footer-right").html((n.show.statusRange?w2utils.formatNumber(this.offset+b)+"-"+w2utils.formatNumber(this.offset+x)+(-1!=this.total?" "+w2utils.lang("of")+" "+w2utils.formatNumber(this.total):""):"")+(t&&n.show.statusBuffered?" ("+w2utils.lang("buffered")+" "+w2utils.formatNumber(k)+(0<this.offset?", skip "+w2utils.formatNumber(this.offset):"")+")":"")),t||this.fixedBody&&!(-1!=this.total&&this.total<=this.vs_start)){var T=Math.floor(i[0].scrollTop/this.recordHeight)-this.last.show_extra,S=T+Math.floor(i.height()/this.recordHeight)+2*this.last.show_extra+1;T<1&&(T=1),S>this.total&&-1!=this.total&&(S=this.total);var O=i.find("#grid_"+this.name+"_rec_top"),z=i.find("#grid_"+this.name+"_rec_bottom"),D=s.find("#grid_"+this.name+"_frec_top"),I=s.find("#grid_"+this.name+"_frec_bottom");-1!=String(O.next().prop("id")).indexOf("_expanded_row")&&(O.next().remove(),D.next().remove()),this.total>S&&-1!=String(z.prev().prop("id")).indexOf("_expanded_row")&&(z.prev().remove(),I.prev().remove());x=parseInt(O.next().attr("line")),t=parseInt(z.prev().attr("line"));if(x<T||1==x||this.last.pull_refresh){if(S<=t+this.last.show_extra-2&&S!=this.total)return;for(this.last.pull_refresh=!1;;){var E=s.find("#grid_"+this.name+"_frec_top").next();if("bottom"==(R=i.find("#grid_"+this.name+"_rec_top").next()).attr("line"))break;if(!(parseInt(R.attr("line"))<T))break;E.remove(),R.remove()}"top"==(j=i.find("#grid_"+this.name+"_rec_bottom").prev().attr("line"))&&(j=T);for(u=parseInt(j)+1;u<=S;u++)this.records[u-1]&&((R=this.records[u-1].w2ui)&&!Array.isArray(R.children)&&(R.expanded=!1),F=this.getRecordHTML(u-1,u),z.before(F[1]),I.before(F[0]));A(),setTimeout(function(){n.refreshRanges()},0)}else{if(T>=x-this.last.show_extra+2&&1<T)return;for(;;){E=s.find("#grid_"+this.name+"_frec_bottom").prev();if("top"==(R=i.find("#grid_"+this.name+"_rec_bottom").prev()).attr("line"))break;if(!(parseInt(R.attr("line"))>S))break;E.remove(),R.remove()}"bottom"==(j=i.find("#grid_"+this.name+"_rec_top").next().attr("line"))&&(j=S);for(var R,F,u=parseInt(j)-1;T<=u;u--)this.records[u-1]&&((R=this.records[u-1].w2ui)&&!Array.isArray(R.children)&&(R.expanded=!1),F=this.getRecordHTML(u-1,u),O.after(F[1]),D.after(F[0]));A(),setTimeout(function(){n.refreshRanges()},0)}var x=(T-1)*n.recordHeight,j=(k-S)*this.recordHeight;j<0&&(j=0),O.css("height",x+"px"),D.css("height",x+"px"),z.css("height",j+"px"),I.css("height",j+"px"),n.last.range_start=T,n.last.range_end=S,k<Math.floor(i[0].scrollTop/this.recordHeight)+Math.floor(i.height()/this.recordHeight)+10&&!0!==this.last.pull_more&&(k<this.total-this.offset||-1==this.total&&this.last.xhr_hasMore)&&(!0===this.autoLoad&&(this.last.pull_more=!0,this.last.xhr_offset+=this.limit,this.request("get")),$("#grid_"+this.name+"_rec_more, #grid_"+this.name+"_frec_more").show().eq(1).off(".load-more").on("click.load-more",function(){$(this).find("td").html('<div><div style="width: 20px; height: 20px;" class="w2ui-spinner"></div></div>'),n.last.pull_more=!0,n.last.xhr_offset+=n.limit,n.request("get")}).find("td").html(n.autoLoad?'<div><div style="width: 20px; height: 20px;" class="w2ui-spinner"></div></div>':'<div style="padding-top: 15px">'+w2utils.lang("Load")+" "+n.limit+" "+w2utils.lang("More")+"...</div>"))}}function A(){n.markSearch&&(clearTimeout(n.last.marker_timer),n.last.marker_timer=setTimeout(function(){for(var e=[],t=0;t<n.searchData.length;t++){var i=n.searchData[t],s=n.getSearch(i.field);s&&!s.hidden&&(s=n.getColumn(i.field,!0),e.push({field:i.field,search:i.value,col:s}))}0<e.length&&e.forEach(function(e){$(n.box).find('td[col="'+e.col+'"]').not(".w2ui-head").w2marker(e.search)})},50))}},getRecordHTML:function(e,t,i){var s,n="",l="",o=this.last.selection;if(-1==e){n+='<tr line="0">',l+='<tr line="0">',this.show.lineNumbers&&(n+='<td class="w2ui-col-number" style="height: 0px;"></td>'),this.show.selectColumn&&(n+='<td class="w2ui-col-select" style="height: 0px;"></td>'),this.show.expandColumn&&(n+='<td class="w2ui-col-expand" style="height: 0px;"></td>'),l+='<td class="w2ui-grid-data w2ui-grid-data-spacer" col="start" style="height: 0px; width: 0px;"></td>',this.show.orderColumn&&(l+='<td class="w2ui-col-order" style="height: 0px;" col="order"></td>');for(var a=0;a<this.columns.length;a++){var r='<td class="w2ui-grid-data" col="'+a+'" style="height: 0px;"></td>';(p=this.columns[a]).frozen&&!p.hidden?n+=r:p.hidden||a<this.last.colStart||a>this.last.colEnd||(l+=r)}return n+='<td class="w2ui-grid-data-last" style="height: 0px"></td>',l+='<td class="w2ui-grid-data-last" col="end" style="height: 0px"></td>',[n+="</tr>",l+="</tr>"]}var d="object"!=typeof this.url?this.url:this.url.get;if(!0!==i)if(0<this.searchData.length&&!d){if(e>=this.last.searchIds.length)return"";e=this.last.searchIds[e],s=this.records[e]}else{if(e>=this.records.length)return"";s=this.records[e]}else{if(e>=this.summary.length)return"";s=this.summary[e]}if(!s)return"";null!=s.recid||null==this.recid||null!=(u=this.parseField(s,this.recid))&&(s.recid=u);w2utils.escapeId(s.recid);d=!1;-1!=o.indexes.indexOf(e)&&(d=!0);var u=s.w2ui?s.w2ui.style:"";null!=u&&"string"==typeof u||(u="");o=s.w2ui?s.w2ui.class:"";null!=o&&"string"==typeof o||(o=""),n+='<tr id="grid_'+this.name+"_frec_"+s.recid+'" recid="'+s.recid+'" line="'+t+'" index="'+e+'"  class="'+(t%2==0?"w2ui-even":"w2ui-odd")+" w2ui-record "+o+(d&&"row"==this.selectType?" w2ui-selected":"")+(s.w2ui&&!1===s.w2ui.editable?" w2ui-no-edit":"")+(s.w2ui&&!0===s.w2ui.expanded?" w2ui-expanded":"")+'"  style="height: '+this.recordHeight+"px; "+(d||""==u?u.replace("background-color","none"):u)+'" '+(""!=u?'custom_style="'+u+'"':"")+">",l+='<tr id="grid_'+this.name+"_rec_"+s.recid+'" recid="'+s.recid+'" line="'+t+'" index="'+e+'"  class="'+(t%2==0?"w2ui-even":"w2ui-odd")+" w2ui-record "+o+(d&&"row"==this.selectType?" w2ui-selected":"")+(s.w2ui&&!1===s.w2ui.editable?" w2ui-no-edit":"")+(s.w2ui&&!0===s.w2ui.expanded?" w2ui-expanded":"")+'"  style="height: '+this.recordHeight+"px; "+(d||""==u?u.replace("background-color","none"):u)+'" '+(""!=u?'custom_style="'+u+'"':"")+">",this.show.lineNumbers&&(n+='<td id="grid_'+this.name+"_cell_"+e+"_number"+(i?"_s":"")+'"    class="w2ui-col-number '+(d?" w2ui-row-selected":"")+'"'+(this.reorderRows?' style="cursor: move"':"")+">"+(!0!==i?this.getLineHTML(t,s):"")+"</td>"),this.show.selectColumn&&(s&&s.w2ui&&s.w2ui.hideCheckBox,n+='<td id="grid_'+this.name+"_cell_"+e+"_select"+(i?"_s":"")+'" class="w2ui-grid-data w2ui-col-select">'+(!0===i||s.w2ui&&!0===s.w2ui.hideCheckBox?"":'    <div>        <input class="w2ui-grid-select-check" type="checkbox" tabindex="-1" '+(d?'checked="checked"':"")+' style="pointer-events: none"/>    </div>')+"</td>"),this.show.expandColumn&&(d="",d=s.w2ui&&!0===s.w2ui.expanded?"-":"+",s.w2ui&&"none"==s.w2ui.expanded&&(d=""),s.w2ui&&"spinner"==s.w2ui.expanded&&(d='<div class="w2ui-spinner" style="width: 16px; margin: -2px 2px;"></div>'),n+='<td id="grid_'+this.name+"_cell_"+e+"_expand"+(i?"_s":"")+'" class="w2ui-grid-data w2ui-col-expand">'+(!0!==i?'    <div ondblclick="if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;"             onclick="w2ui[\''+this.name+"'].toggle(jQuery(this).parents('tr').attr('recid'));                 if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;\">        "+d+" </div>":"")+"</td>"),l+='<td class="w2ui-grid-data-spacer" col="start" style="border-right: 0"></td>',this.show.orderColumn&&(l+='<td id="grid_'+this.name+"_cell_"+e+"_order"+(i?"_s":"")+'" class="w2ui-grid-data w2ui-col-order" col="order">'+(!0!==i?'<div title="Drag to reorder">&nbsp;</div>':"")+"</td>");for(var c=0,h=0;;){var p,f,g,m=1;if(null==(p=this.columns[c]))break;if(p.hidden)c++,0<h&&h--;else if(0<h){if(c++,null==this.columns[c])break;s.w2ui.colspan[this.columns[c-1].field]=0,h--}else if(s.w2ui&&(f=s.w2ui.colspan,g=this.columns[c].field,f&&0===f[g]&&delete f[g]),!(c<this.last.colStart||c>this.last.colEnd)||p.frozen){if(s.w2ui&&"object"==typeof s.w2ui.colspan){var w=parseInt(s.w2ui.colspan[p.field])||null;if(1<w){for(var v=0,a=c;a<c+w&&!(a>=this.columns.length);a++)this.columns[a].hidden&&v++;m=w-v,h=w-1}}m=this.getCellHTML(e,c,i,m);p.frozen?n+=m:l+=m,c++}else c++}return n+='<td class="w2ui-grid-data-last"></td>',l+='<td class="w2ui-grid-data-last" col="end"></td>',[n+="</tr>",l+="</tr>"]},getLineHTML:function(e){return"<div>"+e+"</div>"},getCellHTML:function(i,s,e,t){var n=this,l=this.columns[s];if(null==l)return"";var o,a,r,d=(!0!==e?this.records:this.summary)[i],u=-1!==i?this.getCellValue(i,s,e):"",c=-1!==i?this.getCellEditable(i,s):"",h="max-height: "+parseInt(this.recordHeight)+"px;"+(l.clipboardCopy?"margin-right: 20px":""),p=!e&&d&&d.w2ui&&d.w2ui.changes&&null!=d.w2ui.changes[l.field],f="",g="",m=this.last.selection,w=!1,v="";if(-1!=m.indexes.indexOf(i)&&(w=!0),null==t&&(t=d&&d.w2ui&&d.w2ui.colspan&&d.w2ui.colspan[l.field]?d.w2ui.colspan[l.field]:1),0===s&&d&&d.w2ui&&Array.isArray(d.w2ui.children)){for(var y,b=0,x=this.get(d.w2ui.parent_recid,!0);null!=x;){if(b++,null==(y=this.records[x].w2ui)||null==y.parent_recid)break;x=this.get(y.parent_recid,!0)}if(d.w2ui.parent_recid)for(var _=0;_<b;_++)v+='<span class="w2ui-show-children w2ui-icon-empty"></span>';v+='<span class="w2ui-show-children '+(0<d.w2ui.children.length?d.w2ui.expanded?"w2ui-icon-collapse":"w2ui-icon-expand":"w2ui-icon-empty")+'"  onclick="event.stopPropagation(); w2ui[\''+this.name+"'].toggle(jQuery(this).parents('tr').attr('recid'))\"></span>"}!0===l.info&&(l.info={}),null!=l.info&&(o="w2ui-icon-info","function"==typeof l.info.icon?o=l.info.icon(d):"object"==typeof l.info.icon?o=l.info.icon[this.parseField(d,l.field)]||"":"string"==typeof l.info.icon&&(o=l.info.icon),a=l.info.style||"","function"==typeof l.info.style?a=l.info.style(d):"object"==typeof l.info.style?a=l.info.style[this.parseField(d,l.field)]||"":"string"==typeof l.info.style&&(a=l.info.style),v+='<span class="w2ui-info '+o+'" style="'+a+'" '+(null!=l.info.showOn?"on"+l.info.showOn:"onclick")+"=\"event.stopPropagation(); w2ui['"+this.name+"'].showBubble("+i+", "+s+')"'+(null!=l.info.hideOn?"on"+l.info.hideOn:"")+"=\"var grid = w2ui['"+this.name+"']; if (grid.last.bubbleEl) { $(grid.last.bubbleEl).w2tag() } grid.last.bubbleEl = null;\"></span>"),null!=l.render&&-1!==i?("function"==typeof l.render&&(null!=(a=l.render.call(this,d,i,s,u))&&"object"==typeof a?(u=$.trim(a.html||""),g=a.class||"",f=a.style||""):u=$.trim(a),(u.length<4||"<div"!=u.substr(0,4).toLowerCase())&&(u='<div style="'+h+'" title="'+k(u)+'">'+v+String(u)+"</div>")),"object"==typeof l.render&&(u='<div style="'+h+'" title="'+k(r=null==(r=l.render[u])||""===r?u:r)+'">'+v+String(r)+"</div>"),"string"==typeof l.render&&(y=[],-1==(r=l.render.toLowerCase().indexOf(":"))?(y[0]=l.render.toLowerCase(),y[1]=""):(y[0]=l.render.toLowerCase().substr(0,r),y[1]=l.render.toLowerCase().substr(r+1)),r=w2utils.formatters[y[0]],u='<div style="'+h+'" title="'+k(u="function"==typeof(r=l.options&&!1===l.options.autoFormat?null:r)?r(u,y[1],d):"")+'">'+v+String(u)+"</div>")):(c&&-1!=["checkbox","check"].indexOf(c.type)&&(c=e?-(i+1):i,h+="text-align: center;",u='<input tabindex="-1" type="checkbox" '+(u?'checked="checked"':"")+" onclick=\"    var obj = w2ui['"+this.name+"'];     obj.editChange.call(obj, this, "+c+", "+s+', event); "/>',v=""),u='<div style="'+h+'" title="'+k(u)+'">'+v+String(u)+"</div>"),null==u&&(u=""),"string"==typeof l.render&&(y=l.render.toLowerCase().split(":"),-1!=["number","int","float","money","currency","percent","size"].indexOf(y[0])&&(f+="text-align: right;")),d&&d.w2ui&&("object"==typeof d.w2ui.style&&("string"==typeof d.w2ui.style[s]&&(f+=d.w2ui.style[s]+";"),"string"==typeof d.w2ui.style[l.field]&&(f+=d.w2ui.style[l.field]+";")),"object"==typeof d.w2ui.class&&("string"==typeof d.w2ui.class[s]&&(g+=d.w2ui.class[s]+" "),"string"==typeof d.w2ui.class[l.field]&&(g+=d.w2ui.class[l.field]+" ")));h=!1;w&&-1!=$.inArray(s,m.columns[i])&&(h=!0);w="string"==typeof l.clipboardCopy?l.clipboardCopy:"Copy to clipboard",m="<span onmouseEnter=\"jQuery(this).w2tag('"+w+"', { position: 'top|bottom' })\"onclick=\"w2ui['"+this.name+"'].clipboardCopy("+i+", "+s+'); jQuery(this).w2tag(\'Copied\', { position: \'top|bottom\' }); event.stopPropagation();" onmouseLeave="jQuery(this).w2tag()" class="w2ui-clipboard-copy w2ui-icon-paste"></span>';return u='<td class="w2ui-grid-data'+(h?" w2ui-selected":"")+" "+g+(p?" w2ui-changed":"")+'"    id="grid_'+this.name+"_data_"+i+"_"+s+'" col="'+s+'"    style="'+f+(null!=l.style?l.style:"")+'" '+(null!=l.attr?l.attr:"")+(1<t?'colspan="'+t+'"':"")+">"+u+(""!=w2utils.stripTags(u)&&l.clipboardCopy&&w?m:"")+"</td>",u=-1===i&&!0===e?'<td class="w2ui-grid-data" col="'+s+'" style="height: 0px; '+f+'" '+(1<t?'colspan="'+t+'"':"")+"></td>":u;function k(e){var t="";return n.show.recordTitles&&(null!=l.title?("function"==typeof l.title&&(t=l.title.call(n,d,i,s)),"string"==typeof l.title&&(t=l.title)):t=w2utils.stripTags(String(e).replace(/"/g,"''"))),null!=t?String(t):""}},clipboardCopy:function(e,t){var i=this.records[e],e=this.columns[t],t=e?this.parseField(i,e.field):"";"function"==typeof e.clipboardCopy&&(t=e.clipboardCopy(i)),$("#grid_"+this.name+"_focus").text(t).select(),document.execCommand("copy")},showBubble:function(e,t){var i="",s=this.columns[t].info,n=this.records[e],l=$(this.box).find("#grid_"+this.name+"_data_"+e+"_"+t+" .w2ui-info");if(this.last.bubbleEl&&$(this.last.bubbleEl).w2tag(),this.last.bubbleEl=l,null==s.fields){s.fields=[];for(var o=0;o<this.columns.length;o++){var a=this.columns[o];s.fields.push(a.field+("string"==typeof a.render?":"+a.render:""))}}var r=s.fields;if("function"==typeof r&&(r=r(n,e,t)),"function"==typeof s.render)i=s.render(n,e,t);else if($.isArray(r)){for(i='<table cellpadding="0" cellspacing="0">',o=0;o<r.length;o++)""!=(u=String(r[o]).split(":"))[0]&&"-"!=u[0]&&"--"!=u[0]&&"---"!=u[0]?(c=(a=null==(a=this.getColumn(u[0]))?{field:u[0],caption:u[0]}:a)?this.parseField(n,a.field):"",1<u.length&&(w2utils.formatters[u[1]]?c=w2utils.formatters[u[1]](c,u[2]||null,n):console.log('ERROR: w2utils.formatters["'+u[1]+'"] does not exists.')),(!0===s.showEmpty||null!=c&&""!=c)&&(null!=s.maxLength&&"string"==typeof c&&c.length>s.maxLength&&(c=c.substr(0,s.maxLength)+"..."),i+="<tr><td>"+a.text+"</td><td>"+((0===c?"0":c)||"")+"</td></tr>")):i+='<tr><td colspan=2><div style="border-top: '+(""==u[0]?"0":"1")+'px solid #C1BEBE; margin: 6px 0px;"></div></td></tr>';i+="</table>"}else if($.isPlainObject(r)){for(var d in i='<table cellpadding="0" cellspacing="0">',r){var u,c,h=r[d];""!=h&&"-"!=h&&"--"!=h&&"---"!=h?(u=String(h).split(":"),c=(a=null==(a=this.getColumn(u[0]))?{field:u[0],caption:u[0]}:a)?this.parseField(n,a.field):"",1<u.length&&(w2utils.formatters[u[1]]?c=w2utils.formatters[u[1]](c,u[2]||null,n):console.log('ERROR: w2utils.formatters["'+u[1]+'"] does not exists.')),"function"==typeof h&&(c=h(n,e,t)),(!0===s.showEmpty||null!=c&&""!=c)&&(i+="<tr><td>"+d+"</td><td>"+((c=null!=s.maxLength&&"string"==typeof c&&c.length>s.maxLength?c.substr(0,s.maxLength)+"...":c)||"")+"</td></tr>")):i+='<tr><td colspan=2><div style="border-top: '+(""==h?"0":"1")+'px solid #C1BEBE; margin: 6px 0px;"></div></td></tr>'}i+="</table>"}$(l).w2tag($.extend({html:i,left:-4,position:"bottom|top",className:"w2ui-info-bubble",style:"",hideOnClick:!0},s.options||{}))},getCellEditable:function(e,t){var i=this.columns[t],s=this.records[e];if(!s||!i)return null;var n=s.w2ui?s.w2ui.editable:null;return!1===n?null:(null!=n&&!0!==n||"function"==typeof(n=i?i.editable:null)&&(i=this.getCellValue(e,t,!1),n=n.call(this,s,e,t,i)),n)},getCellValue:function(e,t,i){var t=this.columns[t],e=(!0!==i?this.records:this.summary)[e],s=this.parseField(e,t.field);return e&&e.w2ui&&e.w2ui.changes&&null!=e.w2ui.changes[t.field]&&(s=e.w2ui.changes[t.field]),$.isPlainObject(s)&&(t.options&&t.options.items?(val=t.options.items.find(function(e){return e.id==s.id}),s=val?val.text:s.id):null!=(s=null!=s.text?s.text:s).id&&(s=s.id)),s=null==s?"":s},getFooterHTML:function(){return'<div>    <div class="w2ui-footer-left"></div>    <div class="w2ui-footer-right"></div>    <div class="w2ui-footer-center"></div></div>'},status:function(e){var t,i;null!=e?$("#grid_"+this.name+"_footer").find(".w2ui-footer-left").html(e):(t="",0<(i=this.getSelection()).length&&(this.show.statusSelection&&1<i.length&&(t=String(i.length).replace(/(\d)(?=(\d\d\d)+(?!\d))/g,"$1,")+" "+w2utils.lang("selected")),this.show.statusRecordID&&1==i.length&&("object"==typeof(e=i[0])&&(e=e.recid+", "+w2utils.lang("Column")+": "+e.column),t=w2utils.lang("Record ID")+": "+e+" ")),$("#grid_"+this.name+"_footer .w2ui-footer-left").html(t),1==i.length?this.toolbar.enable("w2ui-edit"):this.toolbar.disable("w2ui-edit"),1<=i.length?this.toolbar.enable("w2ui-delete"):this.toolbar.disable("w2ui-delete"))},lock:function(e,t){var i=this,s=Array.prototype.slice.call(arguments,0);s.unshift(this.box),setTimeout(function(){$(i.box).find("#grid_"+i.name+"_empty_msg").remove(),w2utils.lock.apply(window,s)},10)},unlock:function(e){var t=this.box;setTimeout(function(){0<$(t).find(".w2ui-message").not(".w2ui-closing").length||w2utils.unlock(t,e)},25)},stateSave:function(e){var i=this;if(!w2utils.hasLocalStorage)return null;for(var s,t={columns:[],show:$.extend({},this.show),last:{search:this.last.search,multi:this.last.multi,logic:this.last.logic,label:this.last.label,field:this.last.field,scrollTop:this.last.scrollTop,scrollLeft:this.last.scrollLeft},sortData:[],searchData:[]},n=0;n<this.columns.length;n++){var l=i.columns[n],o={};Object.keys(i.stateColProps).forEach(function(e,t){i.stateColProps[e]&&(s=void 0!==l[e]?l[e]:i.stateColDefaults[e]||null,o[e]=s)}),t.columns.push(o)}for(n=0;n<this.sortData.length;n++)t.sortData.push($.extend({},this.sortData[n]));for(n=0;n<this.searchData.length;n++)t.searchData.push($.extend({},this.searchData[n]));if(!0!==e){e=this.trigger({phase:"before",type:"stateSave",target:this.name,state:t});if(!0===e.isCancelled)return void("function"==typeof callBack&&callBack({status:"error",message:"Request aborted."}));try{var a=$.parseJSON(localStorage.w2ui||"{}");(a=a||{}).states||(a.states={}),a.states[this.stateId||this.name]=t,localStorage.w2ui=JSON.stringify(a)}catch(e){return delete localStorage.w2ui,null}this.trigger($.extend(e,{phase:"after"}))}return t},stateRestore:function(e){var t=this,i="object"!=typeof this.url?this.url:this.url.get;if(!e)try{if(!w2utils.hasLocalStorage)return!1;(a=(a=$.parseJSON(localStorage.w2ui||"{}"))||{}).states||(a.states={}),e=a.states[this.stateId||this.name]}catch(e){return delete localStorage.w2ui,null}var s=this.trigger({phase:"before",type:"stateRestore",target:this.name,state:e});if(!0!==s.isCancelled){if($.isPlainObject(e)){$.extend(this.show,e.show),$.extend(this.last,e.last);for(var n=this.last.scrollTop,l=this.last.scrollLeft,o=0;o<e.columns.length;o++){var a=e.columns[o],r=this.getColumn(a.field,!0);null!==r&&($.extend(this.columns[r],a),o!==r&&this.columns.splice(o,0,this.columns.splice(r,1)[0]))}this.sortData.splice(0,this.sortData.length);for(o=0;o<e.sortData.length;o++)this.sortData.push(e.sortData[o]);this.searchData.splice(0,this.searchData.length);for(o=0;o<e.searchData.length;o++)this.searchData.push(e.searchData[o]);setTimeout(function(){i||(0<t.sortData.length&&t.localSort(),0<t.searchData.length&&t.localSearch()),t.last.scrollTop=n,t.last.scrollLeft=l,t.refresh()},1)}return this.trigger($.extend(s,{phase:"after"})),!0}"function"==typeof callBack&&callBack({status:"error",message:"Request aborted."})},stateReset:function(){if(this.stateRestore(this.last.state),w2utils.hasLocalStorage)try{var e=$.parseJSON(localStorage.w2ui||"{}");e.states&&e.states[this.stateId||this.name]&&delete e.states[this.stateId||this.name],localStorage.w2ui=JSON.stringify(e)}catch(e){return delete localStorage.w2ui,null}},parseField:function(e,t){if(this.nestedFields){var i="";try{for(var i=e,s=String(t).split("."),n=0;n<s.length;n++)i=i[s[n]]}catch(e){i=""}return i}return e?e[t]:""},prepareData:function(){for(var r=this,e=0;e<this.records.length;e++)!function e(t){for(var i=0;i<r.columns.length;i++){var s,n,l=r.columns[i];null!=t[l.field]&&"string"==typeof l.render&&(-1!=["number","int","float","money","currency","percent"].indexOf(l.render.split(":")[0])&&"number"!=typeof t[l.field]&&(t[l.field]=parseFloat(t[l.field])),-1!=["date","age"].indexOf(l.render.split(":")[0])&&(t[l.field+"_"]||(n=t[l.field],w2utils.isInt(n)&&(n=parseInt(n)),t[l.field+"_"]=new Date(n))),-1!=["time"].indexOf(l.render)&&(w2utils.isTime(t[l.field])?(s=w2utils.isTime(t[l.field],!0),(n=new Date).setHours(s.hours,s.minutes,s.seconds||0,0)):(s=t[l.field],s=null!=(s=w2utils.isInt(s)?parseInt(s):s)?new Date(s):new Date,(n=new Date).setHours(s.getHours(),s.getMinutes(),s.getSeconds(),0)),t[l.field+"_"]||(t[l.field+"_"]=n)))}if(t.w2ui&&t.w2ui.children&&!0!==t.w2ui.expanded)for(var o=0;o<t.w2ui.children.length;o++){var a=t.w2ui.children[o];e(a)}}(this.records[e])},nextCell:function(e,t,i){var s=t+1;if(s>=this.columns.length)return null;var n=this.records[e].w2ui,l=(this.columns[t],this.columns[s]),n=n&&n.colspan&&!isNaN(n.colspan[l.field])?parseInt(n.colspan[l.field]):1;if(null==l)return null;if(l&&l.hidden||0===n)return this.nextCell(e,s,i);if(i){t=this.getCellEditable(e,t);if(null==t||-1!=["checkbox","check"].indexOf(t.type))return this.nextCell(e,s,i)}return s},prevCell:function(e,t,i){var s=t-1;if(s<0)return null;var n=this.records[e].w2ui,l=this.columns[s],n=n&&n.colspan&&!isNaN(n.colspan[l.field])?parseInt(n.colspan[l.field]):1;if(null==l)return null;if(l&&l.hidden||0===n)return this.prevCell(e,s,i);if(i){t=this.getCellEditable(e,t);if(null==t||-1!=["checkbox","check"].indexOf(t.type))return this.prevCell(e,s,i)}return s},nextRow:function(e,t){var i=this.last.searchIds,s=null;if(e+1<this.records.length&&0===i.length||0<i.length&&e<i[i.length-1]){if(e++,0<i.length)for(;!(-1!=$.inArray(e,i)||e>this.records.length);)e++;var n=this.records[e].w2ui,l=this.columns[t],s=0===(n&&n.colspan&&null!=l&&!isNaN(n.colspan[l.field])?parseInt(n.colspan[l.field]):1)?this.nextRow(e,t):e}return s},prevRow:function(e,t){var i=this.last.searchIds,s=null;if(0<e&&0===i.length||0<i.length&&e>i[0]){if(e--,0<i.length)for(;!(-1!=$.inArray(e,i)||e<0);)e--;var n=this.records[e].w2ui,l=this.columns[t],s=0===(n&&n.colspan&&null!=l&&!isNaN(n.colspan[l.field])?parseInt(n.colspan[l.field]):1)?this.prevRow(e,t):e}return s},selectionSave:function(){return this.last._selection=this.getSelection(),this.last._selection},selectionRestore:function(e){var t=(new Date).getTime();this.last.selection={indexes:[],columns:{}};var i,s=this.last.selection,n=this.last._selection;if(n)for(var l=0;l<n.length;l++)$.isPlainObject(n[l])?null!=(i=this.get(n[l].recid,!0))&&(-1==s.indexes.indexOf(i)&&s.indexes.push(i),s.columns[i]||(s.columns[i]=[]),s.columns[i].push(n[l].column)):null!=(i=this.get(n[l],!0))&&s.indexes.push(i);return delete this.last._selection,!0!==e&&this.refresh(),(new Date).getTime()-t},message:function(e,t){"string"==typeof e&&(e={width:e.length<300?350:550,height:e.length<300?170:250,body:'<div class="w2ui-centered">'+e+"</div>",buttons:'<button type="button" class="w2ui-btn" onclick="w2ui[\''+this.name+"'].message()\">Ok</button>",onOpen:function(e){setTimeout(function(){$(this.box).find(".w2ui-btn").focus()},25)},onClose:function(e){"function"==typeof t&&t()}}),w2utils.message.call(this,{box:this.box,path:"w2ui."+this.name,title:".w2ui-grid-header:visible",body:".w2ui-grid-box"},e)}},$.extend(w2grid.prototype,w2utils.event),w2obj.grid=w2grid}(jQuery),function(O){function a(e){this.box=null,this.name=null,this.panels=[],this.tmp={},this.padding=1,this.resizer=4,this.style="",O.extend(!0,this,w2obj.layout,e)}var z=["top","left","main","preview","right","bottom"];O.fn.w2layout=function(e){if(!O.isPlainObject(e)){var t=w2ui[O(this).attr("name")];return t?0<arguments.length?(t[e]&&t[e].apply(t,Array.prototype.slice.call(arguments,1)),this):t:null}if(w2utils.checkName(e,"w2layout")){var i=e.panels||[],s=new a(e);O.extend(s,{handlers:[],panels:[]});for(var n=0,l=i.length;n<l;n++)s.panels[n]=O.extend(!0,{},a.prototype.panel,i[n]),(O.isPlainObject(s.panels[n].tabs)||O.isArray(s.panels[n].tabs))&&function(e,t,i){var s=e.get(t);null!=s&&null==i&&(i=s.tabs);if(null==s||null==i)return;O.isArray(i)&&(i={tabs:i});O().w2destroy(e.name+"_"+t+"_tabs"),s.tabs=O().w2tabs(O.extend({},i,{owner:e,name:e.name+"_"+t+"_tabs"})),s.show.tabs=!0}(s,i[n].type),(O.isPlainObject(s.panels[n].toolbar)||O.isArray(s.panels[n].toolbar))&&function(e,t,i){var s=e.get(t);null!=s&&null==i&&(i=s.toolbar);if(null==s||null==i)return;O.isArray(i)&&(i={items:i});O().w2destroy(e.name+"_"+t+"_toolbar"),s.toolbar=O().w2toolbar(O.extend({},i,{owner:e,name:e.name+"_"+t+"_toolbar"})),s.show.toolbar=!0}(s,i[n].type);for(var o=0;o<z.length;o++)null==s.get(z[o])&&s.panels.push(O.extend(!0,{},a.prototype.panel,{type:z[o],hidden:"main"!==z[o],size:50}));return w2ui[s.name]=s,0<O(this).length&&s.render(O(this)[0]),s}},a.prototype={onShow:null,onHide:null,onResizing:null,onResizerClick:null,onRender:null,onRefresh:null,onContent:null,onResize:null,onDestroy:null,panel:{type:null,title:"",size:100,minSize:20,maxSize:!1,hidden:!1,resizable:!1,overflow:"auto",style:"",content:"",tabs:null,toolbar:null,width:null,height:null,show:{toolbar:!1,tabs:!1},callBack:null,onRefresh:null,onShow:null,onHide:null},content:function(e,t,i){return console.log("NOTICE: layout.content method is deprecated, please use layout.html() instead"),this.html(e,t,i)},html:function(e,t,i){var s=this,n=this.get(e),l={panel:e,html:n.content,error:!1,cancelled:!1,removed:function(e){"function"==typeof e&&(n.callBack=e)}};if("function"==typeof n.callBack&&(n.callBack({panel:e,content:n.content,new_content:t,transition:i||"none"}),n.callBack=null),"css"==e)return O("#layout_"+s.name+"_panel_css").html("<style>"+t+"</style>"),l.status=!0,l;if(null==n)return console.log("ERROR: incorrect panel name. Panel name can be main, left, right, top, bottom, preview or css"),l.error=!0,l;if(null==t)return l;var o=this.trigger({phase:"before",type:"content",target:e,object:n,content:t,transition:i});if(!0===o.isCancelled)return l.cancelled=!0,l;if(t instanceof jQuery)return console.log("ERROR: You can not pass jQuery object to w2layout.content() method"),l;var a,r,d="#layout_"+this.name+"_panel_"+n.type,u=O(d+"> .w2ui-panel-content"),c=0;return 0<u.length&&(O(d).scrollTop(0),c=O(u).position().top),""===n.content?n.content=t:(n.content=t,n.hidden||null!=i&&""!==i&&((a=O(d+"> .w2ui-panel-content")).after('<div class="w2ui-panel-content new-panel" style="'+a[0].style.cssText+'"></div>'),r=O(d+"> .w2ui-panel-content.new-panel"),a.css("top",c),r.css("top",c),"object"==typeof t?(t.box=r[0],t.render()):r.html(t),w2utils.transition(a[0],r[0],i,function(){a.remove(),r.removeClass("new-panel"),r.css("overflow",n.overflow),O(d+"> .w2ui-panel-content").slice(1).remove(),s.resize(),-1!=window.navigator.userAgent.indexOf("MSIE")&&setTimeout(function(){s.resize()},100)}))),this.refresh(e),s.trigger(O.extend(o,{phase:"after"})),s.resize(),-1!=window.navigator.userAgent.indexOf("MSIE")&&setTimeout(function(){s.resize()},100),l},message:function(e,t){var i=this;"string"==typeof t&&(t={width:t.length<300?350:550,height:t.length<300?170:250,body:'<div class="w2ui-centered">'+t+"</div>",buttons:'<button class="w2ui-btn" onclick="w2ui[\''+this.name+"'].message('"+e+"')\">Ok</button>",onOpen:function(e){setTimeout(function(){O(this.box).find(".w2ui-btn").focus()},25)}});var s,n=this.get(e),l=O("#layout_"+this.name+"_panel_"+n.type).css("overflow");t&&(t.onClose&&(s=t.onClose),t.onClose=function(e){"function"==typeof s&&s(e),e.done(function(){O("#layout_"+i.name+"_panel_"+n.type).css("overflow",l)})}),O("#layout_"+this.name+"_panel_"+n.type).css("overflow","hidden"),w2utils.message.call(this,{box:O("#layout_"+this.name+"_panel_"+n.type),param:e,path:"w2ui."+this.name,title:".w2ui-panel-title:visible",body:".w2ui-panel-content"},t)},load:function(s,e,n,l){var o=this;return"css"==s?(O.get(e,function(e,t,i){o.html(s,i.responseText),l&&l()}),!0):null!=this.get(s)&&(O.get(e,function(e,t,i){o.html(s,i.responseText,n),l&&l(),o.resize(),-1!=window.navigator.userAgent.indexOf("MSIE")&&setTimeout(function(){o.resize()},100)}),!0)},sizeTo:function(e,t,i){var s=this;return null!=s.get(e)&&(O(s.box).find(" > div > .w2ui-panel").css(w2utils.cssPrefix("transition",!0!==i?".2s":"0s")),setTimeout(function(){s.set(e,{size:t})},1),setTimeout(function(){O(s.box).find(" > div > .w2ui-panel").css(w2utils.cssPrefix("transition","0s")),s.resize()},500),!0)},show:function(e,t){var i=this,s=this.trigger({phase:"before",type:"show",target:e,object:this.get(e),immediate:t});if(!0!==s.isCancelled){var n=i.get(e);return null==n?!1:(!(n.hidden=!1)===t?(O("#layout_"+i.name+"_panel_"+e).css({opacity:"1"}),i.trigger(O.extend(s,{phase:"after"})),i.resize()):(O("#layout_"+i.name+"_panel_"+e).css({opacity:"0"}),O(i.box).find(" > div > .w2ui-panel").css(w2utils.cssPrefix("transition",".2s")),setTimeout(function(){i.resize()},1),setTimeout(function(){O("#layout_"+i.name+"_panel_"+e).css({opacity:"1"})},250),setTimeout(function(){O(i.box).find(" > div > .w2ui-panel").css(w2utils.cssPrefix("transition","0s")),i.trigger(O.extend(s,{phase:"after"})),i.resize()},500)),!0)}},hide:function(e,t){var i=this,s=this.trigger({phase:"before",type:"hide",target:e,object:this.get(e),immediate:t});if(!0!==s.isCancelled){var n=i.get(e);return null==n?!1:((n.hidden=!0)===t?(O("#layout_"+i.name+"_panel_"+e).css({opacity:"0"}),i.trigger(O.extend(s,{phase:"after"})),i.resize()):(O(i.box).find(" > div > .w2ui-panel").css(w2utils.cssPrefix("transition",".2s")),O("#layout_"+i.name+"_panel_"+e).css({opacity:"0"}),setTimeout(function(){i.resize()},1),setTimeout(function(){O(i.box).find(" > div > .w2ui-panel").css(w2utils.cssPrefix("transition","0s")),i.trigger(O.extend(s,{phase:"after"})),i.resize()},500)),!0)}},toggle:function(e,t){var i=this.get(e);return null!=i&&(i.hidden?this.show(e,t):this.hide(e,t))},set:function(e,t){var i=this.get(e,!0);return null!=i&&(O.extend(this.panels[i],t),null==t.content&&null==t.resizable||this.refresh(e),this.resize(),!0)},get:function(e,t){for(var i=0;i<this.panels.length;i++)if(this.panels[i].type==e)return!0===t?i:this.panels[i];return null},el:function(e){e=O("#layout_"+this.name+"_panel_"+e+"> .w2ui-panel-content");return 1!=e.length?null:e[0]},hideToolbar:function(e){var t=this.get(e);t&&(t.show.toolbar=!1,O("#layout_"+this.name+"_panel_"+e+"> .w2ui-panel-toolbar").hide(),this.resize())},showToolbar:function(e){var t=this.get(e);t&&(t.show.toolbar=!0,O("#layout_"+this.name+"_panel_"+e+"> .w2ui-panel-toolbar").show(),this.resize())},toggleToolbar:function(e){var t=this.get(e);t&&(t.show.toolbar?this.hideToolbar(e):this.showToolbar(e))},assignToolbar:function(e,t){"string"==typeof t&&null!=w2ui[t]&&(t=w2ui[t]);var i=this.get(e);i.toolbar=t;var s=O(this.box).find(e+"> .w2ui-panel-toolbar");null!=i.toolbar?(0===s.find("[name="+i.toolbar.name+"]").length?s.w2render(i.toolbar):null!=i.toolbar&&i.toolbar.refresh(),(t.owner=this).showToolbar(e),this.refresh(e)):(s.html(""),this.hideToolbar(e))},hideTabs:function(e){var t=this.get(e);t&&(t.show.tabs=!1,O("#layout_"+this.name+"_panel_"+e+"> .w2ui-panel-tabs").hide(),this.resize())},showTabs:function(e){var t=this.get(e);t&&(t.show.tabs=!0,O("#layout_"+this.name+"_panel_"+e+"> .w2ui-panel-tabs").show(),this.resize())},toggleTabs:function(e){var t=this.get(e);t&&(t.show.tabs?this.hideTabs(e):this.showTabs(e))},render:function(e){var u=this,t=(new Date).getTime(),i=u.trigger({phase:"before",type:"render",target:u.name,box:e});if(!0!==i.isCancelled){if(null!=e&&(0<O(u.box).find("#layout_"+u.name+"_panel_main").length&&O(u.box).removeAttr("name").removeClass("w2ui-layout").html(""),u.box=e),!u.box)return!1;O(u.box).attr("name",u.name).addClass("w2ui-layout").html("<div></div>"),0<O(u.box).length&&(O(u.box)[0].style.cssText+=u.style);for(var s=0;s<z.length;s++){u.get(z[s]);var n='<div id="layout_'+u.name+"_panel_"+z[s]+'" class="w2ui-panel">    <div class="w2ui-panel-title"></div>    <div class="w2ui-panel-tabs"></div>    <div class="w2ui-panel-toolbar"></div>    <div class="w2ui-panel-content"></div></div><div id="layout_'+u.name+"_resizer_"+z[s]+'" class="w2ui-resizer"></div>';O(u.box).find(" > div").append(n)}return O(u.box).find(" > div").append('<div id="layout_'+u.name+'_panel_css" style="position: absolute; top: 10000px;"></div>'),u.refresh(),u.trigger(O.extend(i,{phase:"after"})),setTimeout(function(){u.tmp.events={resize:function(e){null==w2ui[u.name]?O(window).off("resize.w2ui-"+u.name):w2ui[u.name].resize()},resizeStart:l,mouseMove:a,mouseUp:o},O(window).on("resize.w2ui-"+u.name,u.tmp.events.resize),u.resize()},0),(new Date).getTime()-t}function l(e,t){if(u.box){t=t||window.event,O(document).off("mousemove",u.tmp.events.mouseMove).on("mousemove",u.tmp.events.mouseMove),O(document).off("mouseup",u.tmp.events.mouseUp).on("mouseup",u.tmp.events.mouseUp),u.tmp.resize={type:e,x:t.screenX,y:t.screenY,diff_x:0,diff_y:0,value:0};for(var i=0;i<z.length;i++){var s=O(u.el(z[i])).parent().find(".w2ui-lock");0<s.length?s.attr("locked","previous"):u.lock(z[i],{opacity:0})}"left"!=e&&"right"!=e||(u.tmp.resize.value=parseInt(O("#layout_"+u.name+"_resizer_"+e)[0].style.left)),"top"!=e&&"preview"!=e&&"bottom"!=e||(u.tmp.resize.value=parseInt(O("#layout_"+u.name+"_resizer_"+e)[0].style.top))}}function o(e){if(u.box&&(e=e||window.event,O(document).off("mousemove",u.tmp.events.mouseMove),O(document).off("mouseup",u.tmp.events.mouseUp),null!=u.tmp.resize)){for(var t=0;t<z.length;t++){var i=O(u.el(z[t])).parent().find(".w2ui-lock");"previous"==i.attr("locked")?i.removeAttr("locked"):u.unlock(z[t])}if(0!==u.tmp.diff_x||0!==u.tmp.resize.diff_y){var s,n,l=u.get("top"),o=u.get("bottom"),a=u.get(u.tmp.resize.type),r=parseInt(O(u.box).height()),d=parseInt(O(u.box).width()),e=String(a.size);switch(u.tmp.resize.type){case"top":s=parseInt(a.sizeCalculated)+u.tmp.resize.diff_y,n=0;break;case"bottom":s=parseInt(a.sizeCalculated)-u.tmp.resize.diff_y,n=0;break;case"preview":s=parseInt(a.sizeCalculated)-u.tmp.resize.diff_y,n=(l&&!l.hidden?l.sizeCalculated:0)+(o&&!o.hidden?o.sizeCalculated:0);break;case"left":s=parseInt(a.sizeCalculated)+u.tmp.resize.diff_x,n=0;break;case"right":s=parseInt(a.sizeCalculated)-u.tmp.resize.diff_x,n=0}"%"==e.substr(e.length-1)?a.size=Math.floor(100*s/("left"==a.type||"right"==a.type?d:r-n)*100)/100+"%":"-"==String(a.size).substr(0,1)?a.size=parseInt(a.size)-a.sizeCalculated+s:a.size=s,u.resize()}O("#layout_"+u.name+"_resizer_"+u.tmp.resize.type).removeClass("active"),delete u.tmp.resize}}function a(e){if(u.box&&(e=e||window.event,null!=u.tmp.resize)){var t=u.get(u.tmp.resize.type),i=u.tmp.resize,s=u.trigger({phase:"before",type:"resizing",target:u.name,object:t,originalEvent:e,panel:i?i.type:"all",diff_x:i?i.diff_x:0,diff_y:i?i.diff_y:0});if(!0!==s.isCancelled){var n=O("#layout_"+u.name+"_resizer_"+i.type),l=e.screenX-i.x,o=e.screenY-i.y,a=u.get("main");switch(n.hasClass("active")||n.addClass("active"),i.type){case"left":t.minSize-l>t.width&&(l=t.minSize-t.width),t.maxSize&&t.width+l>t.maxSize&&(l=t.maxSize-t.width),a.minSize+l>a.width&&(l=a.width-a.minSize);break;case"right":t.minSize+l>t.width&&(l=t.width-t.minSize),t.maxSize&&t.width-l>t.maxSize&&(l=t.width-t.maxSize),a.minSize-l>a.width&&(l=a.minSize-a.width);break;case"top":t.minSize-o>t.height&&(o=t.minSize-t.height),t.maxSize&&t.height+o>t.maxSize&&(o=t.maxSize-t.height),a.minSize+o>a.height&&(o=a.height-a.minSize);break;case"preview":case"bottom":t.minSize+o>t.height&&(o=t.height-t.minSize),t.maxSize&&t.height-o>t.maxSize&&(o=t.height-t.maxSize),a.minSize-o>a.height&&(o=a.minSize-a.height)}switch(i.diff_x=l,i.diff_y=o,i.type){case"top":case"preview":case"bottom":(i.diff_x=0)<n.length&&(n[0].style.top=i.value+i.diff_y+"px");break;case"left":case"right":(i.diff_y=0)<n.length&&(n[0].style.left=i.value+i.diff_x+"px")}u.trigger(O.extend(s,{phase:"after"}))}}}},refresh:function(e){var t=this;null==e&&(e=null);var i=(new Date).getTime(),s=t.trigger({phase:"before",type:"refresh",target:null!=e?e:t.name,object:t.get(e)});if(!0!==s.isCancelled){if("string"==typeof e){var n=t.get(e);if(null==n)return;var l="#layout_"+t.name+"_panel_"+n.type,e="#layout_"+t.name+"_resizer_"+n.type;O(l).css({display:n.hidden?"none":"block"}),n.resizable?O(e).show():O(e).hide(),"object"==typeof n.content&&"function"==typeof n.content.render?(n.content.box=O(l+"> .w2ui-panel-content")[0],setTimeout(function(){0<O(l+"> .w2ui-panel-content").length&&(O(l+"> .w2ui-panel-content").removeClass().removeAttr("name").addClass("w2ui-panel-content").css("overflow",n.overflow)[0].style.cssText+=";"+n.style),n.content&&"function"==typeof n.content.render&&n.content.render()},1)):0<O(l+"> .w2ui-panel-content").length&&(O(l+"> .w2ui-panel-content").removeClass().removeAttr("name").addClass("w2ui-panel-content").html(n.content).css("overflow",n.overflow)[0].style.cssText+=";"+n.style);e=O(t.box).find(l+"> .w2ui-panel-tabs");n.show.tabs?0===e.find("[name="+n.tabs.name+"]").length&&null!=n.tabs?e.w2render(n.tabs):n.tabs.refresh():e.html("").removeClass("w2ui-tabs").hide(),e=O(t.box).find(l+"> .w2ui-panel-toolbar"),n.show.toolbar?0===e.find("[name="+n.toolbar.name+"]").length&&null!=n.toolbar?e.w2render(n.toolbar):n.toolbar.refresh():e.html("").removeClass("w2ui-toolbar").hide(),e=O(t.box).find(l+"> .w2ui-panel-title"),n.title?e.html(n.title).show():e.html("").hide()}else{if(0===O("#layout_"+t.name+"_panel_main").length)return void t.render();t.resize();for(var o=0;o<this.panels.length;o++)t.refresh(this.panels[o].type)}return t.trigger(O.extend(s,{phase:"after"})),(new Date).getTime()-i}},resize:function(){if(!this.box)return!1;var e=(new Date).getTime(),t=this.tmp.resize,i=this.trigger({phase:"before",type:"resize",target:this.name,panel:t?t.type:"all",diff_x:t?t.diff_x:0,diff_y:t?t.diff_y:0});if(!0!==i.isCancelled){this.padding<0&&(this.padding=0);var s=parseInt(O(this.box).width()),n=parseInt(O(this.box).height());O(this.box).find(" > div").css({width:s+"px",height:n+"px"});for(var l,o,a,r,d,u,c=this,h=this.get("main"),p=this.get("preview"),f=this.get("left"),g=this.get("right"),m=this.get("top"),w=this.get("bottom"),v=null!=p&&!0!==p.hidden,y=null!=f&&!0!==f.hidden,b=null!=g&&!0!==g.hidden,x=null!=m&&!0!==m.hidden,_=null!=w&&!0!==w.hidden,k=0;k<z.length;k++)"main"!==z[k]&&(t=this.get(z[k]))&&("%"==(u=String(t.size||0)).substr(u.length-1)?(u=n,"preview"==t.type&&(u=u-(m&&!m.hidden?m.sizeCalculated:0)-(w&&!w.hidden?w.sizeCalculated:0)),t.sizeCalculated=parseInt(("left"==t.type||"right"==t.type?s:u)*parseFloat(t.size)/100)):t.sizeCalculated=parseInt(t.size),t.sizeCalculated=Math.max(t.sizeCalculated,parseInt(t.minSize)));"-"==String(g.size).substr(0,1)&&(y&&"-"==String(f.size).substr(0,1)?console.log("ERROR: you cannot have both left panel.size and right panel.size be negative."):g.sizeCalculated=s-(y?f.sizeCalculated:0)+parseInt(g.size)),"-"==String(f.size).substr(0,1)&&(b&&"-"==g.size.substr(0,1)?console.log("ERROR: you cannot have both left panel.size and right panel.size be negative."):f.sizeCalculated=s-(b?g.sizeCalculated:0)+parseInt(f.size)),null!=m&&!0!==m.hidden?(o=l=0,a=s,r=m.sizeCalculated,O("#layout_"+this.name+"_panel_top").css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px"}).show(),m.width=a,m.height=r,m.resizable&&(o=m.sizeCalculated-(0===this.padding?this.resizer:0),r=this.resizer>this.padding?this.resizer:this.padding,O("#layout_"+this.name+"_resizer_top").show().css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px",cursor:"ns-resize"}).off("mousedown").on("mousedown",function(e){var t=c.trigger({phase:"before",type:"resizerClick",target:"top",originalEvent:e});if(!0!==t.isCancelled)return w2ui[c.name].tmp.events.resizeStart("top",e),c.trigger(O.extend(t,{phase:"after"})),!1}))):(O("#layout_"+this.name+"_panel_top").hide(),O("#layout_"+this.name+"_resizer_top").hide()),null!=f&&!0!==f.hidden?(o=(l=0)+(x?m.sizeCalculated+this.padding:0),a=f.sizeCalculated,r=n-(x?m.sizeCalculated+this.padding:0)-(_?w.sizeCalculated+this.padding:0),d=O("#layout_"+this.name+"_panel_left"),-1!=window.navigator.userAgent.indexOf("MSIE")&&0<d.length&&d[0].clientHeight<d[0].scrollHeight&&(a+=17),d.css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px"}).show(),f.width=a,f.height=r,f.resizable&&(l=f.sizeCalculated-(0===this.padding?this.resizer:0),a=this.resizer>this.padding?this.resizer:this.padding,O("#layout_"+this.name+"_resizer_left").show().css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px",cursor:"ew-resize"}).off("mousedown").on("mousedown",function(e){var t=c.trigger({phase:"before",type:"resizerClick",target:"left",originalEvent:e});if(!0!==t.isCancelled)return w2ui[c.name].tmp.events.resizeStart("left",e),c.trigger(O.extend(t,{phase:"after"})),!1}))):(O("#layout_"+this.name+"_panel_left").hide(),O("#layout_"+this.name+"_resizer_left").hide()),null!=g&&!0!==g.hidden?(l=s-g.sizeCalculated,o=0+(x?m.sizeCalculated+this.padding:0),a=g.sizeCalculated,r=n-(x?m.sizeCalculated+this.padding:0)-(_?w.sizeCalculated+this.padding:0),O("#layout_"+this.name+"_panel_right").css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px"}).show(),g.width=a,g.height=r,g.resizable&&(l-=this.padding,a=this.resizer>this.padding?this.resizer:this.padding,O("#layout_"+this.name+"_resizer_right").show().css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px",cursor:"ew-resize"}).off("mousedown").on("mousedown",function(e){var t=c.trigger({phase:"before",type:"resizerClick",target:"right",originalEvent:e});if(!0!==t.isCancelled)return w2ui[c.name].tmp.events.resizeStart("right",e),c.trigger(O.extend(t,{phase:"after"})),!1}))):(O("#layout_"+this.name+"_panel_right").hide(),O("#layout_"+this.name+"_resizer_right").hide()),null!=w&&!0!==w.hidden?(l=0,o=n-w.sizeCalculated,a=s,r=w.sizeCalculated,O("#layout_"+this.name+"_panel_bottom").css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px"}).show(),w.width=a,w.height=r,w.resizable&&(o-=0===this.padding?0:this.padding,r=this.resizer>this.padding?this.resizer:this.padding,O("#layout_"+this.name+"_resizer_bottom").show().css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px",cursor:"ns-resize"}).off("mousedown").on("mousedown",function(e){var t=c.trigger({phase:"before",type:"resizerClick",target:"bottom",originalEvent:e});if(!0!==t.isCancelled)return w2ui[c.name].tmp.events.resizeStart("bottom",e),c.trigger(O.extend(t,{phase:"after"})),!1}))):(O("#layout_"+this.name+"_panel_bottom").hide(),O("#layout_"+this.name+"_resizer_bottom").hide()),l=0+(y?f.sizeCalculated+this.padding:0),o=0+(x?m.sizeCalculated+this.padding:0),a=s-(y?f.sizeCalculated+this.padding:0)-(b?g.sizeCalculated+this.padding:0),r=n-(x?m.sizeCalculated+this.padding:0)-(_?w.sizeCalculated+this.padding:0)-(v?p.sizeCalculated+this.padding:0),d=O("#layout_"+this.name+"_panel_main"),-1!=window.navigator.userAgent.indexOf("MSIE")&&0<d.length&&d[0].clientHeight<d[0].scrollHeight&&(a+=17),d.css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px"}),h.width=a,h.height=r,null!=p&&!0!==p.hidden?(l=0+(y?f.sizeCalculated+this.padding:0),o=n-(_?w.sizeCalculated+this.padding:0)-p.sizeCalculated,a=s-(y?f.sizeCalculated+this.padding:0)-(b?g.sizeCalculated+this.padding:0),r=p.sizeCalculated,d=O("#layout_"+this.name+"_panel_preview"),-1!=window.navigator.userAgent.indexOf("MSIE")&&0<d.length&&d[0].clientHeight<d[0].scrollHeight&&(a+=17),d.css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px"}).show(),p.width=a,p.height=r,p.resizable&&(o-=0===this.padding?0:this.padding,r=this.resizer>this.padding?this.resizer:this.padding,O("#layout_"+this.name+"_resizer_preview").show().css({display:"block",left:l+"px",top:o+"px",width:a+"px",height:r+"px",cursor:"ns-resize"}).off("mousedown").on("mousedown",function(e){var t=c.trigger({phase:"before",type:"resizerClick",target:"preview",originalEvent:e});if(!0!==t.isCancelled)return w2ui[c.name].tmp.events.resizeStart("preview",e),c.trigger(O.extend(t,{phase:"after"})),!1}))):(O("#layout_"+this.name+"_panel_preview").hide(),O("#layout_"+this.name+"_resizer_preview").hide());for(var C=0;C<z.length;C++){var T=this.get(z[C]),S="#layout_"+this.name+"_panel_"+z[C]+" > .w2ui-panel-",$=0;T&&(T.title&&($+=w2utils.getSize(O(S+"title").css({top:$+"px",display:"block"}),"height")),T.show.tabs&&(null!=T.tabs&&w2ui[this.name+"_"+z[C]+"_tabs"]&&w2ui[this.name+"_"+z[C]+"_tabs"].resize(),$+=w2utils.getSize(O(S+"tabs").css({top:$+"px",display:"block"}),"height")),T.show.toolbar&&(null!=T.toolbar&&w2ui[this.name+"_"+z[C]+"_toolbar"]&&w2ui[this.name+"_"+z[C]+"_toolbar"].resize(),$+=w2utils.getSize(O(S+"toolbar").css({top:$+"px",display:"block"}),"height"))),O(S+"content").css({display:"block"}).css({top:$+"px"})}return clearTimeout(this._resize_timer),this._resize_timer=setTimeout(function(){for(var e in w2ui){var t;"function"==typeof w2ui[e].resize&&(null==w2ui[e].panels&&w2ui[e].resize(),0<(t=O(w2ui[e].box).parents(".w2ui-layout")).length&&t.attr("name")==c.name&&w2ui[e].resize())}},100),this.trigger(O.extend(i,{phase:"after"})),(new Date).getTime()-e}},destroy:function(){var e=this.trigger({phase:"before",type:"destroy",target:this.name});if(!0!==e.isCancelled)return null!=w2ui[this.name]&&(0<O(this.box).find("#layout_"+this.name+"_panel_main").length&&O(this.box).removeAttr("name").removeClass("w2ui-layout").html(""),delete w2ui[this.name],this.trigger(O.extend(e,{phase:"after"})),this.tmp.events&&this.tmp.events.resize&&O(window).off("resize",this.tmp.events.resize),!0)},lock:function(e,t,i){var s;-1!=z.indexOf(e)?((s=Array.prototype.slice.call(arguments,0))[0]="#layout_"+this.name+"_panel_"+e,w2utils.lock.apply(window,s)):console.log("ERROR: First parameter needs to be the a valid panel name.")},unlock:function(e,t){-1!=z.indexOf(e)?(e="#layout_"+this.name+"_panel_"+e,w2utils.unlock(e,t)):console.log("ERROR: First parameter needs to be the a valid panel name.")}},O.extend(a.prototype,w2utils.event),w2obj.layout=a}(jQuery);var w2popup={};!function(c){w2popup={defaults:{title:"",body:"",buttons:"",style:"",color:"#000",opacity:.4,speed:.3,modal:!(c.fn.w2popup=function(e,t){null==e&&(t={},e="open"),c.isPlainObject(e)&&(t=e,e="open"),"load"===(e=e.toLowerCase())&&"string"==typeof t&&(t=c.extend({url:t},2<arguments.length?arguments[2]:{})),"open"===e&&null!=t.url&&(e="load"),t=t||{};var i,s={};return 0<c(this).length&&"open"==e&&(0<c(this).find("div[rel=title], div[rel=body], div[rel=buttons]").length?(0<c("#w2ui-popup").length&&(i=c("#w2ui-popup").data("options"),w2popup._prev={template:w2popup._template,title:i.title,body:i.body,buttons:i.buttons}),w2popup._template=this,0<c(this).find("div[rel=title]").length&&(s.title=c(this).find("div[rel=title]")),0<c(this).find("div[rel=body]").length&&(s.body=c(this).find("div[rel=body]"),s.style=c(this).find("div[rel=body]")[0].style.cssText),0<c(this).find("div[rel=buttons]").length&&(s.buttons=c(this).find("div[rel=buttons]"))):(s.title="&#160;",s.body=c(this).html()),0!==parseInt(c(this).css("width"))&&(s.width=parseInt(c(this).css("width"))),i=t.title||t.showClose||void 0===t.showClose||t.showMax||void 0===t.showMax,0!==parseInt(c(this).css("height"))&&(s.height=parseInt(c(this).css("height"))+(i?32:0))),w2popup[e](c.extend({},s,t))}),maximized:!1,keyboard:!0,width:500,height:300,showClose:!0,showMax:!1,transition:null,multiple:!1},status:"closed",handlers:[],onOpen:null,onClose:null,onMax:null,onMin:null,onToggle:null,onKeydown:null,open:function(i){var t=this,e=c.extend(!0,{},i);if("closing"!=w2popup.status){var s=c("#w2ui-popup").data("options"),i=c.extend({},this.defaults,s,{title:"",body:"",buttons:""},i,{maximized:!1});setTimeout(function(){c("#w2ui-popup").data("options",i)},100),0===c("#w2ui-popup").length&&(w2popup.onMax=null,w2popup.onMin=null,w2popup.onToggle=null,w2popup.onOpen=null,w2popup.onClose=null,w2popup.onKeydown=null,w2popup.onAction=null),i.onOpen&&(w2popup.onOpen=i.onOpen),i.onClose&&(w2popup.onClose=i.onClose),i.onMax&&(w2popup.onMax=i.onMax),i.onMin&&(w2popup.onMin=i.onMin),i.onToggle&&(w2popup.onToggle=i.onToggle),i.onKeydown&&(w2popup.onKeydown=i.onKeydown),i.onAction&&(w2popup.onAction=i.onAction),i.width=parseInt(i.width),i.height=parseInt(i.height),null==window.innerHeight?(a=parseInt(document.documentElement.offsetWidth),o=parseInt(document.documentElement.offsetHeight),"IE7"===w2utils.engine&&(a+=21,o+=4)):(a=parseInt(window.innerWidth),o=parseInt(window.innerHeight)),a-10<i.width&&(i.width=a-10),o-10<i.height&&(i.height=o-10);var n,l=(o-i.height)/2*.6,o=(a-i.width)/2;if(null!=i.actions&&(i.buttons="",Object.keys(i.actions).forEach(function(e){var t=i.actions[e];"function"==typeof t&&(i.buttons+='<button class="w2ui-btn" onclick="w2popup.action(\''+e+"')\">"+e+"</button>"),"object"==typeof t&&(i.buttons+='<button class="w2ui-btn '+(t.class||"")+'" style="'+(t.style||"")+'"onclick="w2popup.action(\''+e+"')\">"+(t.text||e)+"</button>"),"string"==typeof t&&(i.buttons+=t)})),0===c("#w2ui-popup").length){if(!0===(n=this.trigger({phase:"before",type:"open",target:"popup",options:i,present:!1})).isCancelled)return;w2popup.status="opening",w2popup.lockScreen(i);var a="";i.showClose&&(a+='<div class="w2ui-popup-button w2ui-popup-close" onmousedown="event.stopPropagation()" onclick="w2popup.close()">Close</div>'),i.showMax&&(a+='<div class="w2ui-popup-button w2ui-popup-max" onmousedown="event.stopPropagation()" onclick="w2popup.toggle()">Max</div>');o='<div id="w2ui-popup" class="w2ui-popup w2ui-popup-opening" style="left: '+o+"px; top: "+l+"px;     width: "+parseInt(i.width)+"px; height: "+parseInt(i.height)+'px;"></div>';c("body").append(o);l=c("#w2ui-popup");0<l.find("div[rel=title], div[rel=body], div[rel=buttons]").length&&(0<(u=l.find("div[rel=title]")).length&&(i.title=u.html(),u.remove()),0<(u=l.find("div[rel=buttons]")).length&&(i.buttons=u.html(),u.remove()),0<(u=l.find("div[rel=body]")).length?i.body=u.html():i.body=l.html());o='<div class="w2ui-popup-title" style="'+(i.title?"":"display: none")+'">'+a+'</div><div class="w2ui-box" style="'+(i.title?"":"top: 0px !important;")+(i.buttons?"":"bottom: 0px !important;")+'">    <div class="w2ui-popup-body'+(i.title?"":" w2ui-popup-no-title")+(i.buttons?"":" w2ui-popup-no-buttons")+'" style="'+i.style+'">    </div></div><div class="w2ui-popup-buttons" style="'+(i.buttons?"":"display: none")+'"></div><input class="w2ui-popup-hidden" style="position: absolute; top: -100px"/>';c("#w2ui-popup").html(o),i.title&&c("#w2ui-popup .w2ui-popup-title").append(i.title),i.buttons&&c("#w2ui-popup .w2ui-popup-buttons").append(i.buttons),i.body&&c("#w2ui-popup .w2ui-popup-body").append(i.body),setTimeout(function(){c("#w2ui-popup").css(w2utils.cssPrefix({transition:i.speed+"s opacity, "+i.speed+"s -webkit-transform"})).removeClass("w2ui-popup-opening"),t.focus()},1),setTimeout(function(){c("#w2ui-popup").css(w2utils.cssPrefix("transform",""))},1e3*i.speed),w2popup.status="open",t.trigger(c.extend(n,{phase:"after"}))}else if(!0===i.multiple)w2popup.message(e);else{if(null==w2popup._prev&&null!=w2popup._template&&t.restoreTemplate(),!0===(n=this.trigger({phase:"before",type:"open",target:"popup",options:i,present:!0})).isCancelled)return;w2popup.status="opening",null!=s&&(s.maximized||s.width==i.width&&s.height==i.height||w2popup.resize(i.width,i.height),i.prevSize=i.width+"px:"+i.height+"px",i.maximized=s.maximized);s=c("#w2ui-popup .w2ui-box").clone();s.removeClass("w2ui-box").addClass("w2ui-box-temp").find(".w2ui-popup-body").empty().append(i.body),"string"==typeof i.body&&0<s.find("div[rel=title], div[rel=body], div[rel=buttons]").length&&(0<(u=s.find("div[rel=title]")).length&&(i.title=u.html(),u.remove()),0<(u=s.find("div[rel=buttons]")).length&&(i.buttons=u.html(),u.remove()),0<(u=s.find("div[rel=body]")).length?i.body=u.html():i.body=s.html(),s.html(i.body)),c("#w2ui-popup .w2ui-box").after(s),i.buttons?(c("#w2ui-popup .w2ui-popup-buttons").show().html("").append(i.buttons),c("#w2ui-popup .w2ui-popup-body").removeClass("w2ui-popup-no-buttons"),c("#w2ui-popup .w2ui-box, #w2ui-popup .w2ui-box-temp").css("bottom","")):(c("#w2ui-popup .w2ui-popup-buttons").hide().html(""),c("#w2ui-popup .w2ui-popup-body").addClass("w2ui-popup-no-buttons"),c("#w2ui-popup .w2ui-box, #w2ui-popup .w2ui-box-temp").css("bottom","0px")),i.title?(c("#w2ui-popup .w2ui-popup-title").show().html((i.showClose?'<div class="w2ui-popup-button w2ui-popup-close" onmousedown="event.stopPropagation()" onclick="w2popup.close()">Close</div>':"")+(i.showMax?'<div class="w2ui-popup-button w2ui-popup-max" onmousedown="event.stopPropagation()" onclick="w2popup.toggle()">Max</div>':"")).append(i.title),c("#w2ui-popup .w2ui-popup-body").removeClass("w2ui-popup-no-title"),c("#w2ui-popup .w2ui-box, #w2ui-popup .w2ui-box-temp").css("top","")):(c("#w2ui-popup .w2ui-popup-title").hide().html(""),c("#w2ui-popup .w2ui-popup-body").addClass("w2ui-popup-no-title"),c("#w2ui-popup .w2ui-box, #w2ui-popup .w2ui-box-temp").css("top","0px"));var r=c("#w2ui-popup .w2ui-box")[0],d=c("#w2ui-popup .w2ui-box-temp")[0];w2utils.transition(r,d,i.transition,function(){t.restoreTemplate(),c(r).remove(),c(d).removeClass("w2ui-box-temp").addClass("w2ui-box");var e=c(d).find(".w2ui-popup-body");1==e.length&&(e[0].style.cssText=i.style),c("#w2ui-popup").data("prev-size",null),t.focus()}),w2popup.status="open",t.trigger(c.extend(n,{phase:"after"}))}i._last_focus=c(":focus"),i.keyboard&&c(document).on("keydown",this.keydown);var u={resizing:!1,mvMove:function(e){if(1!=u.resizing)return;e=e||window.event;u.div_x=e.screenX-u.x,u.div_y=e.screenY-u.y;e=w2popup.trigger({phase:"before",type:"move",target:"popup",div_x:u.div_x,div_y:u.div_y});if(!0===e.isCancelled)return;c("#w2ui-popup").css(w2utils.cssPrefix({transition:"none",transform:"translate3d("+u.div_x+"px, "+u.div_y+"px, 0px)"})),w2popup.trigger(c.extend(e,{phase:"after"}))},mvStop:function(e){if(1!=u.resizing)return;e=e||window.event;w2popup.status="open",u.div_x=e.screenX-u.x,u.div_y=e.screenY-u.y,c("#w2ui-popup").css({left:u.pos_x+u.div_x+"px",top:u.pos_y+u.div_y+"px"}).css(w2utils.cssPrefix({transition:"none",transform:"translate3d(0px, 0px, 0px)"})),u.resizing=!1,c(document).off("mousemove",u.mvMove),c(document).off("mouseup",u.mvStop),u.isLocked||w2popup.unlock()}};return c("#w2ui-popup .w2ui-popup-title").on("mousedown",function(e){w2popup.get().maximized||function(e){e=e||window.event;w2popup.status="moving",u.resizing=!0,u.isLocked=1==c("#w2ui-popup > .w2ui-lock").length,u.x=e.screenX,u.y=e.screenY,u.pos_x=c("#w2ui-popup").position().left,u.pos_y=c("#w2ui-popup").position().top,u.isLocked||w2popup.lock({opacity:0});c(document).on("mousemove",u.mvMove),c(document).on("mouseup",u.mvStop),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0;{if(!e.preventDefault)return;e.preventDefault()}}(e)}),this}setTimeout(function(){t.open.call(t,i)},100)},action:function(e,t){var i=this,s=c("#w2ui-popup").data("options");null!=t&&(i={parent:this,options:s=c("#w2ui-message"+t).data("options"),close:function(){w2popup.message({msgId:t})}});var n=s.actions[e],s=n;c.isPlainObject(n)&&n.onClick&&(s=n.onClick);n=this.trigger({phase:"before",target:e,msgId:t,type:"action",action:n,originalEvent:event});!0!==n.isCancelled&&("function"==typeof s&&s.call(i,event),this.trigger(c.extend(n,{phase:"after"})))},keydown:function(e){var t=c("#w2ui-popup").data("options");t&&!t.keyboard||!0!==(t=w2popup.trigger({phase:"before",type:"keydown",target:"popup",options:t,originalEvent:e})).isCancelled&&(27===e.keyCode&&(e.preventDefault(),0<c("#w2ui-popup .w2ui-message").length?w2popup.message():w2popup.close()),w2popup.trigger(c.extend(t,{phase:"after"})))},close:function(e){var t,i=this,e=c.extend({},c("#w2ui-popup").data("options"),e);0!==c("#w2ui-popup").length&&"closed"!=this.status&&("opening"!=this.status?!0!==(t=this.trigger({phase:"before",type:"close",target:"popup",options:e})).isCancelled&&(w2popup.status="closing",c("#w2ui-popup").css(w2utils.cssPrefix({transition:e.speed+"s opacity, "+e.speed+"s -webkit-transform"})).addClass("w2ui-popup-closing"),w2popup.unlockScreen(e),setTimeout(function(){i.restoreTemplate(),c("#w2ui-popup").remove(),w2popup.status="closed",e._last_focus&&0<e._last_focus.length&&e._last_focus.focus(),i.trigger(c.extend(t,{phase:"after"}))},1e3*e.speed),e.keyboard&&c(document).off("keydown",this.keydown)):setTimeout(function(){w2popup.close()},100))},toggle:function(){var e=this,t=c("#w2ui-popup").data("options"),i=this.trigger({phase:"before",type:"toggle",target:"popup",options:t});!0!==i.isCancelled&&(!0===t.maximized?w2popup.min():w2popup.max(),setTimeout(function(){e.trigger(c.extend(i,{phase:"after"}))},1e3*t.speed+50))},max:function(){var e,t=this,i=c("#w2ui-popup").data("options");!0===i.maximized||!0!==(e=this.trigger({phase:"before",type:"max",target:"popup",options:i})).isCancelled&&(w2popup.status="resizing",i.prevSize=c("#w2ui-popup").css("width")+":"+c("#w2ui-popup").css("height"),w2popup.resize(1e4,1e4,function(){w2popup.status="open",i.maximized=!0,t.trigger(c.extend(e,{phase:"after"})),c("#w2ui-popup .w2ui-grid, #w2ui-popup .w2ui-form, #w2ui-popup .w2ui-layout").each(function(){var e=c(this).attr("name");w2ui[e]&&w2ui[e].resize&&w2ui[e].resize()})}))},min:function(){var e,t,i=this,s=c("#w2ui-popup").data("options");!0===s.maximized&&(e=s.prevSize.split(":"),!0!==(t=this.trigger({phase:"before",type:"min",target:"popup",options:s})).isCancelled&&(w2popup.status="resizing",w2popup.resize(parseInt(e[0]),parseInt(e[1]),function(){w2popup.status="open",s.maximized=!1,s.prevSize=null,i.trigger(c.extend(t,{phase:"after"})),c("#w2ui-popup .w2ui-grid, #w2ui-popup .w2ui-form, #w2ui-popup .w2ui-layout").each(function(){var e=c(this).attr("name");w2ui[e]&&w2ui[e].resize&&w2ui[e].resize()})})))},get:function(){return c("#w2ui-popup").data("options")},set:function(e){w2popup.open(e)},clear:function(){c("#w2ui-popup .w2ui-popup-title").html(""),c("#w2ui-popup .w2ui-popup-body").html(""),c("#w2ui-popup .w2ui-popup-buttons").html("")},reset:function(){w2popup.open(w2popup.defaults)},load:function(i){var s,n,e;function l(e,t){delete i.url,c("body").append('<div id="w2ui-tmp" style="display: none">'+e+"</div>"),(null!=t&&0<c("#w2ui-tmp #"+t).length?c("#w2ui-tmp #"+t):c("#w2ui-tmp > div")).w2popup(i),0<c("#w2ui-tmp > style").length&&(t=c("<div>").append(c("#w2ui-tmp > style").clone()).html(),0===c("#w2ui-popup #div-style").length&&c("#w2ui-popup").append('<div id="div-style" style="position: absolute; left: -100; width: 1px"></div>'),c("#w2ui-popup #div-style").html(t)),c("#w2ui-tmp").remove()}w2popup.status="loading",null!=i.url?(e=String(i.url).split("#"),s=e[0],n=e[1],null==i&&(i={}),null!=(e=c("#w2ui-popup").data(s))?l(e,n):c.get(s,function(e,t,i){l(i.responseText,n),c("#w2ui-popup").data(s,i.responseText)})):console.log("ERROR: The url parameter is empty.")},message:function(i){var e=this;c().w2tag(),i=i||{width:200,height:100};var t=parseInt(c("#w2ui-popup").width()),s=parseInt(c("#w2ui-popup").height());i.originalWidth=i.width,i.originalHeight=i.height,parseInt(i.width)<10&&(i.width=10),parseInt(i.height)<10&&(i.height=10),null==i.hideOnClick&&(i.hideOnClick=!1);var n=c("#w2ui-popup").data("options")||{},l=parseInt(c("#w2ui-popup > .w2ui-popup-title").css("height"));(null==i.width||i.width>n.width-10)&&(i.width=n.width-10),(null==i.height||i.height>n.height-l-5)&&(i.height=n.height-l-5),i.originalHeight<0&&(i.height=s+i.originalHeight-l),i.originalWidth<0&&(i.width=t+2*i.originalWidth);var o,a,r,s=c("#w2ui-popup .w2ui-popup-title"),d=c("#w2ui-popup .w2ui-message").length;null!=i.actions&&(i.buttons="",Object.keys(i.actions).forEach(function(e){var t=i.actions[e];"function"==typeof t&&(i.buttons+='<button class="w2ui-btn" onclick="w2popup.action(\''+e+"', "+d+')">'+e+"</button>"),"object"==typeof t&&(i.buttons+='<button class="w2ui-btn '+(t.class||"")+'" style="'+(t.style||"")+'"onclick="w2popup.action(\''+e+"', "+d+')">'+(t.text||e)+"</button>"),"string"==typeof t&&(i.buttons+=t)})),""===c.trim(i.html)&&""===c.trim(i.body)&&""===c.trim(i.buttons)?(o=c("#w2ui-popup .w2ui-message").last(),i=(o=null!=i.msgId?c("#w2ui-message"+i.msgId):o).data("options")||{},!0!==(r=e.trigger({phase:"before",type:"msgClose",msgId:o.attr("data-msgId"),target:"popup",options:i})).isCancelled&&(o.css(w2utils.cssPrefix({transition:"0.15s",transform:"translateY(-"+i.height+"px)"})),l=c("#w2ui-popup .w2ui-message"),((l=c(l[l.length-2]).css("z-index",1500).data("msg-focus"))&&0<l.length?l:e).focus(),1==d&&w2popup.unlock(150),setTimeout(function(){o.remove(),"function"==typeof i.onClose&&i.onClose(r),e.trigger(c.extend(r,{phase:"after"}))},150))):(""===c.trim(i.body)&&""===c.trim(i.buttons)||(i.html='<div class="w2ui-message-body">'+i.body+'</div><div class="w2ui-message-buttons">'+i.buttons+"</div>"),c("#w2ui-popup .w2ui-message").css("z-index",1390).data("msg-focus",c(":focus")),s.css("z-index",1501),null==i.close&&(i.close=function(){w2popup.message({msgId:d})}),c("#w2ui-popup .w2ui-box").before('<div id="w2ui-message'+d+'" class="w2ui-message" style="display: none; z-index: 1500; '+(0===s.length?"top: 0px;":"top: "+w2utils.getSize(s,"height")+"px;")+(null!=i.width?"width: "+i.width+"px; left: "+(t-i.width)/2+"px;":"left: 10px; right: 10px;")+(null!=i.height?"height: "+i.height+"px;":"bottom: 6px;")+w2utils.cssPrefix("transition","0s",!0)+'" data-msgId="'+d+'" '+(!0===i.hideOnClick?'onclick="w2popup.message();"':"")+"></div>"),c("#w2ui-popup #w2ui-message"+d).data("options",i),a=c("#w2ui-popup #w2ui-message"+d).css("display"),c("#w2ui-popup #w2ui-message"+d).css(w2utils.cssPrefix({transform:"none"==a?"translateY(-"+i.height+"px)":"translateY(0px)"})),"none"==a&&(c("#w2ui-popup #w2ui-message"+d).show().html(i.html),setTimeout(function(){c("#w2ui-popup #w2ui-message"+d).css(c.extend(w2utils.cssPrefix("transition",".3s",!1),w2utils.cssPrefix({transform:"none"==a?"translateY(0px)":"translateY(-"+i.height+"px)"})))},1),0===d&&w2popup.lock(),!0!==(r=e.trigger({phase:"before",type:"msgOpen",msgId:d,target:"popup",options:i})).isCancelled&&setTimeout(function(){e.focus(),c("#w2ui-popup #w2ui-message"+d).css(w2utils.cssPrefix({transition:"0s"})),"function"==typeof i.onOpen&&i.onOpen(r),e.trigger(c.extend(r,{phase:"after"}))},350)))},focus:function(){var t=null,e=c("#w2ui-popup"),i="input:visible, button:visible, select:visible, textarea:visible, [contentEditable], .w2ui-input";c(e).find(i).off(".keep-focus");var s,n=c("#w2ui-popup .w2ui-message").length-1,n=c("#w2ui-popup #w2ui-message"+n);0<n.length?(0<(s=c(n[n.length-1]).find("button")).length&&s[0].focus(),t=n):0<e.length&&(0<(s=e.find(".w2ui-popup-buttons button")).length&&s[0].focus(),t=e),c(t).find(i).on("blur.keep-focus",function(e){setTimeout(function(){var e=c(":focus");!(0<e.length&&!c(t).find(i).is(e)||e.hasClass("w2ui-popup-hidden"))||0<(e=c(t).find(i)).length&&e[0].focus()},1)})},lock:function(e,t){var i=Array.prototype.slice.call(arguments,0);i.unshift(c("#w2ui-popup")),w2utils.lock.apply(window,i)},unlock:function(e){w2utils.unlock(c("#w2ui-popup"),e)},lockScreen:function(e){return!(0<c("#w2ui-lock").length)&&(null==(e=null==e?c("#w2ui-popup").data("options"):e)&&(e={}),e=c.extend({},w2popup.defaults,e),c("body").append('<div id="w2ui-lock"     onmousewheel="if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true; if (event.preventDefault) event.preventDefault(); else return false;"    style="position: '+("IE5"==w2utils.engine?"absolute":"fixed")+"; z-Index: 1199; left: 0px; top: 0px;            padding: 0px; margin: 0px; background-color: "+e.color+'; width: 100%; height: 100%; opacity: 0;"></div>'),setTimeout(function(){c("#w2ui-lock").css("opacity",e.opacity).css(w2utils.cssPrefix("transition",e.speed+"s opacity"))},1),1==e.modal?(c("#w2ui-lock").on("mousedown",function(){c("#w2ui-lock").css("opacity","0.6").css(w2utils.cssPrefix("transition",".1s"))}),c("#w2ui-lock").on("mouseup",function(){setTimeout(function(){c("#w2ui-lock").css("opacity",e.opacity).css(w2utils.cssPrefix("transition",".1s"))},100)})):c("#w2ui-lock").on("mousedown",function(){w2popup.close()}),!0)},unlockScreen:function(e){return 0!==c("#w2ui-lock").length&&(null==(e=null==e?c("#w2ui-popup").data("options"):e)&&(e={}),e=c.extend({},w2popup.defaults,e),c("#w2ui-lock").css("opacity","0").css(w2utils.cssPrefix("transition",e.speed+"s opacity")),setTimeout(function(){c("#w2ui-lock").remove()},1e3*e.speed),!0)},resizeMessages:function(){c("#w2ui-popup").data("options");c("#w2ui-popup .w2ui-message").each(function(){var e=c(this).data("options"),t=c("#w2ui-popup");parseInt(e.width)<10&&(e.width=10),parseInt(e.height)<10&&(e.height=10);var i=parseInt(t.find("> .w2ui-popup-title").css("height")),s=parseInt(t.width()),t=parseInt(t.height());e.width=e.originalWidth,e.width>s-10&&(e.width=s-10),e.height=e.originalHeight,e.height>t-i-5&&(e.height=t-i-5),e.originalHeight<0&&(e.height=t+e.originalHeight-i),e.originalWidth<0&&(e.width=s+2*e.originalWidth),c(this).css({left:(s-e.width)/2+"px",width:e.width+"px",height:e.height+"px"})})},resize:function(e,t,i){var s=this,n=c("#w2ui-popup").data("options")||{};null==n.speed&&(n.speed=0),e=parseInt(e),t=parseInt(t),null==window.innerHeight?(o=parseInt(document.documentElement.offsetWidth),l=parseInt(document.documentElement.offsetHeight),"IE7"===w2utils.engine&&(o+=21,l+=4)):(o=parseInt(window.innerWidth),l=parseInt(window.innerHeight));var l=(l-(t=l-10<t?l-10:t))/2*.6,o=(o-(e=o-10<e?o-10:e))/2;c("#w2ui-popup").css(w2utils.cssPrefix({transition:n.speed+"s width, "+n.speed+"s height, "+n.speed+"s left, "+n.speed+"s top"})).css({top:l,left:o,width:e,height:t});var a=setInterval(function(){s.resizeMessages()},10);setTimeout(function(){clearInterval(a),n.width=e,n.height=t,s.resizeMessages(),"function"==typeof i&&i()},1e3*n.speed+50)},restoreTemplate:function(){var e,t,i,s=c("#w2ui-popup").data("options");null!=s&&(i=w2popup._template,e=s.title,t=s.body,s=s.buttons,w2popup._prev?(i=w2popup._prev.template,e=w2popup._prev.title,t=w2popup._prev.body,s=w2popup._prev.buttons,delete w2popup._prev):delete w2popup._template,null==i||0!==(i=c(i)).length&&("body"==c(t).attr("rel")?(e&&i.append(e),t&&i.append(t),s&&i.append(s)):i.append(t)))}},c.extend(w2popup,w2utils.event)}(jQuery);var w2alert=function(e,t,i){var s=jQuery;return null==t&&(t=w2utils.lang("Notification")),0<s("#w2ui-popup").length&&"closing"!=w2popup.status?w2popup.message({width:400,height:170,body:'<div class="w2ui-centered w2ui-alert-msg" style="font-size: 13px;">'+e+"</div>",buttons:'<button onclick="w2popup.message();" class="w2ui-popup-btn w2ui-btn">'+w2utils.lang("Ok")+"</button>",onOpen:function(){s("#w2ui-popup .w2ui-message .w2ui-popup-btn").focus()},onClose:function(){"function"==typeof i&&i()}}):w2popup.open({width:450,height:220,showMax:!1,showClose:!1,title:t,body:'<div class="w2ui-centered w2ui-alert-msg" style="font-size: 13px;">'+e+"</div>",buttons:'<button onclick="w2popup.close();" class="w2ui-popup-btn w2ui-btn">'+w2utils.lang("Ok")+"</button>",onOpen:function(e){setTimeout(function(){s("#w2ui-popup .w2ui-popup-btn").focus()},1)},onKeydown:function(e){s("#w2ui-popup .w2ui-popup-btn").focus().addClass("clicked")},onClose:function(){"function"==typeof i&&i()}}),{ok:function(e){return i=e,this},done:function(e){return i=e,this}}},w2confirm=function(e,t,i){var s=jQuery,n={},l={msg:"",title:w2utils.lang("Confirmation"),width:0<s("#w2ui-popup").length?400:450,height:0<s("#w2ui-popup").length?170:220,yes_text:"Yes",yes_class:"",yes_style:"",yes_callBack:null,no_text:"No",no_class:"",no_style:"",no_callBack:null,focus_to_no:!1,callBack:null};return 1==arguments.length&&"object"==typeof e?s.extend(n,l,e):"function"==typeof t?s.extend(n,l,{msg:e,callBack:t}):s.extend(n,l,{msg:e,title:t,callBack:i}),"object"==typeof n.btn_yes&&(n.yes_text=n.btn_yes.text||n.yes_text,n.yes_class=n.btn_yes.class||n.yes_class,n.yes_style=n.btn_yes.style||n.yes_style,n.yes_callBack=n.btn_yes.callBack||n.yes_callBack),"object"==typeof n.btn_no&&(n.no_text=n.btn_no.text||n.no_text,n.no_class=n.btn_no.class||n.no_class,n.no_style=n.btn_no.style||n.no_style,n.no_callBack=n.btn_no.callBack||n.no_callBack),0<s("#w2ui-popup").length&&"closing"!=w2popup.status&&w2popup.get()?(n.width>w2popup.get().width&&(n.width=w2popup.get().width),n.height>w2popup.get().height-50&&(n.height=w2popup.get().height-50),w2popup.message({width:n.width,height:n.height,body:'<div class="w2ui-centered w2ui-confirm-msg" style="font-size: 13px;">'+n.msg+"</div>",buttons:w2utils.settings.macButtonOrder?'<button id="No" class="w2ui-popup-btn w2ui-btn '+n.no_class+'" style="'+n.no_style+'">'+w2utils.lang(n.no_text)+'</button><button id="Yes" class="w2ui-popup-btn w2ui-btn '+n.yes_class+'" style="'+n.yes_style+'">'+w2utils.lang(n.yes_text)+"</button>":'<button id="Yes" class="w2ui-popup-btn w2ui-btn '+n.yes_class+'" style="'+n.yes_style+'">'+w2utils.lang(n.yes_text)+'</button><button id="No" class="w2ui-popup-btn w2ui-btn '+n.no_class+'" style="'+n.no_style+'">'+w2utils.lang(n.no_text)+"</button>",onOpen:function(e){s("#w2ui-popup .w2ui-message .w2ui-btn").on("click.w2confirm",function(e){w2popup._confirm_btn=e.target.id,w2popup.message()}),"function"==typeof n.onOpen&&n.onOpen()},onClose:function(e){s("#w2ui-popup .w2ui-message .w2ui-btn").off("click.w2confirm"),setTimeout(function(){"function"==typeof n.callBack&&n.callBack(w2popup._confirm_btn),"Yes"==w2popup._confirm_btn&&"function"==typeof n.yes_callBack&&n.yes_callBack(),"No"==w2popup._confirm_btn&&"function"==typeof n.no_callBack&&n.no_callBack()},300),"function"==typeof n.onClose&&n.onClose()}})):(w2utils.isInt(n.height)||(n.height=n.height+50),w2popup.open({width:n.width,height:n.height,title:n.title,modal:!0,showClose:!1,body:'<div class="w2ui-centered w2ui-confirm-msg" style="font-size: 13px;">'+n.msg+"</div>",buttons:w2utils.settings.macButtonOrder?'<button id="No" class="w2ui-popup-btn w2ui-btn '+n.no_class+'" style="'+n.no_style+'">'+w2utils.lang(n.no_text)+'</button><button id="Yes" class="w2ui-popup-btn w2ui-btn '+n.yes_class+'" style="'+n.yes_style+'">'+w2utils.lang(n.yes_text)+"</button>":'<button id="Yes" class="w2ui-popup-btn w2ui-btn '+n.yes_class+'" style="'+n.yes_style+'">'+w2utils.lang(n.yes_text)+'</button><button id="No" class="w2ui-popup-btn w2ui-btn '+n.no_class+'" style="'+n.no_style+'">'+w2utils.lang(n.no_text)+"</button>",onOpen:function(e){setTimeout(function(){s("#w2ui-popup .w2ui-popup-btn").on("click",function(e){w2popup.close(),"function"==typeof n.callBack&&n.callBack(e.target.id),"Yes"==e.target.id&&"function"==typeof n.yes_callBack&&n.yes_callBack(),"No"==e.target.id&&"function"==typeof n.no_callBack&&n.no_callBack()}),(n.focus_to_no?s("#w2ui-popup .w2ui-popup-btn#No"):s("#w2ui-popup .w2ui-popup-btn#Yes")).focus(),"function"==typeof n.onOpen&&n.onOpen()},1)},onClose:function(e){"function"==typeof n.onClose&&n.onClose()},onKeydown:function(e){if(0===s("#w2ui-popup .w2ui-message").length)switch(e.originalEvent.keyCode){case 13:s("#w2ui-popup .w2ui-popup-btn#Yes").focus().addClass("clicked"),w2popup.close();break;case 27:s("#w2ui-popup .w2ui-popup-btn#No").focus().click(),w2popup.close()}}})),{yes:function(e){return n.yes_callBack=e,this},no:function(e){return n.no_callBack=e,this}}},w2prompt=function(e,t,i){var s=jQuery,n={},l={title:w2utils.lang("Notification"),width:0<s("#w2ui-popup").length?400:450,height:0<s("#w2ui-popup").length?170:220,label:"",value:"",attrs:"",textarea:!1,ok_text:w2utils.lang("Ok"),ok_class:"",cancel_text:w2utils.lang("Cancel"),cancel_class:"",callBack:null,onOpen:null,onClose:null};function o(e,t){"ok"==e&&"function"==typeof n.ok_callBack&&n.ok_callBack(t),"cancel"==e&&"function"==typeof n.cancel_callBack&&n.cancel_callBack(t),"function"==typeof n.callBack&&n.callBack(e,t)}return w2popup.tmp=w2popup.tmp||{},1==arguments.length&&"object"==typeof e?s.extend(n,l,e):"function"==typeof t?s.extend(n,l,{label:e,callBack:t}):s.extend(n,l,{label:e,title:t,callBack:i}),0<s("#w2ui-popup").length&&"closing"!=w2popup.status&&w2popup.get()?(n.width>w2popup.get().width&&(n.width=w2popup.get().width),n.height>w2popup.get().height-50&&(n.height=w2popup.get().height-50),w2popup.message({width:n.width,height:n.height,body:n.textarea?'<div class="w2ui-prompt textarea">  <div>'+n.label+'</div>  <textarea id="w2prompt" class="w2ui-input" '+n.attrs+"></textarea></div>":'<div class="w2ui-prompt w2ui-centered">  <label>'+n.label+'</label>  <input id="w2prompt" class="w2ui-input" '+n.attrs+"></div>",buttons:w2utils.settings.macButtonOrder?'<button id="Cancel" class="w2ui-popup-btn w2ui-btn '+n.cancel_class+'">'+n.cancel_text+'</button><button id="Ok" class="w2ui-popup-btn w2ui-btn '+n.ok_class+'">'+n.ok_text+"</button>":'<button id="Ok" class="w2ui-popup-btn w2ui-btn '+n.ok_class+'">'+n.ok_text+'</button><button id="Cancel" class="w2ui-popup-btn w2ui-btn '+n.cancel_class+'">'+n.cancel_text+"</button>",onOpen:function(){s("#w2prompt").val(n.value).off(".w2prompt").on("keydown.w2prompt",function(e){13==e.keyCode&&s("#w2ui-popup .w2ui-message .w2ui-btn#Ok").click()}),s("#w2ui-popup .w2ui-message .w2ui-btn#Ok").off(".w2prompt").on("click.w2prompt",function(e){w2popup.tmp.btn="ok",w2popup.tmp.value=s("#w2prompt").val(),w2popup.message()}),s("#w2ui-popup .w2ui-message .w2ui-btn#Cancel").off(".w2prompt").on("click.w2prompt",function(e){w2popup.tmp.btn="cancel",w2popup.tmp.value=null,w2popup.message()}),setTimeout(function(){s("#w2prompt").focus()},100),"function"==typeof n.onOpen&&n.onOpen()},onClose:function(){s("#w2ui-popup .w2ui-message .w2ui-btn").off("click.w2prompt"),setTimeout(function(){o(w2popup.tmp.btn,w2popup.tmp.value)},300),"function"==typeof n.onClose&&n.onClose()}})):(w2utils.isInt(n.height)||(n.height=n.height+50),w2popup.open({width:n.width,height:n.height,title:n.title,modal:!0,showClose:!1,body:n.textarea?'<div class="w2ui-prompt">  <div>'+n.label+'</div>  <textarea id="w2prompt" class="w2ui-input" '+n.attrs+"></textarea></div>":'<div class="w2ui-prompt w2ui-centered" style="font-size: 13px;">  <label>'+n.label+'</label>  <input id="w2prompt" class="w2ui-input" '+n.attrs+"></div>",buttons:w2utils.settings.macButtonOrder?'<button id="Cancel" class="w2ui-popup-btn w2ui-btn '+n.cancel_class+'">'+n.cancel_text+'</button><button id="Ok" class="w2ui-popup-btn w2ui-btn '+n.ok_class+'">'+n.ok_text+"</button>":'<button id="Ok" class="w2ui-popup-btn w2ui-btn '+n.ok_class+'">'+n.ok_text+'</button><button id="Cancel" class="w2ui-popup-btn w2ui-btn '+n.cancel_class+'">'+n.cancel_text+"</button>",onOpen:function(e){setTimeout(function(){s("#w2prompt").val(n.value),s("#w2prompt").w2field("text"),s("#w2ui-popup .w2ui-popup-btn#Ok").on("click",function(e){w2popup.tmp.btn="ok",w2popup.tmp.value=s("#w2prompt").val(),w2popup.close()}),s("#w2ui-popup .w2ui-popup-btn#Cancel").on("click",function(e){w2popup.tmp.btn="cancel",w2popup.tmp.value=null,w2popup.close()}),s("#w2ui-popup .w2ui-popup-btn#Ok"),setTimeout(function(){s("#w2prompt").focus()},100),"function"==typeof n.onOpen&&n.onOpen()},1)},onClose:function(e){o(w2popup.tmp.btn,w2popup.tmp.value),"function"==typeof n.onClose&&n.onClose()},onKeydown:function(e){if(0===s("#w2ui-popup .w2ui-message").length)switch(e.originalEvent.keyCode){case 13:s("#w2ui-popup .w2ui-popup-btn#Ok").focus().addClass("clicked");break;case 27:w2popup.tmp.btn="cancel",w2popup.tmp.value=null}}})),{change:function(e){return s("#w2prompt").on("keyup",e).keyup(),this},ok:function(e){return n.ok_callBack=e,this},cancel:function(e){return n.cancel_callBack=e,this}}};!function(c){function l(e){this.box=null,this.name=null,this.active=null,this.reorder=!1,this.flow="down",this.tooltip="top|left",this.tabs=[],this.routeData={},this.tmp={},this.right="",this.style="",c.extend(this,{handlers:[]}),c.extend(!0,this,w2obj.tabs,e)}c.fn.w2tabs=function(e){if(!c.isPlainObject(e)){var t=w2ui[c(this).attr("name")];return t?0<arguments.length?(t[e]&&t[e].apply(t,Array.prototype.slice.call(arguments,1)),this):t:null}if(w2utils.checkName(e,"w2tabs")){for(var i=e.tabs||[],s=new l(e),n=0;n<i.length;n++)s.tabs[n]=c.extend({},l.prototype.tab,i[n]);return w2ui[s.name]=s,0!==c(this).length&&s.render(c(this)[0]),s}},l.prototype={onClick:null,onClose:null,onRender:null,onRefresh:null,onResize:null,onDestroy:null,tab:{id:null,text:null,route:null,hidden:!1,disabled:!1,closable:!1,tooltip:null,style:"",onClick:null,onRefresh:null,onClose:null},add:function(e){return this.insert(null,e)},insert:function(e,t){c.isArray(t)||(t=[t]);for(var i=0;i<t.length;i++){if(null==t[i].id)return void console.log('ERROR: The parameter "id" is required but not supplied. (obj: '+this.name+")");if(!w2utils.checkUniqueId(t[i].id,this.tabs,"tabs",this.name))return;var s,n=c.extend({},l.prototype.tab,t[i]);null==e?this.tabs.push(n):(s=this.get(e,!0),s=this.tabs[s].id,this.insertTabHTML(s,n))}},remove:function(){for(var e=0,t=0;t<arguments.length;t++){var i=this.get(arguments[t]);if(!i)return!1;e++,this.tabs.splice(this.get(i.id,!0),1),c(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(i.id)).remove()}return this.resize(),e},select:function(e){return this.active!=e&&null!=this.get(e)&&(this.active=e,this.refresh(),!0)},set:function(e,t){var i=this.get(e,!0);return null!=i&&(c.extend(this.tabs[i],t),this.refresh(e),!0)},get:function(e,t){if(0===arguments.length){for(var i=[],s=0;s<this.tabs.length;s++)null!=this.tabs[s].id&&i.push(this.tabs[s].id);return i}for(var n=0;n<this.tabs.length;n++)if(this.tabs[n].id==e)return!0===t?n:this.tabs[n];return null},show:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&!1!==n.hidden&&(e++,n.hidden=!1,i.push(n.id))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e]);t.resize()},15),e},hide:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&!0!==n.hidden&&(e++,n.hidden=!0,i.push(n.id))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e]);t.resize()},15),e},enable:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&!1!==n.disabled&&(e++,n.disabled=!1,i.push(n.id))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e])},15),e},disable:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&!0!==n.disabled&&(e++,n.disabled=!0,i.push(n.id))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e])},15),e},dragMove:function(e){if(this.tmp.reordering){var s=this,t=this.tmp.moving,i=this.tabs[t.index],n=u(t.index,1),l=u(t.index,-1),i=c("#tabs_"+this.name+"_tab_"+w2utils.escapeId(i.id));if(0<t.divX&&n){var o=c("#tabs_"+this.name+"_tab_"+w2utils.escapeId(n.id)),a=parseInt(i.css("width")),r=parseInt(o.css("width"));if(r-=a=a<r?Math.floor(a/3):Math.floor(r/3),t.divX>r){var d=this.tabs.indexOf(n);return this.tabs.splice(t.index,0,this.tabs.splice(d,1)[0]),t.$tab.before(o),t.$tab.css("opacity",0),void Object.assign(this.tmp.moving,{index:d,divX:-a,x:e.pageX+a,left:t.left+t.divX+a})}}t.divX<0&&l&&(o=c("#tabs_"+this.name+"_tab_"+w2utils.escapeId(l.id)),a=parseInt(i.css("width")),r=parseInt(o.css("width")),r-=a=a<r?Math.floor(a/3):Math.floor(r/3),Math.abs(t.divX)>r&&(d=this.tabs.indexOf(l),this.tabs.splice(t.index,0,this.tabs.splice(d,1)[0]),o.before(t.$tab),t.$tab.css("opacity",0),Object.assign(t,{index:d,divX:a,x:e.pageX-a,left:t.left+t.divX-a})))}function u(e,t){e+=t;var i=s.tabs[e];return i=i&&i.hidden?u(e,t):i}},tooltipShow:function(e,t,i){var s,n,l=this.get(e),o=c(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(e));null==this.tooltip||l.disabled||this.tmp.reordering||(s=this.tooltip,"function"==typeof(n=l.tooltip)&&(n=n.call(this,l)),o.prop("_mouse_over",!0),setTimeout(function(){!0===o.prop("_mouse_over")&&!0!==o.prop("_mouse_tooltip")&&(o.prop("_mouse_tooltip",!0),o.w2tag(w2utils.lang(n),{position:s})),1==i&&o.w2tag(w2utils.lang(n),{position:s})},1))},tooltipHide:function(e){var t=this.get(e),i=c(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(e));null==this.tooltip||t.disabled||this.tmp.reordering||(i.removeProp("_mouse_over"),setTimeout(function(){!0!==i.prop("_mouse_over")&&!0===i.prop("_mouse_tooltip")&&(i.removeProp("_mouse_tooltip"),i.w2tag())},1))},getTabHTML:function(e){var t=this.get(e,!0),i=this.tabs[t];if(null==i)return!1;null==i.text&&null!=i.caption&&(i.text=i.caption),null==i.tooltip&&null!=i.hint&&(i.tooltip=i.hint),null!=i.caption&&console.log("NOTICE: tabs tab.caption property is deprecated, please use tab.text. Tab -> ",i),null!=i.hint&&console.log("NOTICE: tabs tab.hint property is deprecated, please use tab.tooltip. Tab -> ",i);var s=i.text;null==(s="function"==typeof s?s.call(this,i):s)&&(s="");e="",t="";return i.hidden&&(t+="display: none;"),i.disabled&&(t+="opacity: 0.2;"),i.closable&&!i.disabled&&(e='<div class="w2ui-tab-close'+(this.active===i.id?" active":"")+'"  onmouseover= "w2ui[\''+this.name+"'].tooltipShow('"+i.id+"', event)\"  onmouseout = \"w2ui['"+this.name+"'].tooltipHide('"+i.id+'\', event)"  onmousedown= "event.stopPropagation()"  onmouseup  = "w2ui[\''+this.name+"'].animateClose('"+i.id+"', event); event.stopPropagation()\"></div>"),'<div id="tabs_'+this.name+"_tab_"+i.id+'" style="'+t+" "+i.style+'"   class="w2ui-tab'+(this.active===i.id?" active":"")+(i.closable?" closable":"")+(i.class?" "+i.class:"")+'"   onmouseover = "w2ui[\''+this.name+"'].tooltipShow('"+i.id+"', event)\"   onmouseout  = \"w2ui['"+this.name+"'].tooltipHide('"+i.id+"', event)\"   onmousedown = \"w2ui['"+this.name+"'].initReorder('"+i.id+"', event)\"    onclick     = \"w2ui['"+this.name+"'].click('"+i.id+"', event)\">"+w2utils.lang(s)+e+"</div>"},refresh:function(e){var t=(new Date).getTime();"up"==this.flow?c(this.box).addClass("w2ui-tabs-up"):c(this.box).removeClass("w2ui-tabs-up");var i=this.trigger({phase:"before",type:"refresh",target:null!=e?e:this.name,object:this.get(e)});if(!0!==i.isCancelled){if(null==e)for(var s=0;s<this.tabs.length;s++)this.refresh(this.tabs[s].id);else{var n=c(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(e)),e=this.getTabHTML(e);0===n.length?c(this.box).find("#tabs_"+this.name+"_right").before(e):n.replaceWith(e)}return c("#tabs_"+this.name+"_right").html(this.right),this.trigger(c.extend(i,{phase:"after"})),(new Date).getTime()-t}},render:function(e){var t=(new Date).getTime(),i=this.trigger({phase:"before",type:"render",target:this.name,box:e});if(!0!==i.isCancelled){if(null!=e&&(0<c(this.box).find("#tabs_"+this.name+"_right").length&&c(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-tabs").html(""),this.box=e),!this.box)return!1;e='<div class="w2ui-scroll-wrapper" onmousedown="var el=w2ui[\''+this.name+'\']; if (el) el.resize();">    <div class="w2ui-tabs-line"></div>    <div id="tabs_'+this.name+'_right" class="w2ui-tabs-right">'+this.right+'</div></div><div class="w2ui-scroll-left" onclick="var el=w2ui[\''+this.name+"']; if (el) el.scroll('left');\"></div><div class=\"w2ui-scroll-right\" onclick=\"var el=w2ui['"+this.name+"']; if (el) el.scroll('right');\"></div>";return c(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-tabs").html(e),0<c(this.box).length&&(c(this.box)[0].style.cssText+=this.style),this.trigger(c.extend(i,{phase:"after"})),this.refresh(),this.resize(),(new Date).getTime()-t}},initReorder:function(e,t){var i,s,n,l,o;this.reorder&&(s=c("#tabs_"+(i=this).name+"_tab_"+w2utils.escapeId(e)),n=this.get(e,!0),(l=s.clone()).attr("id","#tabs_"+this.name+"_tab_ghost"),this.tmp.moving={index:n,indexFrom:n,$tab:s,$ghost:l,divX:0,left:s.offset().left,parentX:c(this.box).offset().left,x:t.pageX,opacity:s.css("opacity")},c("body").off(".w2uiTabReorder").on("mousemove.w2uiTabReorder",function(e){if(!i.tmp.reordering){if(!0===(o=i.trigger({phase:"before",type:"reorder",target:i.tabs[n].id,indexFrom:n,tab:i.tabs[n]})).isCancelled)return;c().w2tag(),i.tmp.reordering=!0,l.addClass("moving"),l.css({"pointer-events":"none",position:"absolute",left:s.offset().left}),s.css("opacity",0),c(i.box).find(".w2ui-scroll-wrapper").append(l),c(i.box).find(".w2ui-tab-close").hide()}i.tmp.moving.divX=e.pageX-i.tmp.moving.x,l.css("left",i.tmp.moving.left-i.tmp.moving.parentX+i.tmp.moving.divX+"px"),i.dragMove(e)}).on("mouseup.w2uiTabReorder",function(){c("body").off(".w2uiTabReorder"),l.css({transition:"0.1s",left:i.tmp.moving.$tab.offset().left-i.tmp.moving.parentX}),c(i.box).find(".w2ui-tab-close").show(),setTimeout(function(){l.remove(),s.css({opacity:i.tmp.moving.opacity}),i.tmp.reordering&&(i.trigger(c.extend(o,{phase:"after",indexTo:i.tmp.moving.index})),!0===o.isCancelled)||(i.tmp.reordering=!1)},100)}))},scroll:function(e){var t,i=c(this.box),s=this,n=i.find(".w2ui-scroll-wrapper"),l=n.scrollLeft(),i=c(this.box).find(".w2ui-tabs-right"),o=n.outerWidth(),a=l+parseInt(i.offset().left)+parseInt(i.width());switch(e){case"left":(t=l-o+50)<=0&&(t=0),n.animate({scrollLeft:t},300);break;case"right":a-o<=(t=l+o-50)&&(t=a-o),n.animate({scrollLeft:t},300)}setTimeout(function(){s.resize()},350)},resize:function(){var e=(new Date).getTime(),t=this.trigger({phase:"before",type:"resize",target:this.name});if(!0!==t.isCancelled){var i=c(this.box);i.find(".w2ui-scroll-left, .w2ui-scroll-right").hide();var s=i.find(".w2ui-scroll-wrapper"),n=c(this.box).find(".w2ui-tabs-right"),l=s.outerWidth(),o=0<n.length?n[0].offsetLeft+n[0].clientWidth:0;return l<o&&(0<s.scrollLeft()&&i.find(".w2ui-scroll-left").show(),n=parseInt(s.css("padding-right")),l<o-s.scrollLeft()-n&&i.find(".w2ui-scroll-right").show()),this.trigger(c.extend(t,{phase:"after"})),(new Date).getTime()-e}},destroy:function(){var e=this.trigger({phase:"before",type:"destroy",target:this.name});!0!==e.isCancelled&&(0<c(this.box).find("#tabs_"+this.name+"_right").length&&c(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-tabs").html(""),delete w2ui[this.name],this.trigger(c.extend(e,{phase:"after"})))},click:function(e,t){var i=this.get(e);if(null==i||i.disabled||this.tmp.reordering)return!1;t=this.trigger({phase:"before",type:"click",target:e,tab:i,object:i,originalEvent:t});if(!0!==t.isCancelled){if(c(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(this.active)).removeClass("active"),c(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(this.active)).removeClass("active"),this.active=i.id,"string"==typeof i.route){var s=""!==i.route?String("/"+i.route).replace(/\/{2,}/g,"/"):"",n=w2utils.parseRoute(s);if(0<n.keys.length)for(var l=0;l<n.keys.length;l++)null!=this.routeData[n.keys[l].name]&&(s=s.replace(new RegExp(":"+n.keys[l].name,"g"),this.routeData[n.keys[l].name]));setTimeout(function(){window.location.hash=s},1)}this.trigger(c.extend(t,{phase:"after"})),this.refresh(e)}},animateClose:function(e,t){var i=this.get(e);if(null==i||i.disabled)return!1;var s,n=this.trigger({phase:"before",type:"close",target:e,object:this.get(e),originalEvent:t});!0!==n.isCancelled&&((i=c((s=this).box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(i.id))).css({opacity:0,transition:".25s"}).find(".w2ui-tab-close").remove(),i.css({"padding-left":0,"padding-right":0,"text-overflow":"clip",overflow:"hidden",width:"0px"}),setTimeout(function(){s.remove(e),s.trigger(c.extend(n,{phase:"after"})),s.refresh()},250))},insertTabHTML:function(e,t){var i=this.get(e,!0);this.tabs=this.tabs.slice(0,i).concat([t],this.tabs.slice(i));e=c(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(e)),t=c(this.getTabHTML(t.id));e.before(t),this.resize()}},c.extend(l.prototype,w2utils.event),w2obj.tabs=l}(jQuery),function(p){function r(e){this.box=null,this.name=null,this.routeData={},this.items=[],this.right="",this.tooltip="top|left",p.extend(!0,this,w2obj.toolbar,e)}p.fn.w2toolbar=function(e){if(!p.isPlainObject(e)){var t=w2ui[p(this).attr("name")];return t?0<arguments.length?(t[e]&&t[e].apply(t,Array.prototype.slice.call(arguments,1)),this):t:null}if(w2utils.checkName(e,"w2toolbar")){var i=e.items||[],s=new r(e);p.extend(s,{items:[],handlers:[]});for(var n=0;n<i.length;n++)if(s.items[n]=p.extend({},r.prototype.item,i[n]),"menu-check"==s.items[n].type){var l=s.items[n];if(Array.isArray(l.selected)||(l.selected=[]),Array.isArray(l.items))for(var o=0;o<l.items.length;o++)(a=l.items[o]).checked&&-1==l.selected.indexOf(a.id)&&l.selected.push(a.id),a.checked||-1==l.selected.indexOf(a.id)||(a.checked=!0),null==a.checked&&(a.checked=!1)}else if("menu-radio"==s.items[n].type){l=s.items[n];if(Array.isArray(l.items))for(var a,o=0;o<l.items.length;o++)(a=l.items[o]).checked&&null==l.selected?l.selected=a.id:a.checked=!1,a.checked||l.selected!=a.id||(a.checked=!0),null==a.checked&&(a.checked=!1)}return w2ui[s.name]=s,0!==p(this).length&&s.render(p(this)[0]),s}},r.prototype={onClick:null,onRender:null,onRefresh:null,onResize:null,onDestroy:null,item:{id:null,type:"button",text:null,html:"",tooltip:null,count:null,hidden:!1,disabled:!1,checked:!1,img:null,icon:null,route:null,arrow:!0,style:null,group:null,items:null,selected:null,overlay:{},color:null,options:{advanced:!1,transparent:!0,html:""},onClick:null,onRefresh:null},add:function(e){this.insert(null,e)},insert:function(e,t){p.isArray(t)||(t=[t]);for(var i=0;i<t.length;i++){if(null==t[i].type)return void console.log('ERROR: The parameter "type" is required but not supplied in w2toolbar.add() method.');if(-1==p.inArray(String(t[i].type),["button","check","radio","drop","menu","menu-radio","menu-check","color","text-color","break","html","spacer"]))return void console.log('ERROR: The parameter "type" should be one of the following [button, check, radio, drop, menu, break, html, spacer] in w2toolbar.add() method.');if(null==t[i].id&&"break"!=t[i].type&&"spacer"!=t[i].type)return void console.log('ERROR: The parameter "id" is required but not supplied in w2toolbar.add() method.');if(!w2utils.checkUniqueId(t[i].id,this.items,"toolbar items",this.name))return;var s,n=p.extend({},r.prototype.item,t[i]);null==e?this.items.push(n):(s=this.get(e,!0),this.items=this.items.slice(0,s).concat([n],this.items.slice(s))),this.refresh(n.id),this.resize()}},remove:function(){for(var e=0,t=0;t<arguments.length;t++){var i=this.get(arguments[t]);i&&-1==String(arguments[t]).indexOf(":")&&(e++,p(this.box).find("#tb_"+this.name+"_item_"+w2utils.escapeId(i.id)).remove(),null!=(i=this.get(i.id,!0))&&this.items.splice(i,1))}return this.resize(),e},set:function(e,t){var i=this.get(e);return null!=i&&(p.extend(i,t),this.refresh(String(e).split(":")[0]),!0)},get:function(e,t){if(0===arguments.length){for(var i=[],s=0;s<this.items.length;s++)null!=this.items[s].id&&i.push(this.items[s].id);return i}for(var n=String(e).split(":"),l=0;l<this.items.length;l++){var o=this.items[l];if(-1!=["menu","menu-radio","menu-check"].indexOf(o.type)&&2==n.length&&o.id==n[0]){var a=o.items;"function"==typeof a&&(a=a(this));for(var r=0;r<a.length;r++){var d=a[r];if(d.id==n[1]||null==d.id&&d.text==n[1])return 1==t?r:d;if(Array.isArray(d.items))for(var u=0;u<d.items.length;u++)if(d.items[u].id==n[1]||null==d.items[u].id&&d.items[u].text==n[1])return 1==t?r:d.items[u]}}else if(o.id==n[0])return 1==t?l:o}return null},show:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&(e++,n.hidden=!1,i.push(String(arguments[s]).split(":")[0]))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e]);t.resize()},15),e},hide:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&(e++,n.hidden=!0,i.push(String(arguments[s]).split(":")[0]))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e]),t.tooltipHide(i[e]);t.resize()},15),e},enable:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&(e++,n.disabled=!1,i.push(String(arguments[s]).split(":")[0]))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e])},15),e},disable:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&(e++,n.disabled=!0,i.push(String(arguments[s]).split(":")[0]))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e]),t.tooltipHide(i[e])},15),e},check:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&-1==String(arguments[s]).indexOf(":")&&(e++,n.checked=!0,i.push(String(arguments[s]).split(":")[0]))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e])},15),e},uncheck:function(){for(var t=this,e=0,i=[],s=0;s<arguments.length;s++){var n=this.get(arguments[s]);n&&-1==String(arguments[s]).indexOf(":")&&(-1!=["menu","menu-radio","menu-check","drop","color","text-color"].indexOf(n.type)&&n.checked&&setTimeout(function(){p("#tb_"+t.name+"_item_"+w2utils.escapeId(n.id)).w2overlay({name:t.name,data:{"tb-item":n.id}})},1),e++,n.checked=!1,i.push(String(arguments[s]).split(":")[0]))}return setTimeout(function(){for(var e=0;e<i.length;e++)t.refresh(i[e])},15),e},click:function(e,t){var n=this,i=String(e).split(":"),l=this.get(i[0]),o=l&&l.items?w2obj.field.prototype.normMenu.call(this,l.items,l):[];if(1<i.length){var s=this.get(e);s&&!s.disabled&&n.menuClick({name:n.name,item:l,subItem:s,originalEvent:t})}else if(l&&!l.disabled){s=this.trigger({phase:"before",type:"click",target:null!=e?e:this.name,item:l,object:l,originalEvent:t});if(!0!==s.isCancelled){var a="#tb_"+this.name+"_item_"+w2utils.escapeId(l.id)+" table.w2ui-button";if(p(a).removeClass("down"),"radio"==l.type){for(var r=0;r<this.items.length;r++){var d=this.items[r];null!=d&&d.id!=l.id&&"radio"===d.type&&d.group==l.group&&d.checked&&(d.checked=!1,this.refresh(d.id))}l.checked=!0,p(a).addClass("checked")}if(-1!=["menu","menu-radio","menu-check","drop","color","text-color"].indexOf(l.type)&&(n.tooltipHide(e),l.checked?setTimeout(function(){p("#tb_"+n.name+"_item_"+w2utils.escapeId(l.id)).w2overlay({name:n.name,data:{"tb-item":l.id}}),l.checked=!1,n.refresh(l.id)},1):setTimeout(function(){var e=p("#tb_"+n.name+"_item_"+w2utils.escapeId(l.id));p.isPlainObject(l.overlay)||(l.overlay={});var t,i=(e.width()-50)/2;function s(){l.checked=!1,p(a).removeClass("checked")}19<i&&(i=19),"drop"==l.type&&e.w2overlay(l.html,p.extend({name:n.name,left:i,top:3,data:{"tb-item":l.id}},l.overlay,{onHide:function(e){s()}})),-1!=["menu","menu-radio","menu-check"].indexOf(l.type)&&(t="normal","menu-radio"==l.type&&(t="radio",o.forEach(function(e){l.selected==e.id?e.checked=!0:e.checked=!1})),"menu-check"==l.type&&(t="check",o.forEach(function(e){p.isArray(l.selected)&&-1!=l.selected.indexOf(e.id)?e.checked=!0:e.checked=!1})),e.w2menu(p.extend({name:n.name,items:o,left:i,top:3,data:{"tb-item":l.id}},l.overlay,{type:t,remove:function(e){n.menuClick({name:n.name,remove:!0,item:l,subItem:e.item,originalEvent:e.originalEvent,keepOpen:e.keepOpen})},select:function(e){n.menuClick({name:n.name,item:l,subItem:e.item,originalEvent:e.originalEvent,keepOpen:e.keepOpen})},onHide:function(e){s()}}))),-1!=["color","text-color"].indexOf(l.type)&&p(e).w2color(p.extend({color:l.color,onHide:function(e){s(),n._tmpColor&&n.colorClick({name:n.name,item:l,color:n._tmpColor,final:!0}),delete n._tmpColor},onSelect:function(e){null!=e&&(n.colorClick({name:n.name,item:l,color:e}),n._tmpColor=e)}},l.options))},1)),-1!=["check","menu","menu-radio","menu-check","drop","color","text-color"].indexOf(l.type)&&(l.checked=!l.checked,l.checked?p(a).addClass("checked"):p(a).removeClass("checked")),l.route){var u=String("/"+l.route).replace(/\/{2,}/g,"/"),c=w2utils.parseRoute(u);if(0<c.keys.length)for(var h=0;h<c.keys.length;h++)u=u.replace(new RegExp(":"+c.keys[h].name,"g"),this.routeData[c.keys[h].name]);setTimeout(function(){window.location.hash=u},1)}t&&-1!=["button","check","radio"].indexOf(l.type)&&this.tooltipShow(e,t,!0),this.trigger(p.extend(s,{phase:"after"}))}}},scroll:function(e){var t,i,s,n=p(this.box),l=this,o=n.find(".w2ui-scroll-wrapper"),a=o.scrollLeft();switch(e){case"left":t=o.outerWidth(),i=o.find(":first").outerWidth(),(s=a-t+50)<=0&&(s=0),o.animate({scrollLeft:s},300);break;case"right":t=o.outerWidth(),(i=o.find(":first").outerWidth())-t<=(s=a+t-50)&&(s=i-t),o.animate({scrollLeft:s},300)}setTimeout(function(){l.resize()},350)},render:function(e){var t=(new Date).getTime(),i=this.trigger({phase:"before",type:"render",target:this.name,box:e});if(!0!==i.isCancelled&&(null!=e&&(0<p(this.box).find("> table #tb_"+this.name+"_right").length&&p(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-toolbar").html(""),this.box=e),this.box)){for(var s='<div class="w2ui-scroll-wrapper" onmousedown="var el=w2ui[\''+this.name+'\']; if (el) el.resize();"><table cellspacing="0" cellpadding="0" width="100%"><tbody><tr>',n=0;n<this.items.length;n++){var l=this.items[n];null!=l&&(null==l.id&&(l.id="item_"+n),null!=l.caption&&console.log("NOTICE: toolbar item.caption property is deprecated, please use item.text. Item -> ",l),null!=l.hint&&console.log("NOTICE: toolbar item.hint property is deprecated, please use item.tooltip. Item -> ",l),"spacer"==l.type?s+='<td width="100%" id="tb_'+this.name+"_item_"+l.id+'" align="right"></td>':"new-line"==l.type?s+='<td width="100%"></td></tr></tbody></table><div class="w2ui-toolbar-new-line"></div><table cellspacing="0" cellpadding="0" width="100%"><tbody><tr>':s+='<td id="tb_'+this.name+"_item_"+l.id+'" style="'+(l.hidden?"display: none":"")+'"     class="'+(l.disabled?"disabled":"")+'" valign="middle"></td>')}return s+='<td width="100%" id="tb_'+this.name+'_right" align="right">'+this.right+"</td>",s+='</tr></tbody></table></div><div class="w2ui-scroll-left" onclick="var el=w2ui[\''+this.name+"']; if (el) el.scroll('left');\"></div><div class=\"w2ui-scroll-right\" onclick=\"var el=w2ui['"+this.name+"']; if (el) el.scroll('right');\"></div>",p(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-toolbar").html(s),0<p(this.box).length&&(p(this.box)[0].style.cssText+=this.style),this.refresh(),this.resize(),this.trigger(p.extend(i,{phase:"after"})),(new Date).getTime()-t}},refresh:function(e){var t=(new Date).getTime(),i=this.trigger({phase:"before",type:"refresh",target:null!=e?e:this.name,item:this.get(e)});if(!0!==i.isCancelled){if(null!=e){var s=this.get(e);if(null==s)return!1;if("function"==typeof s.onRefresh){var n=this.trigger({phase:"before",type:"refresh",target:e,item:s,object:s});if(!0===n.isCancelled)return}var l=p(this.box).find("#tb_"+this.name+"_item_"+w2utils.escapeId(s.id)),o=this.getItemHTML(s);return this.tooltipHide(e,{}),0===l.length?(o="spacer"==s.type?'<td width="100%" id="tb_'+this.name+"_item_"+s.id+'" align="right"></td>':'<td id="tb_'+this.name+"_item_"+s.id+'" style="'+(s.hidden?"display: none":"")+'"     class="'+(s.disabled?"disabled":"")+'" valign="middle">'+o+"</td>",(this.get(e,!0)==this.items.length-1?p(this.box).find("#tb_"+this.name+"_right"):p(this.box).find("#tb_"+this.name+"_item_"+w2utils.escapeId(this.items[parseInt(this.get(e,!0))+1].id))).before(o)):(-1==["menu","menu-radio","menu-check","drop","color","text-color"].indexOf(s.type)||0<(e=p("#w2ui-overlay-"+this.name)).length&&(0==s.checked?e[0].hide():-1!=["menu","menu-radio","menu-check"].indexOf(s.type)&&e.w2menu("refresh",{items:s.items})),l.html(o),s.hidden?l.css("display","none"):l.css("display",""),s.disabled?l.addClass("disabled"):l.removeClass("disabled")),"function"==typeof s.onRefresh&&this.trigger(p.extend(n,{phase:"after"})),this.trigger(p.extend(i,{phase:"after"})),(new Date).getTime()-t}for(var a=0;a<this.items.length;a++){var r=this.items[a];null==r.id&&(r.id="item_"+a),this.refresh(r.id)}}},resize:function(){var e=(new Date).getTime(),t=this.trigger({phase:"before",type:"resize",target:this.name});if(!0!==t.isCancelled){var i=p(this.box);i.find(".w2ui-scroll-left, .w2ui-scroll-right").hide();var s=i.find(".w2ui-scroll-wrapper");return s.find(":first").outerWidth()>s.outerWidth()&&(0<s.scrollLeft()&&i.find(".w2ui-scroll-left").show(),s.scrollLeft()<s.find(":first").outerWidth()-s.outerWidth()&&i.find(".w2ui-scroll-right").show()),this.trigger(p.extend(t,{phase:"after"})),(new Date).getTime()-e}},destroy:function(){var e=this.trigger({phase:"before",type:"destroy",target:this.name});!0!==e.isCancelled&&(0<p(this.box).find("> table #tb_"+this.name+"_right").length&&p(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-toolbar").html(""),p(this.box).html(""),delete w2ui[this.name],this.trigger(p.extend(e,{phase:"after"})))},getItemHTML:function(i){var e="";null!=i.caption&&null==i.text&&(i.text=i.caption),null==i.text&&(i.text=""),null==i.tooltip&&null!=i.hint&&(i.tooltip=i.hint),null==i.tooltip&&(i.tooltip=""),"function"==typeof i.get||!Array.isArray(i.items)&&"function"!=typeof i.items||(i.get=function(t){var e=i.items;return(e="function"==typeof e?i.items(i):e).find(function(e){return e.id==t})});var t="<td>&#160;</td>",s="function"==typeof i.text?i.text.call(this,i):i.text;if(i.img&&(t='<td><div class="w2ui-tb-image w2ui-icon '+i.img+'"></div></td>'),i.icon&&(t='<td><div class="w2ui-tb-image"><span class="'+("function"==typeof i.icon?i.icon.call(this,i):i.icon)+'"></span></div></td>'),""===e)switch(i.type){case"color":case"text-color":"string"==typeof i.color&&("#"==i.color.substr(0,1)&&(i.color=i.color.substr(1)),3!=i.color.length&&6!=i.color.length||(i.color="#"+i.color)),"color"==i.type&&(s='<div style="height: 12px; width: 12px; margin-top: 1px; border: 1px solid #8A8A8A; border-radius: 1px; box-shadow: 0px 0px 1px #fff;         background-color: '+(null!=i.color?i.color:"#fff")+'; float: left;"></div>'+(i.text?'<div style="margin-left: 17px;">'+w2utils.lang(i.text)+"</div>":"")),"text-color"==i.type&&(s='<div style="color: '+(null!=i.color?i.color:"#444")+';">'+(i.text?w2utils.lang(i.text):"<b>Aa</b>")+"</div>");case"menu":case"menu-check":case"menu-radio":case"button":case"check":case"radio":case"drop":e+='<table cellpadding="0" cellspacing="0"        class="w2ui-button '+(i.checked?"checked":"")+" "+(i.class||"")+'"        onclick     = "var el=w2ui[\''+this.name+"']; if (el) el.click('"+i.id+'\', event);"        onmouseenter = "'+(i.disabled?"":"jQuery(this).addClass('over'); w2ui['"+this.name+"'].tooltipShow('"+i.id+"', event);")+'"       onmouseleave = "'+(i.disabled?"":"jQuery(this).removeClass('over').removeClass('down'); w2ui['"+this.name+"'].tooltipHide('"+i.id+"', event);")+'"       onmousedown = "'+(i.disabled?"":"jQuery(this).addClass('down');")+'"       onmouseup   = "'+(i.disabled?"":"jQuery(this).removeClass('down');")+'"><tbody><tr><td>  <table cellpadding="1" cellspacing="0"><tbody>  <tr>'+t+(""!==s?'<td class="w2ui-tb-text w2ui-tb-caption" nowrap="nowrap" style="'+(i.style||"")+'">'+w2utils.lang(s)+"</td>":"")+(null!=i.count?'<td class="w2ui-tb-count" nowrap="nowrap"><span>'+i.count+"</span></td>":"")+(-1!=["menu","menu-radio","menu-check","drop","color","text-color"].indexOf(i.type)&&!1!==i.arrow?'<td class="w2ui-tb-down" nowrap="nowrap"><div></div></td>':"")+"  </tr></tbody></table></td></tr></tbody></table>";break;case"break":e+='<table cellpadding="0" cellspacing="0"><tbody><tr>    <td><div class="w2ui-break">&#160;</div></td></tr></tbody></table>';break;case"html":e+='<table cellpadding="0" cellspacing="0"><tbody><tr>    <td nowrap="nowrap">'+("function"==typeof i.html?i.html.call(this,i):i.html)+"</td></tr></tbody></table>"}return"<div>"+e+"</div>"},tooltipShow:function(e,t,i){var s,n,l,o;null!=this.tooltip&&(s=p(this.box).find("#tb_"+this.name+"_item_"+w2utils.escapeId(e)),n=this.get(e),l=this.tooltip,"function"==typeof(o=n.tooltip)&&(o=o.call(this,n)),clearTimeout(this._tooltipTimer),this._tooltipTimer=setTimeout(function(){!0!==s.prop("_mouse_tooltip")&&(s.prop("_mouse_tooltip",!0),-1!=["menu","menu-radio","menu-check","drop","color","text-color"].indexOf(n.type)&&1==n.checked||s.w2tag(w2utils.lang(o),{position:l}))},0),s.prop("_mouse_tooltip")&&1==i&&s.w2tag(w2utils.lang(o),{position:l}))},tooltipHide:function(e,t){var i;null!=this.tooltip&&(i=p(this.box).find("#tb_"+this.name+"_item_"+w2utils.escapeId(e)),this.get(e),clearTimeout(this._tooltipTimer),setTimeout(function(){!0===i.prop("_mouse_tooltip")&&(i.removeProp("_mouse_tooltip"),i.w2tag())},1))},menuClick:function(e){if(e.item&&!e.item.disabled){var t=this.trigger({phase:"before",type:!0!==e.remove?"click":"remove",target:e.item.id+":"+e.subItem.id,item:e.item,subItem:e.subItem,originalEvent:e.originalEvent});if(!0!==t.isCancelled){var s,i,n=e.subItem,l=this.get(e.item.id),o=l.items;if("function"==typeof o&&(o=l.items()),"menu-radio"==l.type&&(l.selected=n.id,Array.isArray(o)&&o.forEach(function(e){!0===e.checked&&delete e.checked,Array.isArray(e.items)&&e.items.forEach(function(e){!0===e.checked&&delete e.checked})}),n.checked=!0),"menu-check"==l.type&&(p.isArray(l.selected)||(l.selected=[]),null==n.group?-1==(i=l.selected.indexOf(n.id))?(l.selected.push(n.id),n.checked=!0):(l.selected.splice(i,1),n.checked=!1):!1===n.group||(s=[],function i(e){e.forEach(function(e){var t;e.group!==n.group||-1!=(t=l.selected.indexOf(e.id))&&(e.id!=n.id&&s.push(e.id),l.selected.splice(t,1)),Array.isArray(e.items)&&i(e.items)})}(o),-1==(i=l.selected.indexOf(n.id))&&(l.selected.push(n.id),n.checked=!0))),"string"==typeof n.route){var a=""!==n.route?String("/"+n.route).replace(/\/{2,}/g,"/"):"",r=w2utils.parseRoute(a);if(0<r.keys.length)for(var d=0;d<r.keys.length;d++)null!=this.routeData[r.keys[d].name]&&(a=a.replace(new RegExp(":"+r.keys[d].name,"g"),this.routeData[r.keys[d].name]));setTimeout(function(){window.location.hash=a},1)}this.refresh(e.item.id),this.trigger(p.extend(t,{phase:"after"}))}}},colorClick:function(e){var t;!e.item||e.item.disabled||!0!==(t=this.trigger({phase:"before",type:"click",target:e.item.id,item:e.item,color:e.color,final:e.final,originalEvent:e.originalEvent})).isCancelled&&(e.item.color=e.color,this.refresh(e.item.id),this.trigger(p.extend(t,{phase:"after"})))}},p.extend(r.prototype,w2utils.event),w2obj.toolbar=r}(jQuery),function(h){function d(e){this.name=null,this.box=null,this.sidebar=null,this.parent=null,this.nodes=[],this.menu=[],this.routeData={},this.selected=null,this.img=null,this.icon=null,this.style="",this.topHTML="",this.bottomHTML="",this.flatButton=!1,this.keyboard=!0,this.flat=!1,this.hasFocus=!1,h.extend(!0,this,w2obj.sidebar,e)}h.fn.w2sidebar=function(e){if(!h.isPlainObject(e)){var t=w2ui[h(this).attr("name")];return t?0<arguments.length?(t[e]&&t[e].apply(t,Array.prototype.slice.call(arguments,1)),this):t:null}if(w2utils.checkName(e,"w2sidebar")){var i=e.nodes,t=new d(e);return h.extend(t,{handlers:[],nodes:[]}),null!=i&&t.add(t,i),t.sidebar=t,w2ui[t.name]=t,0!==h(this).length&&t.render(h(this)[0]),t}},d.prototype={levelPadding:12,handle:{size:0,style:"",content:""},onClick:null,onDblClick:null,onContextMenu:null,onMenuClick:null,onExpand:null,onCollapse:null,onKeydown:null,onRender:null,onRefresh:null,onResize:null,onDestroy:null,onFocus:null,onBlur:null,onFlat:null,node:{id:null,text:"",count:null,img:null,icon:null,nodes:[],style:"",route:null,selected:!1,expanded:!1,hidden:!1,disabled:!1,group:!1,groupShowHide:!0,collapsible:!1,plus:!1,onClick:null,onDblClick:null,onContextMenu:null,onExpand:null,onCollapse:null,parent:null,sidebar:null},add:function(e,t){return 1==arguments.length&&(t=arguments[0],e=this),"string"==typeof e&&(e=this.get(e)),this.insert(e,null,t)},insert:function(e,t,i){var s,n,l,o,a;if(2==arguments.length)if(i=arguments[1],null!=(t=arguments[0])){if(null==(n=this.get(t)))return null!=(i=!h.isArray(i)?[i]:i)[0].caption&&null==i[0].text&&(console.log("NOTICE: sidebar node.caption property is deprecated, please use node.text. Node -> ",i[0]),i[0].text=i[0].caption),s=i[0].text,console.log('ERROR: Cannot insert node "'+s+'" because cannot find node "'+t+'" to insert before.'),null;e=this.get(t).parent}else e=this;"string"==typeof e&&(e=this.get(e)),h.isArray(i)||(i=[i]);for(var r=0;r<i.length;r++)if(null!=typeof(o=i[r]).id)if(null==this.get(this,o.id)){if((l=h.extend({},d.prototype.node,o)).sidebar=this,l.parent=e,a=l.nodes||[],l.nodes=[],null==t)e.nodes.push(l);else{if(null==(n=this.get(e,t,!0)))return console.log('ERROR: Cannot insert node "'+o.text+'" because cannot find node "'+t+'" to insert before.'),null;e.nodes.splice(n,0,l)}0<a.length&&this.insert(l,null,a)}else console.log("ERROR: Cannot insert node with id="+o.id+" (text: "+o.text+") because another node with the same id already exists.");else null!=o.caption&&null==o.text&&(console.log("NOTICE: sidebar node.caption property is deprecated, please use node.text"),o.text=o.caption),s=o.text,console.log('ERROR: Cannot insert node "'+s+'" because it has no id.');return this.refresh(e.id),l},remove:function(){for(var e,t,i=0,s=0;s<arguments.length;s++)null!=(t=this.get(arguments[s]))&&(null!=this.selected&&this.selected===t.id&&(this.selected=null),null!=(e=this.get(t.parent,arguments[s],!0))&&(t.parent.nodes[e].selected&&t.sidebar.unselect(t.id),t.parent.nodes.splice(e,1),i++));return 0<i&&1==arguments.length?this.refresh(t.parent.id):this.refresh(),i},set:function(e,t,i){if(2==arguments.length&&(i=t,t=e,e=this),null==(e="string"==typeof e?this.get(e):e).nodes)return null;for(var s=0;s<e.nodes.length;s++){if(e.nodes[s].id===t){var n=this.update(t,i);return 0!=Object.keys(n).length&&(n=i.nodes,h.extend(e.nodes[s],i,{nodes:[]}),null!=n&&this.add(e.nodes[s],n),this.refresh(t)),!0}if(this.set(e.nodes[s],t,i))return!0}return!1},get:function(e,t,i){if(0===arguments.length){for(var s=[],n=this.find({}),l=0;l<n.length;l++)null!=n[l].id&&s.push(n[l].id);return s}if((1==arguments.length||2==arguments.length&&!0===t)&&(i=t,t=e,e=this),null==(e="string"==typeof e?this.get(e):e).nodes)return null;for(var o=0;o<e.nodes.length;o++){if(e.nodes[o].id==t)return!0===i?o:e.nodes[o];var a=this.get(e.nodes[o],t,i);if(a||0===a)return a}return null},find:function(e,t,i){if(1==arguments.length&&(t=e,e=this),i=i||[],null==(e="string"==typeof e?this.get(e):e).nodes)return i;for(var s=0;s<e.nodes.length;s++){var n,l=!0;for(n in t)e.nodes[s][n]!=t[n]&&(l=!1);l&&i.push(e.nodes[s]),0<e.nodes[s].nodes.length&&(i=this.find(e.nodes[s],t,i))}return i},hide:function(){for(var e=0,t=0;t<arguments.length;t++){var i=this.get(arguments[t]);null!=i&&!0!==i.hidden&&(i.hidden=!0,e++)}return 0<e&&(1==arguments.length?this.refresh(arguments[0]):this.refresh()),e},show:function(){for(var e=0,t=0;t<arguments.length;t++){var i=this.get(arguments[t]);null!=i&&!1!==i.hidden&&(i.hidden=!1,e++)}return 0<e&&(1==arguments.length?this.refresh(arguments[0]):this.refresh()),e},disable:function(){for(var e=0,t=0;t<arguments.length;t++){var i=this.get(arguments[t]);null!=i&&!0!==i.disabled&&(i.disabled=!0,i.selected&&this.unselect(i.id),e++)}return 0<e&&(1==arguments.length?this.refresh(arguments[0]):this.refresh()),e},enable:function(){for(var e=0,t=0;t<arguments.length;t++){var i=this.get(arguments[t]);null!=i&&!1!==i.disabled&&(i.disabled=!1,e++)}return 0<e&&(1==arguments.length?this.refresh(arguments[0]):this.refresh()),e},select:function(e){var t=this.get(e);if(!t)return!1;if(this.selected==e&&t.selected)return!1;this.unselect(this.selected);var i=h(this.box).find("#node_"+w2utils.escapeId(e));return i.addClass("w2ui-selected").find(".w2ui-icon").addClass("w2ui-icon-selected"),0<i.length&&this.scrollIntoView(e,!0),t.selected=!0,this.selected=e,!0},unselect:function(e){0===arguments.length&&(e=this.selected);var t=this.get(e);return!!t&&(t.selected=!1,h(this.box).find("#node_"+w2utils.escapeId(e)).removeClass("w2ui-selected").find(".w2ui-icon").removeClass("w2ui-icon-selected"),this.selected==e&&(this.selected=null),!0)},toggle:function(e){var t=this.get(e);return null!=t&&(t.plus?(this.set(e,{plus:!1}),this.expand(e),void this.refresh(e)):0!==t.nodes.length&&(!!t.collapsible&&(this.get(e).expanded?this.collapse(e):this.expand(e))))},collapse:function(e){var t=this,i=this.get(e);if(null==i)return!1;var s=this.trigger({phase:"before",type:"collapse",target:e,object:i});return!0!==s.isCancelled?(h(this.box).find("#node_"+w2utils.escapeId(e)+"_sub").slideUp(200),h(this.box).find("#node_"+w2utils.escapeId(e)+" .w2ui-expanded").removeClass("w2ui-expanded").addClass("w2ui-collapsed"),i.expanded=!1,this.trigger(h.extend(s,{phase:"after"})),setTimeout(function(){t.refresh(e)},200),!0):void 0},collapseAll:function(e){if(null==(e="string"==typeof(e=null==e?this:e)?this.get(e):e).nodes)return!1;for(var t=0;t<e.nodes.length;t++)!0===e.nodes[t].expanded&&(e.nodes[t].expanded=!1),e.nodes[t].nodes&&0<e.nodes[t].nodes.length&&this.collapseAll(e.nodes[t]);return this.refresh(e.id),!0},expand:function(e){var t=this,i=this.get(e),s=this.trigger({phase:"before",type:"expand",target:e,object:i});if(!0!==s.isCancelled)return h(this.box).find("#node_"+w2utils.escapeId(e)+"_sub").slideDown(200),h(this.box).find("#node_"+w2utils.escapeId(e)+" .w2ui-collapsed").removeClass("w2ui-collapsed").addClass("w2ui-expanded"),i.expanded=!0,this.trigger(h.extend(s,{phase:"after"})),setTimeout(function(){t.refresh(e)},200),!0},expandAll:function(e){if(null==(e="string"==typeof(e=null==e?this:e)?this.get(e):e).nodes)return!1;for(var t=0;t<e.nodes.length;t++)!1===e.nodes[t].expanded&&(e.nodes[t].expanded=!0),e.nodes[t].nodes&&0<e.nodes[t].nodes.length&&this.expandAll(e.nodes[t]);this.refresh(e.id)},expandParents:function(e){e=this.get(e);return null!=e&&(e.parent&&(e.parent.expanded||(e.parent.expanded=!0,this.refresh(e.parent.id)),this.expandParents(e.parent.id)),!0)},click:function(n,l){var o,a,r=this,d=this.get(n);null!=d&&(d.disabled||d.group||(h(r.box).find(".w2ui-node.w2ui-selected").each(function(e,t){var i=h(t).attr("id").replace("node_",""),i=r.get(i);null!=i&&(i.selected=!1),h(t).removeClass("w2ui-selected").find(".w2ui-icon").removeClass("w2ui-icon-selected")}),o=h(r.box).find("#node_"+w2utils.escapeId(n)),a=h(r.box).find("#node_"+w2utils.escapeId(r.selected)),o.addClass("w2ui-selected").find(".w2ui-icon").addClass("w2ui-icon-selected"),setTimeout(function(){var e=r.trigger({phase:"before",type:"click",target:n,originalEvent:l,node:d,object:d});if(!0===e.isCancelled)return o.removeClass("w2ui-selected").find(".w2ui-icon").removeClass("w2ui-icon-selected"),void a.addClass("w2ui-selected").find(".w2ui-icon").addClass("w2ui-icon-selected");if(null!=a&&(a.selected=!1),r.get(n).selected=!0,r.selected=n,"string"==typeof d.route){var t=""!==d.route?String("/"+d.route).replace(/\/{2,}/g,"/"):"",i=w2utils.parseRoute(t);if(0<i.keys.length)for(var s=0;s<i.keys.length;s++)null!=r.routeData[i.keys[s].name]&&(t=t.replace(new RegExp(":"+i.keys[s].name,"g"),r.routeData[i.keys[s].name]));setTimeout(function(){window.location.hash=t},1)}r.trigger(h.extend(e,{phase:"after"}))},1)))},focus:function(e){var t=this,e=this.trigger({phase:"before",type:"focus",target:this.name,originalEvent:e});if(!0===e.isCancelled)return!1;this.hasFocus=!0,h(this.box).find(".w2ui-sidebar-body").addClass("w2ui-focus"),setTimeout(function(){var e=h(t.box).find("#sidebar_"+t.name+"_focus");e.is(":focus")||e.focus()},10),this.trigger(h.extend(e,{phase:"after"}))},blur:function(e){e=this.trigger({phase:"before",type:"blur",target:this.name,originalEvent:e});if(!0===e.isCancelled)return!1;this.hasFocus=!1,h(this.box).find(".w2ui-sidebar-body").removeClass("w2ui-focus"),this.trigger(h.extend(e,{phase:"after"}))},keydown:function(e){var t,o=this,i=o.get(o.selected);function s(e,t){null==e||e.hidden||e.disabled||e.group||(o.click(e.id,t),setTimeout(function(){o.scrollIntoView()},50))}function n(e,t){for(e=t(e);null!=e&&(e.hidden||e.disabled)&&!e.group;)e=t(e);return e}function l(e){if(null==e)return null;var t=e.parent,e=o.get(e.id,!0),t=0<e?function e(t){if(t.expanded&&0<t.nodes.length){var i=t.nodes[t.nodes.length-1];return(i.hidden||i.disabled||i.group?l:e)(i)}return t}(t.nodes[e-1]):t;return t=null!=t&&(t.hidden||t.disabled||t.group)?l(t):t}!0===o.keyboard&&(i=i||o.nodes[0],!0!==(t=o.trigger({phase:"before",type:"keydown",target:o.name,originalEvent:e})).isCancelled&&(13!=e.keyCode&&32!=e.keyCode||0<i.nodes.length&&o.toggle(o.selected),37==e.keyCode&&(0<i.nodes.length&&i.expanded?o.collapse(o.selected):(s(i.parent),i.parent.group||o.collapse(i.parent.id))),39==e.keyCode&&(0<i.nodes.length||i.plus)&&!i.expanded&&o.expand(o.selected),38==e.keyCode&&(null==o.get(o.selected)?s(this.nodes[0]||null):s(n(i,l))),40==e.keyCode&&(null==o.get(o.selected)?s(this.nodes[0]||null):s(n(i,function e(t,i){if(null==t)return null;var s=t.parent;var n=o.get(t.id,!0);var l=null;l=t.expanded&&0<t.nodes.length&&!0!==i?(t=t.nodes[0],t.hidden||t.disabled||t.group?e(t):t):s&&n+1<s.nodes.length?s.nodes[n+1]:e(s,!0);null!=l&&(l.hidden||l.disabled||l.group)&&(l=e(l));return l}))),-1!=h.inArray(e.keyCode,[13,32,37,38,39,40])&&(e.preventDefault&&e.preventDefault(),e.stopPropagation&&e.stopPropagation()),o.trigger(h.extend(t,{phase:"after"}))))},scrollIntoView:function(e,t){var i,s;null==e&&(e=this.selected),null!=this.get(e)&&(i=h(this.box).find(".w2ui-sidebar-body"),((e=(s=h(this.box).find("#node_"+w2utils.escapeId(e))).offset().top-i.offset().top)+s.height()>i.height()||e<=0)&&i.animate({scrollTop:i.scrollTop()+e-i.height()/2+s.height()},t?0:250,"linear"))},dblClick:function(e,t){var i=this.get(e),i=this.trigger({phase:"before",type:"dblClick",target:e,originalEvent:t,object:i});!0!==i.isCancelled&&(this.toggle(e),this.trigger(h.extend(i,{phase:"after"})))},contextMenu:function(t,e){var i=this,s=i.get(t);t!=i.selected&&i.click(t);var n=i.trigger({phase:"before",type:"contextMenu",target:t,originalEvent:e,object:s,allowOnDisabled:!1});!0!==n.isCancelled&&(s.disabled&&!n.allowOnDisabled||(0<i.menu.length&&h(i.box).find("#node_"+w2utils.escapeId(t)).w2menu({items:i.menu,contextMenu:!0,originalEvent:e,onSelect:function(e){i.menuClick(t,parseInt(e.index),e.originalEvent)}}),e.preventDefault&&e.preventDefault(),i.trigger(h.extend(n,{phase:"after"}))))},menuClick:function(e,t,i){t=this.trigger({phase:"before",type:"menuClick",target:e,originalEvent:i,menuIndex:t,menuItem:this.menu[t]});!0!==t.isCancelled&&this.trigger(h.extend(t,{phase:"after"}))},goFlat:function(){var e=this.trigger({phase:"before",type:"flat",goFlat:!this.flat});!0!==e.isCancelled&&(this.flat=!this.flat,this.refresh(),this.trigger(h.extend(e,{phase:"after"})))},render:function(e){var t=(new Date).getTime(),s=this,i=this.trigger({phase:"before",type:"render",target:this.name,box:e});if(!0!==i.isCancelled&&(null!=e&&(0<h(this.box).find("> div > div.w2ui-sidebar-body").length&&h(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-sidebar").html(""),this.box=e),this.box)){h(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-sidebar").html('<div><input id="sidebar_'+this.name+'_focus" style="position: absolute; top: 0; right: 0; width: 1px; z-index: -1; opacity: 0" '+(w2utils.isIOS?"readonly":"")+'/><div class="w2ui-sidebar-top"></div><div class="w2ui-sidebar-body"></div><div class="w2ui-sidebar-bottom"></div></div>'),h(this.box).find("> div").css({width:h(this.box).width()+"px",height:h(this.box).height()+"px"}),0<h(this.box).length&&(h(this.box)[0].style.cssText+=this.style);var n,e="";return 1==this.flatButton&&(e='<div class="w2ui-flat-'+(this.flat?"right":"left")+'" onclick="w2ui[\''+this.name+"'].goFlat()\"></div>"),""===this.topHTML&&""===e||(h(this.box).find(".w2ui-sidebar-top").html(this.topHTML+e),h(this.box).find(".w2ui-sidebar-body").css("top",h(this.box).find(".w2ui-sidebar-top").height()+"px")),""!==this.bottomHTML&&(h(this.box).find(".w2ui-sidebar-bottom").html(this.bottomHTML),h(this.box).find(".w2ui-sidebar-body").css("bottom",h(this.box).find(".w2ui-sidebar-bottom").height()+"px")),h(this.box).find("#sidebar_"+this.name+"_focus").on("focus",function(e){clearTimeout(n),s.hasFocus||s.focus(e)}).on("blur",function(e){n=setTimeout(function(){s.hasFocus&&s.blur(e)},100)}).on("keydown",function(e){9!=e.keyCode&&w2ui[s.name].keydown.call(w2ui[s.name],e)}),h(this.box).off("mousedown").on("mousedown",function(i){setTimeout(function(){var e,t;-1==["INPUT","TEXTAREA","SELECT"].indexOf(i.target.tagName.toUpperCase())&&((e=h(s.box).find("#sidebar_"+s.name+"_focus")).is(":focus")||(h(i.target).hasClass("w2ui-node")&&(t=h(i.target).position().top+h(s.box).find(".w2ui-sidebar-top").height()+i.offsetY,e.css({top:t+"px",left:"0px"})),e.focus()))},1)}),this.trigger(h.extend(i,{phase:"after"})),this.refresh(),(new Date).getTime()-t}},update:function(e,t){var i,s,n=this.get(e);return n&&(s=h(this.box).find("#node_"+n.id),n.group?(t.text&&(n.text=t.text,s.find(".w2ui-group-text").replaceWith("function"==typeof n.text?n.text.call(this,n):'<span class="w2ui-group-text">'+n.text+"</span>"),delete t.text),t.class&&(n.class=t.class,i=s.data("level"),s[0].className="w2ui-node-group w2ui-level-"+i+(n.class?" "+n.class:""),delete t.class),t.style&&(n.style=t.style,s.next()[0].style=n.style+";"+(!n.hidden&&n.expanded?"":"display: none;"),delete t.style)):(!t.icon||0<(e=s.find(".w2ui-node-image > span")).length&&(n.icon=t.icon,e[0].className="function"==typeof n.icon?n.icon.call(this,n):n.icon,delete t.icon),t.count&&(n.count=t.count,s.find(".w2ui-node-count").html(n.count),0<s.find(".w2ui-node-count").length&&delete t.count),t.class&&0<s.length&&(n.class=t.class,i=s.data("level"),s[0].className="w2ui-node w2ui-level-"+i+(n.selected?" w2ui-selected":"")+(n.disabled?" w2ui-disabled":"")+(n.class?" "+n.class:""),delete t.class),t.text&&(n.text=t.text,s.find(".w2ui-node-text").html("function"==typeof n.text?n.text.call(this,n):n.text),delete t.text),t.style&&0<s.length&&(s=s.find(".w2ui-node-text"),n.style=t.style,s[0].style=n.style,delete t.style))),t},refresh:function(e){var t=(new Date).getTime(),i=this.trigger({phase:"before",type:"refresh",target:null!=e?e:this.name,fullRefresh:null==e});if(!0!==i.isCancelled){var s="";1==this.flatButton&&(s='<div class="w2ui-flat-'+(this.flat?"right":"left")+'" onclick="w2ui[\''+this.name+"'].goFlat()\"></div>"),""===this.topHTML&&""===s||(h(this.box).find(".w2ui-sidebar-top").html(this.topHTML+s),h(this.box).find(".w2ui-sidebar-body").css("top",h(this.box).find(".w2ui-sidebar-top").height()+"px")),""!==this.bottomHTML&&(h(this.box).find(".w2ui-sidebar-bottom").html(this.bottomHTML),h(this.box).find(".w2ui-sidebar-body").css("bottom",h(this.box).find(".w2ui-sidebar-bottom").height()+"px")),h(this.box).find("> div").removeClass("w2ui-sidebar-flat").addClass(this.flat?"w2ui-sidebar-flat":"").css({width:h(this.box).width()+"px",height:h(this.box).height()+"px"}),0<this.nodes.length&&null==this.nodes[0].parent&&(r=this.nodes,this.nodes=[],this.add(this,r));var n,l,o,a=this;if(null==e)n=this,l=".w2ui-sidebar-body";else{if(null==(n=this.get(e)))return;l="#node_"+w2utils.escapeId(n.id)+"_sub"}n!==this&&(r="#node_"+w2utils.escapeId(n.id),o=c(n),h(this.box).find(r).before('<div id="sidebar_'+this.name+'_tmp"></div>'),h(this.box).find(r).remove(),h(this.box).find(l).remove(),h("#sidebar_"+this.name+"_tmp").before(o),h("#sidebar_"+this.name+"_tmp").remove());var r={top:h(this.box).find(l).scrollTop(),left:h(this.box).find(l).scrollLeft()};h(this.box).find(l).html("");for(var d=0;d<n.nodes.length;d++)if(o=c(u=n.nodes[d]),h(this.box).find(l).append(o),0!==u.nodes.length)this.refresh(u.id);else{var u=this.trigger({phase:"before",type:"refresh",target:u.id});if(!0===u.isCancelled)return;this.trigger(h.extend(u,{phase:"after"}))}return h(this.box).find(l).scrollLeft(r.left).scrollTop(r.top),this.trigger(h.extend(i,{phase:"after"})),(new Date).getTime()-t}function c(e){var t="",i=e.img,s=e.icon;null==s&&null==i&&(null==s&&(s=a.icon),null==i&&(i=a.img));for(var n,l=e.parent,o=0;l&&null!=l.parent;)l=l.parent,o++;return null!=e.caption&&null==e.text&&(e.text=e.caption),null!=e.caption&&(console.log("NOTICE: sidebar node.caption property is deprecated, please use node.text. Node -> ",e),e.text=e.caption),Array.isArray(e.nodes)&&0<e.nodes.length&&(e.collapsible=!0),e.group?(t='<div class="w2ui-node-group w2ui-level-'+o+(e.class?" "+e.class:"")+'" id="node_'+e.id+'" data-level="'+o+'"   style="'+(e.hidden?"display: none":"")+'" onclick="w2ui[\''+a.name+"'].toggle('"+e.id+"')\"   oncontextmenu=\"w2ui['"+a.name+"'].contextMenu('"+e.id+"', event);\"   onmouseout=\"jQuery(this).find('span:nth-child(1)').css('color', 'transparent')\"    onmouseover=\"jQuery(this).find('span:nth-child(1)').css('color', 'inherit')\">"+(e.groupShowHide&&e.collapsible?"<span>"+(!e.hidden&&e.expanded?w2utils.lang("Hide"):w2utils.lang("Show"))+"</span>":"<span></span>")+("function"==typeof e.text?e.text.call(a,e):'<span class="w2ui-group-text">'+e.text+"</span>")+'</div><div class="w2ui-node-sub" id="node_'+e.id+'_sub" style="'+e.style+";"+(!e.hidden&&e.expanded?"":"display: none;")+'"></div>',a.flat&&(t='<div class="w2ui-node-group" id="node_'+e.id+'"><span>&#160;</span></div><div id="node_'+e.id+'_sub" style="'+e.style+";"+(!e.hidden&&e.expanded?"":"display: none;")+'"></div>')):(e.selected&&!e.disabled&&(a.selected=e.id),l="",i&&(l='<div class="w2ui-node-image w2ui-icon '+i+(e.selected&&!e.disabled?" w2ui-icon-selected":"")+'"></div>'),s&&(l='<div class="w2ui-node-image"><span class="'+("function"==typeof s?s.call(a,e):s)+'"></span></div>'),n=e.text,i="",s=null!=e.count?'<div class="w2ui-node-count">'+e.count+"</div>":"",!0===e.collapsible&&(i='<div class="w2ui-'+(e.expanded?"expanded":"collapsed")+'"><span></span></div>'),"function"==typeof e.text&&(n=e.text.call(a,e)),t='<div class="w2ui-node w2ui-level-'+o+(e.selected?" w2ui-selected":"")+(e.disabled?" w2ui-disabled":"")+(e.class?" "+e.class:"")+'"    id="node_'+e.id+'" data-level="'+o+'" style="position: relative; '+(e.hidden?"display: none;":"")+'"    ondblclick="w2ui[\''+a.name+"'].dblClick('"+e.id+"', event);\"    oncontextmenu=\"w2ui['"+a.name+"'].contextMenu('"+e.id+"', event);\"    onClick=\"w2ui['"+a.name+"'].click('"+e.id+"', event); \">"+(a.handle.content?'<div class="w2ui-node-handle" style="width: '+a.handle.size+"px; "+a.handle.style+'">'+a.handle.content+"</div>":"")+'   <div class="w2ui-node-data" style="margin-left:'+(o*a.levelPadding+a.handle.size)+'px">'+i+l+s+'<div class="w2ui-node-text w2ui-node-caption" style="'+(e.style||"")+'">'+n+'</div>   </div></div><div class="w2ui-node-sub" id="node_'+e.id+'_sub" style="'+e.style+";"+(!e.hidden&&e.expanded?"":"display: none;")+'"></div>',a.flat&&(t='<div class="w2ui-node w2ui-level-'+o+" "+(e.selected?"w2ui-selected":"")+" "+(e.disabled?"w2ui-disabled":"")+(e.class?" "+e.class:"")+'" id="node_'+e.id+'" style="'+(e.hidden?"display: none;":"")+"\"    onmouseover=\"jQuery(this).find('.w2ui-node-data').w2tag(w2utils.base64decode('"+w2utils.base64encode(n+(e.count||0===e.count?' - <span class="w2ui-node-count">'+e.count+"</span>":""))+"'),                { id: '"+e.id+"', left: -5 })\"    onmouseout=\"jQuery(this).find('.w2ui-node-data').w2tag(null, { id: '"+e.id+"' })\"    ondblclick=\"w2ui['"+a.name+"'].dblClick('"+e.id+"', event);\"    oncontextmenu=\"w2ui['"+a.name+"'].contextMenu('"+e.id+"', event);\"    onClick=\"w2ui['"+a.name+"'].click('"+e.id+'\', event); "><div class="w2ui-node-data w2ui-node-flat">'+l+'</div></div><div class="w2ui-node-sub" id="node_'+e.id+'_sub" style="'+e.style+";"+(!e.hidden&&e.expanded?"":"display: none;")+'"></div>')),t}},resize:function(){var e=(new Date).getTime(),t=this.trigger({phase:"before",type:"resize",target:this.name});if(!0!==t.isCancelled)return h(this.box).css("overflow","hidden"),h(this.box).find("> div").css({width:h(this.box).width()+"px",height:h(this.box).height()+"px"}),this.trigger(h.extend(t,{phase:"after"})),(new Date).getTime()-e},destroy:function(){var e=this.trigger({phase:"before",type:"destroy",target:this.name});!0!==e.isCancelled&&(0<h(this.box).find("> div > div.w2ui-sidebar-body").length&&h(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-sidebar").html(""),delete w2ui[this.name],this.trigger(h.extend(e,{phase:"after"})))},lock:function(e,t){var i=Array.prototype.slice.call(arguments,0);i.unshift(this.box),w2utils.lock.apply(window,i)},unlock:function(e){w2utils.unlock(this.box,e)}},h.extend(d.prototype,w2utils.event),w2obj.sidebar=d}(jQuery),function(b){function n(e){this.el=null,this.helpers={},this.type=e.type||"text",this.options=b.extend(!0,{},e),this.onSearch=e.onSearch||null,this.onRequest=e.onRequest||null,this.onLoad=e.onLoad||null,this.onError=e.onError||null,this.onClick=e.onClick||null,this.onAdd=e.onAdd||null,this.onNew=e.onNew||null,this.onRemove=e.onRemove||null,this.onMouseOver=e.onMouseOver||null,this.onMouseOut=e.onMouseOut||null,this.onIconClick=e.onIconClick||null,this.onScroll=e.onScroll||null,this.tmp={},delete this.options.type,delete this.options.onSearch,delete this.options.onRequest,delete this.options.onLoad,delete this.options.onError,delete this.options.onClick,delete this.options.onMouseOver,delete this.options.onMouseOut,delete this.options.onIconClick,delete this.options.onScroll,b.extend(!0,this,w2obj.field)}b.fn.w2field=function(s,e){if(0!==this.length)return 0===arguments.length?b(this).data("w2field"):((s="string"==typeof(s="string"==typeof s&&"object"==typeof e?b.extend(!0,{},e,{type:s}):s)&&null==e?{type:s}:s)&&(s.type=String(s.type).toLowerCase()),this.each(function(e,t){var i=b(t).data("w2field");return null==i?(i=new n(s),b.extend(i,{handlers:[]}),t&&(i.el=b(t)[0]),i.init(),b(t).data("w2field",i),i):(i.clear(),"clear"===s.type?void 0:(i=new n(s),b.extend(i,{handlers:[]}),t&&(i.el=b(t)[0]),i.init(),b(t).data("w2field",i),i))}));var t=n.prototype;return t[s]?t[s].apply(t,Array.prototype.slice.call(arguments,1)):void 0},n.prototype={custom:{},addType:function(e,t){return e=String(e).toLowerCase(),this.custom[e]=t,!0},removeType:function(e){return e=String(e).toLowerCase(),!!this.custom[e]&&(delete this.custom[e],!0)},init:function(){var e,t=this,i=this.options;if("function"!=typeof this.custom[this.type])if(-1!=["INPUT","TEXTAREA"].indexOf(this.el.tagName.toUpperCase())){switch(this.type){case"text":case"int":case"float":case"money":case"currency":case"percent":case"alphanumeric":case"bin":case"hex":e={min:null,max:null,step:1,autoFormat:!0,currencyPrefix:w2utils.settings.currencyPrefix,currencySuffix:w2utils.settings.currencySuffix,currencyPrecision:w2utils.settings.currencyPrecision,decimalSymbol:w2utils.settings.decimalSymbol,groupSymbol:w2utils.settings.groupSymbol,arrows:!1,keyboard:!0,precision:null,silent:!0,prefix:"",suffix:""},this.options=b.extend(!0,{},e,i),(i=this.options).numberRE=new RegExp("["+i.groupSymbol+"]","g"),i.moneyRE=new RegExp("["+i.currencyPrefix+i.currencySuffix+i.groupSymbol+"]","g"),i.percentRE=new RegExp("["+i.groupSymbol+"%]","g"),-1!==["text","alphanumeric","hex","bin"].indexOf(this.type)&&(i.arrows=!1,i.keyboard=!1),this.addPrefix(),this.addSuffix();break;case"color":e={prefix:"",suffix:'<div style="width: '+(parseInt(b(this.el).css("font-size"))||12)+'px">&#160;</div>',arrows:!1,keyboard:!1,advanced:null,transparent:!0},this.options=b.extend(!0,{},e,i),i=this.options,this.addPrefix(),this.addSuffix(),""!==b(this.el).val()&&setTimeout(function(){t.change()},1);break;case"date":e={format:w2utils.settings.dateFormat,keyboard:!0,silent:!0,start:"",end:"",blocked:{},colored:{},blockWeekDays:null},this.options=b.extend(!0,{},e,i),i=this.options,null==b(this.el).attr("placeholder")&&b(this.el).attr("placeholder",i.format);break;case"time":e={format:w2utils.settings.timeFormat,keyboard:!0,silent:!0,start:"",end:"",noMinutes:!1},this.options=b.extend(!0,{},e,i),i=this.options,null==b(this.el).attr("placeholder")&&b(this.el).attr("placeholder",i.format);break;case"datetime":e={format:w2utils.settings.dateFormat+" | "+w2utils.settings.timeFormat,keyboard:!0,silent:!0,start:"",end:"",blocked:[],colored:{},placeholder:null,btn_now:!0,noMinutes:!1},this.options=b.extend(!0,{},e,i),i=this.options,null==b(this.el).attr("placeholder")&&b(this.el).attr("placeholder",i.placeholder||i.format);break;case"list":case"combo":if(e={items:[],selected:{},url:null,recId:null,recText:null,method:null,interval:350,postData:{},minLength:1,cacheMax:250,maxDropHeight:350,maxDropWidth:null,match:"begins",silent:!0,icon:null,iconStyle:"",align:"both",altRows:!0,onSearch:null,onRequest:null,onLoad:null,onError:null,onIconClick:null,renderDrop:null,compare:null,filter:!0,prefix:"",suffix:"",openOnFocus:!1,markSearch:!1},"function"==typeof i.items&&(i._items_fun=i.items),i.items=w2obj.field.prototype.normMenu.call(this,i.items),"list"===this.type){if(e.openOnFocus=!0,b(this.el).addClass("w2ui-select"),!b.isPlainObject(i.selected)&&Array.isArray(i.items))for(var s=0;s<i.items.length;s++){var n=i.items[s];if(n&&n.id===i.selected){i.selected=b.extend(!0,{},n);break}}this.watchSize()}i=b.extend({},e,i),this.options=i,b.isPlainObject(i.selected)||(i.selected={}),b(this.el).data("selected",i.selected),i.url&&(i.items=[],this.request(0)),"list"===this.type&&this.addFocus(),this.addPrefix(),this.addSuffix(),setTimeout(function(){t.refresh()},10),b(this.el).attr("autocapitalize","off").attr("autocomplete","off").attr("autocorrect","off").attr("spellcheck","false"),null!=i.selected.text&&b(this.el).val(i.selected.text);break;case"enum":e={items:[],selected:[],max:0,url:null,recId:null,recText:null,interval:350,method:null,postData:{},minLength:1,cacheMax:250,maxWidth:250,maxHeight:350,maxDropHeight:350,maxDropWidth:null,match:"contains",silent:!0,align:"both",altRows:!0,openOnFocus:!1,markSearch:!0,renderDrop:null,renderItem:null,compare:null,filter:!0,style:"",onSearch:null,onRequest:null,onLoad:null,onError:null,onClick:null,onAdd:null,onNew:null,onRemove:null,onMouseOver:null,onMouseOut:null,onScroll:null},"function"==typeof(i=b.extend({},e,i,{suffix:""})).items&&(i._items_fun=i.items),i.items=w2obj.field.prototype.normMenu.call(this,i.items),i.selected=w2obj.field.prototype.normMenu.call(this,i.selected),this.options=i,b.isArray(i.selected)||(i.selected=[]),b(this.el).data("selected",i.selected),i.url&&(i.items=[],this.request(0)),this.addSuffix(),this.addMulti(),this.watchSize();break;case"file":e={selected:[],max:0,maxSize:0,maxFileSize:0,maxWidth:250,maxHeight:350,maxDropHeight:350,maxDropWidth:null,readContent:!0,silent:!0,align:"both",altRows:!0,renderItem:null,style:"",onClick:null,onAdd:null,onRemove:null,onMouseOver:null,onMouseOut:null},i=b.extend({},e,i),this.options=i,b.isArray(i.selected)||(i.selected=[]),b(this.el).data("selected",i.selected),null==b(this.el).attr("placeholder")&&b(this.el).attr("placeholder",w2utils.lang("Attach files by dragging and dropping or Click to Select")),this.addMulti(),this.watchSize()}this.tmp={onChange:function(e){t.change.call(t,e)},onClick:function(e){t.click.call(t,e)},onFocus:function(e){t.focus.call(t,e)},onBlur:function(e){t.blur.call(t,e)},onKeydown:function(e){t.keyDown.call(t,e)},onKeyup:function(e){t.keyUp.call(t,e)},onKeypress:function(e){t.keyPress.call(t,e)}},b(this.el).addClass("w2field w2ui-input").data("w2field",this).on("change.w2field",this.tmp.onChange).on("click.w2field",this.tmp.onClick).on("focus.w2field",this.tmp.onFocus).on("blur.w2field",this.tmp.onBlur).on("keydown.w2field",this.tmp.onKeydown).on("keyup.w2field",this.tmp.onKeyup).on("keypress.w2field",this.tmp.onKeypress).css(w2utils.cssPrefix("box-sizing","border-box")),this.change(b.Event("change"))}else console.log("ERROR: w2field could only be applied to INPUT or TEXTAREA.",this.el);else this.custom[this.type].call(this,i)},watchSize:function(){var e=this,t=b(e.el).data("tmp")||{};t.sizeTimer=setInterval(function(){0<b(e.el).parents("body").length?e.resize():clearInterval(t.sizeTimer)},200),b(e.el).data("tmp",t)},get:function(){var e=-1!==["list","enum","file"].indexOf(this.type)?b(this.el).data("selected"):b(this.el).val();return e},set:function(e,t){-1!==["list","enum","file"].indexOf(this.type)?("list"!==this.type&&t?(null==b(this.el).data("selected")&&b(this.el).data("selected",[]),b(this.el).data("selected").push(e),b(this.el).trigger("input").change()):(t="enum"===this.type?[e]:e,b(this.el).data("selected",t).trigger("input").change()),this.refresh()):b(this.el).val(e)},setIndex:function(e,t){if(-1!==["list","enum"].indexOf(this.type)){var i=this.options.items;if(i&&i[e])return"list"!==this.type&&t?(null==b(this.el).data("selected")&&b(this.el).data("selected",[]),b(this.el).data("selected").push(i[e]),b(this.el).trigger("input").change()):(e="enum"===this.type?[i[e]]:i[e],b(this.el).data("selected",e).trigger("input").change()),this.refresh(),!0}return!1},clear:function(){var e=this.options;-1!==["money","currency"].indexOf(this.type)&&b(this.el).val(b(this.el).val().replace(e.moneyRE,"")),"percent"===this.type&&b(this.el).val(b(this.el).val().replace(/%/g,"")),"list"===this.type&&b(this.el).removeClass("w2ui-select"),this.type="clear";e=b(this.el).data("tmp");if(this.tmp){for(var t in null!=e&&(b(this.el).height("auto"),e&&e["old-padding-left"]&&b(this.el).css("padding-left",e["old-padding-left"]),e&&e["old-padding-right"]&&b(this.el).css("padding-right",e["old-padding-right"]),e&&e["old-background-color"]&&b(this.el).css("background-color",e["old-background-color"]),e&&e["old-border-color"]&&b(this.el).css("border-color",e["old-border-color"]),clearInterval(e.sizeTimer)),b(this.el).val(this.clean(b(this.el).val())).removeClass("w2field").removeData().off(".w2field"),this.helpers)b(this.helpers[t]).remove();this.helpers={}}},refresh:function(){var o=this,i=this.options,a=b(this.el).data("selected"),e=(new Date).getTime();if(-1!==["list"].indexOf(this.type)&&(b(o.el).parent().css("white-space","nowrap"),o.helpers.prefix&&o.helpers.prefix.hide(),setTimeout(function(){var t;o.helpers.focus&&(!b.isEmptyObject(a)&&i.icon?i.prefix='<span class="w2ui-icon '+i.icon+'"style="cursor: pointer; font-size: 14px; display: inline-block; margin-top: -1px; color: #7F98AD;'+i.iconStyle+'"></span>':i.prefix="",o.addPrefix(),t=o.helpers.focus.find("input"),""===b(t).val()?(b(t).css("text-indent","-9999em").prev().css("opacity",0),b(o.el).val(a&&null!=a.text?w2utils.lang(a.text):"")):(b(t).css("text-indent",0).prev().css("opacity",1),b(o.el).val(""),setTimeout(function(){o.helpers.prefix&&o.helpers.prefix.hide();var e="position: absolute; opacity: 0; margin: 4px 0px 0px 2px; background-position: left !important;";i.icon?(b(t).css("margin-left","17px"),b(o.helpers.focus).find(".icon-search").attr("style",e+"width: 11px !important; opacity: 1; display: block")):(b(t).css("margin-left","0px"),b(o.helpers.focus).find(".icon-search").attr("style",e+"width: 0px !important; opacity: 0; display: none"))},1)),b(o.el).prop("readonly")||b(o.el).prop("disabled")?setTimeout(function(){b(o.helpers.prefix).css("opacity","0.6"),b(o.helpers.suffix).css("opacity","0.6")},1):setTimeout(function(){b(o.helpers.prefix).css("opacity","1"),b(o.helpers.suffix).css("opacity","1")},1))},1)),-1!==["enum","file"].indexOf(this.type)){var t="";if(a)for(var s=0;s<a.length;s++){var n=a[s],l="",l="function"==typeof i.renderItem?i.renderItem(n,s,'<div class="w2ui-list-remove" title="'+w2utils.lang("Remove")+'" index="'+s+'">&#160;&#160;</div>'):'<div class="w2ui-list-remove" title="'+w2utils.lang("Remove")+'" index="'+s+'">&#160;&#160;</div>'+("enum"===o.type?n.text:n.name+'<span class="file-size"> - '+w2utils.formatSize(n.size)+"</span>");t+='<li index="'+s+'" style="max-width: '+parseInt(i.maxWidth)+"px; "+(n.style||"")+'">'+l+"</li>"}var r=o.helpers.multi,d=r.find("ul");r.attr("style",r.attr("style")+";"+i.style),b(o.el).css("z-index","-1"),b(o.el).prop("readonly")||b(o.el).prop("disabled")?setTimeout(function(){r[0].scrollTop=0,r.addClass("w2ui-readonly").find("li").css("opacity","0.9").parent().find("li.nomouse").hide().find("input").prop("readonly",!0).parents("ul").find(".w2ui-list-remove").hide()},1):setTimeout(function(){r.removeClass("w2ui-readonly").find("li").css("opacity","1").parent().find("li.nomouse").show().find("input").prop("readonly",!1).parents("ul").find(".w2ui-list-remove").show()},1),r.find(".w2ui-enum-placeholder").remove(),d.find("li").not("li.nomouse").remove(),""!==t?d.prepend(t):null!=b(o.el).attr("placeholder")&&""===r.find("input").val()&&(u="padding-top: "+b(this.el).css("padding-top")+";padding-left: "+b(this.el).css("padding-left")+"; box-sizing: "+b(this.el).css("box-sizing")+"; line-height: "+b(this.el).css("line-height")+"; font-size: "+b(this.el).css("font-size")+"; font-family: "+b(this.el).css("font-family")+"; ",r.prepend('<div class="w2ui-enum-placeholder" style="'+u+'">'+b(o.el).attr("placeholder")+"</div>")),r.off("scroll.w2field").on("scroll.w2field",function(e){e=o.trigger({phase:"before",type:"scroll",target:o.el,originalEvent:e});!0!==e.isCancelled&&o.trigger(b.extend(e,{phase:"after"}))}).find("li").data("mouse","out").on("click",function(e){var t,i,s,n="LI"===e.target.tagName.toUpperCase()?e.target:b(e.target).parents("LI"),l=a[b(n).attr("index")];b(n).hasClass("nomouse")||(e.stopPropagation(),b(e.target).hasClass("w2ui-list-remove")?b(o.el).prop("readonly")||b(o.el).prop("disabled")||!0!==(t=o.trigger({phase:"before",type:"remove",target:o.el,originalEvent:e.originalEvent,item:l})).isCancelled&&(b().w2overlay(),a.splice(b(e.target).attr("index"),1),b(o.el).trigger("input").trigger("change"),b(e.target).parent().fadeOut("fast"),setTimeout(function(){o.refresh(),o.trigger(b.extend(t,{phase:"after"}))},300)):!0!==(t=o.trigger({phase:"before",type:"click",target:o.el,originalEvent:e.originalEvent,item:l})).isCancelled&&("file"===o.type&&(i="",/image/i.test(l.type)&&(i='<div style="padding: 3px;">    <img src="'+(l.content?"data:"+l.type+";base64,"+l.content:"")+'" style="max-width: 300px;"         onload="var w = jQuery(this).width(); var h = jQuery(this).height();             if (w < 300 & h < 300) return;             if (w >= h && w > 300) jQuery(this).width(300);            if (w < h && h > 300) jQuery(this).height(300);"        onerror="this.style.display = \'none\'"    ></div>'),s='style="padding: 3px"',i+='<div style="padding: 8px;">    <table cellpadding="2"><tbody>    <tr><td '+(e='style="padding: 3px; text-align: right; color: #777;"')+">"+w2utils.lang("Name")+":</td><td "+s+">"+l.name+"</td></tr>    <tr><td "+e+">"+w2utils.lang("Size")+":</td><td "+s+">"+w2utils.formatSize(l.size)+"</td></tr>    <tr><td "+e+">"+w2utils.lang("Type")+":</td><td "+s+'>        <span style="width: 200px; display: block-inline; overflow: hidden; text-overflow: ellipsis; white-space: nowrap="nowrap";">'+l.type+"</span>    </td></tr>    <tr><td "+e+">"+w2utils.lang("Modified")+":</td><td "+s+">"+w2utils.date(l.modified)+"</td></tr>    </tbody></table></div>",b("#w2ui-overlay").remove(),b(n).w2overlay(i)),o.trigger(b.extend(t,{phase:"after"}))))}).on("mouseover",function(e){var t="LI"===e.target.tagName.toUpperCase()?e.target:b(e.target).parents("LI");if(!b(t).hasClass("nomouse")){if("out"===b(t).data("mouse")){var i=a[b(e.target).attr("index")],i=o.trigger({phase:"before",type:"mouseOver",target:o.el,originalEvent:e.originalEvent,item:i});if(!0===i.isCancelled)return;o.trigger(b.extend(i,{phase:"after"}))}b(t).data("mouse","over")}}).on("mouseout",function(t){var i="LI"===t.target.tagName.toUpperCase()?t.target:b(t.target).parents("LI");b(i).hasClass("nomouse")||(b(i).data("mouse","leaving"),setTimeout(function(){var e;"leaving"===b(i).data("mouse")&&(b(i).data("mouse","out"),e=a[b(t.target).attr("index")],!0!==(e=o.trigger({phase:"before",type:"mouseOut",target:o.el,originalEvent:t.originalEvent,item:e})).isCancelled&&o.trigger(b.extend(e,{phase:"after"})))},0))}),b(this.el).height("auto");d=b(r).find("> div.w2ui-multi-items").height()+2*w2utils.getSize(r,"+height");(d=d<26?26:d)>i.maxHeight&&(d=i.maxHeight),0<r.length&&(r[0].scrollTop=1e3);var u=w2utils.getSize(b(this.el),"height")-2;d<u&&(d=u),b(r).css({height:d+"px",overflow:d==i.maxHeight?"auto":"hidden"}),d<i.maxHeight&&b(r).prop("scrollTop",0),b(this.el).css({height:d+0+"px"}),"enum"===o.type&&(d=o.helpers.multi.find("input")).width(8*(d.val().length+2)+"px")}return(new Date).getTime()-e},reset:function(){var e=this.type;this.clear(),this.type=e,this.init()},resize:function(){var e,t,i,s=this,n=b(s.el).width(),l=b(s.el).height();s.tmp.current_width==n&&0<l||(i=this.helpers.focus,e=this.helpers.multi,t=this.helpers.suffix,l=this.helpers.prefix,i&&i.width(b(s.el).width()),e&&(i=w2utils.getSize(s.el,"width")-parseInt(b(s.el).css("margin-left"),10)-parseInt(b(s.el).css("margin-right"),10),b(e).width(i)),t&&(s.options.suffix='<div class="arrow-down" style="margin-top: '+(parseInt(b(s.el).height())-6)/2+'px;"></div>',s.addSuffix()),l&&s.addPrefix(),s.tmp.current_width=n)},clean:function(e){if("number"==typeof e)return e;var t=this.options;return e=String(e).trim(),-1!==["int","float","money","currency","percent"].indexOf(this.type)&&("string"==typeof e&&(t.autoFormat&&-1!==["money","currency"].indexOf(this.type)&&(e=String(e).replace(t.moneyRE,"")),t.autoFormat&&"percent"===this.type&&(e=String(e).replace(t.percentRE,"")),e=(e=t.autoFormat&&-1!==["int","float"].indexOf(this.type)?String(e).replace(t.numberRE,""):e).replace(/\s+/g,"").replace(w2utils.settings.groupSymbol,"").replace(w2utils.settings.decimalSymbol,".")),parseFloat(e)==e&&(null!=t.min&&e<t.min&&(e=t.min,b(this.el).val(t.min)),null!=t.max&&e>t.max&&(e=t.max,b(this.el).val(t.max))),e=""!==e&&w2utils.isFloat(e)?Number(e):""),e},format:function(e){var t=this.options;if(t.autoFormat&&""!==e)switch(this.type){case"money":case"currency":""!==(e=w2utils.formatNumber(e,t.currencyPrecision,t.groupSymbol))&&(e=t.currencyPrefix+e+t.currencySuffix);break;case"percent":""!==(e=w2utils.formatNumber(e,t.precision,t.groupSymbol))&&(e+="%");break;case"float":e=w2utils.formatNumber(e,t.precision,t.groupSymbol);break;case"int":e=w2utils.formatNumber(e,0,t.groupSymbol)}return e},change:function(e){var t,i=this,s=i.options;if(-1!==["int","float","money","currency","percent"].indexOf(this.type)){var n=b(this.el).val(),l=this.format(this.clean(b(this.el).val()));if(""!==n&&n!=l)return b(this.el).val(l).trigger("input").change(),e.stopPropagation(),e.preventDefault(),!1}"color"===this.type&&("rgb"!==(t=b(this.el).val()).substr(0,3).toLowerCase()&&(t="#"+t,8!==(e=b(this.el).val().length)&&6!==e&&3!==e&&(t="")),b(this.el).next().find("div").css("background-color",t),b(this.el).hasClass("has-focus")&&!0!==b(this.el).data("skipInit")&&this.updateOverlay()),-1!==["list","enum","file"].indexOf(this.type)&&(i.refresh(),setTimeout(function(){i.refresh()},5)),-1!==["date","time","datetime"].indexOf(this.type)&&(t=parseInt(i.el.value),w2utils.isInt(i.el.value)&&3e3<t&&("time"===this.type&&b(i.el).val(w2utils.formatTime(new Date(t),s.format)).trigger("input").change(),"date"===this.type&&b(i.el).val(w2utils.formatDate(new Date(t),s.format)).trigger("input").change(),"datetime"===this.type&&b(i.el).val(w2utils.formatDateTime(new Date(t),s.format)).trigger("input").change()))},click:function(e){e.stopPropagation(),-1!==["list","combo","enum"].indexOf(this.type)&&(b(this.el).hasClass("has-focus")||this.focus(e)),-1!==["date","time","color","datetime"].indexOf(this.type)&&this.updateOverlay()},focus:function(e){var t=this;if(b(t.el).addClass("has-focus"),-1!==["color","date","time","datetime"].indexOf(t.type)){if(b(t.el).prop("readonly")||b(t.el).prop("disabled"))return;0<b("#w2ui-overlay").length&&b("#w2ui-overlay")[0].hide(),setTimeout(function(){t.updateOverlay()},150)}if(-1!==["list","combo","enum"].indexOf(t.type)){if(b(t.el).prop("readonly")||b(t.el).prop("disabled"))return;0<b("#w2ui-overlay").length&&b("#w2ui-overlay")[0].hide(),t.resize(),setTimeout(function(){"list"===t.type&&b(t.el).is(":focus")?b(t.helpers.focus).find("input").focus():(t.search(),setTimeout(function(){t.updateOverlay()},1))},1),"function"==typeof t.options._items_fun&&(t.options.items=w2obj.field.prototype.normMenu.call(this,t.options._items_fun))}"file"===t.type&&b(t.helpers.multi).css({outline:"auto 5px #7DB4F3","outline-offset":"2px"})},blur:function(e){var t,i=this,s=i.options,n=b(i.el).val().trim(),l=b("#w2ui-overlay");b(i.el).removeClass("has-focus"),-1!==["color","date","time","list","combo","enum","datetime"].indexOf(i.type)&&(t=window.setTimeout(function(){!0!==l.data("keepOpen")&&l.hide()},0),b(".menu",l).one("focus",function(){clearTimeout(t),b(this).one("focusout",function(e){l.hide()})})),-1!==["int","float","money","currency","percent"].indexOf(i.type)&&(""===n||i.checkType(n)||(b(i.el).val("").trigger("input").change(),!1===s.silent&&(b(i.el).w2tag("Not a valid number"),setTimeout(function(){b(i.el).w2tag("")},3e3)))),-1!==["date","time","datetime"].indexOf(i.type)&&(""===n||i.inRange(i.el.value)?"date"!==i.type||""===n||w2utils.isDate(i.el.value,s.format)?"time"!==i.type||""===n||w2utils.isTime(i.el.value)?"datetime"!==i.type||""===n||w2utils.isDateTime(i.el.value,s.format)||(b(i.el).val("").removeData("selected").trigger("input").change(),!1===s.silent&&(b(i.el).w2tag("Not a valid date"),setTimeout(function(){b(i.el).w2tag("")},3e3))):(b(i.el).val("").removeData("selected").trigger("input").change(),!1===s.silent&&(b(i.el).w2tag("Not a valid time"),setTimeout(function(){b(i.el).w2tag("")},3e3))):(b(i.el).val("").removeData("selected").trigger("input").change(),!1===s.silent&&(b(i.el).w2tag("Not a valid date"),setTimeout(function(){b(i.el).w2tag("")},3e3))):(b(i.el).val("").removeData("selected").trigger("input").change(),!1===s.silent&&(b(i.el).w2tag("Not in range"),setTimeout(function(){b(i.el).w2tag("")},3e3)))),"enum"===i.type&&b(i.helpers.multi).find("input").val("").width(20),"file"===i.type&&b(i.helpers.multi).css({outline:"none"})},keyPress:function(e){var t=this;t.options;if(-1!==["int","float","money","currency","percent","hex","bin","color","alphanumeric"].indexOf(t.type)){if(e.metaKey||e.ctrlKey||e.altKey||e.charCode!=e.keyCode&&0<e.keyCode)return;var i=String.fromCharCode(e.charCode);if(!t.checkType(i,!0)&&13!=e.keyCode)return e.preventDefault(),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0,!1}-1!==["date","time","datetime"].indexOf(t.type)&&9!==e.keyCode&&setTimeout(function(){t.updateOverlay()},1)},keyDown:function(e,t){var i=this,s=i.options,n=e.keyCode||t&&t.keyCode,l=!1;if(-1!==["int","float","money","currency","percent"].indexOf(i.type)){if(!s.keyboard||b(i.el).prop("readonly")||b(i.el).prop("disabled"))return;var o=parseFloat(b(i.el).val().replace(s.moneyRE,""))||0,a=s.step;switch((e.ctrlKey||e.metaKey)&&(a=10),n){case 38:if(e.shiftKey)break;var r=o+a<=s.max||null==s.max?Number((o+a).toFixed(12)):s.max;b(i.el).val(r).trigger("input").change(),l=!0;break;case 40:if(e.shiftKey)break;r=o-a>=s.min||null==s.min?Number((o-a).toFixed(12)):s.min;b(i.el).val(r).trigger("input").change(),l=!0}l&&(e.preventDefault(),setTimeout(function(){i.el.setSelectionRange(i.el.value.length,i.el.value.length)},0))}if("date"===i.type){if(!s.keyboard||b(i.el).prop("readonly")||b(i.el).prop("disabled"))return;var d=864e5,a=1;switch((e.ctrlKey||e.metaKey)&&(a=10),(h=w2utils.isDate(b(i.el).val(),s.format,!0))||(h=new Date,d=0),n){case 38:if(e.shiftKey)break;var u=w2utils.formatDate(h.getTime()+d,s.format);10==a&&(u=w2utils.formatDate(new Date(h.getFullYear(),h.getMonth()+1,h.getDate()),s.format)),b(i.el).val(u).trigger("input").change(),l=!0;break;case 40:if(e.shiftKey)break;u=w2utils.formatDate(h.getTime()-d,s.format);10==a&&(u=w2utils.formatDate(new Date(h.getFullYear(),h.getMonth()-1,h.getDate()),s.format)),b(i.el).val(u).trigger("input").change(),l=!0}l&&(e.preventDefault(),setTimeout(function(){i.el.setSelectionRange(i.el.value.length,i.el.value.length),i.updateOverlay()},0))}if("time"===i.type){if(!s.keyboard||b(i.el).prop("readonly")||b(i.el).prop("disabled"))return;var a=e.ctrlKey||e.metaKey?60:1,o=b(i.el).val(),c=i.toMin(o)||i.toMin((new Date).getHours()+":"+((new Date).getMinutes()-1));switch(n){case 38:if(e.shiftKey)break;c+=a,l=!0;break;case 40:if(e.shiftKey)break;c-=a,l=!0}l&&(b(i.el).val(i.fromMin(c)).trigger("input").change(),e.preventDefault(),setTimeout(function(){i.el.setSelectionRange(i.el.value.length,i.el.value.length)},0))}if("datetime"===i.type){if(!s.keyboard||b(i.el).prop("readonly")||b(i.el).prop("disabled"))return;d=864e5,a=1;(e.ctrlKey||e.metaKey)&&(a=10);var h,p=b(i.el).val();switch((h=w2utils.isDateTime(p,this.options.format,!0))||(h=new Date,d=0),n){case 38:if(e.shiftKey)break;u=w2utils.formatDateTime(h.getTime()+d,s.format);10==a&&(u=w2utils.formatDateTime(new Date(h.getFullYear(),h.getMonth()+1,h.getDate()),s.format)),b(i.el).val(u).trigger("input").change(),l=!0;break;case 40:if(e.shiftKey)break;u=w2utils.formatDateTime(h.getTime()-d,s.format);10==a&&(u=w2utils.formatDateTime(new Date(h.getFullYear(),h.getMonth()-1,h.getDate()),s.format)),b(i.el).val(u).trigger("input").change(),l=!0}l&&(e.preventDefault(),setTimeout(function(){i.el.setSelectionRange(i.el.value.length,i.el.value.length),i.updateOverlay()},0))}if("color"===i.type){if(b(i.el).prop("readonly")||b(i.el).prop("disabled"))return;if((e.ctrlKey||e.metaKey)&&!e.shiftKey){var f=null;switch(n){case 38:f="up";break;case 40:f="down";break;case 39:f="right";break;case 37:f="left"}i.el.nav&&null!=f&&(p=i.el.nav(f),b(i.el).val(p).trigger("input").change(),e.preventDefault())}}if(-1!==["list","combo","enum"].indexOf(i.type)&&!b(i.el).prop("readonly")&&!b(i.el).prop("disabled")){var g=b(i.el).data("selected"),m=b(i.el),w=!1;switch(-1!==["list","enum"].indexOf(i.type)&&("list"===i.type&&(m=b(i.helpers.focus).find("input")),"enum"===i.type&&(m=b(i.helpers.multi).find("input")),-1==[37,38,39,40].indexOf(n)&&setTimeout(function(){i.refresh()},1),86==e.keyCode&&(e.ctrlKey||e.metaKey)&&setTimeout(function(){i.refresh(),i.search(),i.request()},50)),n){case 27:"list"===i.type&&(""!==m.val()&&m.val(""),e.stopPropagation());break;case 37:case 39:break;case 13:if(0===b("#w2ui-overlay").length)break;var v=s.items[s.index];if("enum"===i.type)if(null!=v){if(!0===(y=i.trigger({phase:"before",type:"add",target:i.el,originalEvent:e.originalEvent,item:v})).isCancelled)return;v=y.item,g.length>=s.max&&0<s.max&&g.pop(),delete v.hidden,delete i.tmp.force_open,g.push(v),b(i.el).trigger("input").change(),m.val("").width(20),i.refresh(),i.trigger(b.extend(y,{phase:"after"}))}else{if(v={id:m.val(),text:m.val()},!0===(y=i.trigger({phase:"before",type:"new",target:i.el,originalEvent:e.originalEvent,item:v})).isCancelled)return;v=y.item,"function"==typeof i.onNew&&(g.length>=s.max&&0<s.max&&g.pop(),delete i.tmp.force_open,g.push(v),b(i.el).trigger("input").change(),m.val("").width(20),i.refresh()),i.trigger(b.extend(y,{phase:"after"}))}else v&&b(i.el).data("selected",v).val(v.text).trigger("input").change(),""===b(i.el).val()&&b(i.el).data("selected")&&b(i.el).removeData("selected").val("").trigger("input").change(),"list"===i.type&&(m.val(""),i.refresh()),i.tmp.force_hide=!0;break;case 8:case 46:if("enum"===i.type&&8===n&&""===m.val()&&0<g.length){var y,v=g[g.length-1];if(!0===(y=i.trigger({phase:"before",type:"remove",target:i.el,originalEvent:e.originalEvent,item:v})).isCancelled)return;g.pop(),b(i.el).trigger("input").trigger("change"),i.refresh(),i.trigger(b.extend(y,{phase:"after"}))}"list"===i.type&&""===m.val()&&(b(i.el).data("selected",{}).trigger("input").change(),i.refresh());break;case 38:for(s.index=w2utils.isInt(s.index)?parseInt(s.index):0,s.index--;0<s.index&&(s.items[s.index].hidden||s.items[s.index].disabled);)s.index--;if(0===s.index&&(s.items[s.index].hidden||s.items[s.index].disabled))for(;s.items[s.index]&&(s.items[s.index].hidden||s.items[s.index].disabled);)s.index++;w=!0;break;case 40:for(s.index=w2utils.isInt(s.index)?parseInt(s.index):-1,s.index++;s.index<s.items.length-1&&(s.items[s.index].hidden||s.items[s.index].disabled);)s.index++;if(s.index==s.items.length-1&&(s.items[s.index].hidden||s.items[s.index].disabled))for(;s.items[s.index]&&(s.items[s.index].hidden||s.items[s.index].disabled);)s.index--;""===m.val()&&0===b("#w2ui-overlay").length?i.tmp.force_open=!0:w=!0}if(w)return s.index<0&&(s.index=0),s.index>=s.items.length&&(s.index=s.items.length-1),i.updateOverlay(w),e.preventDefault(),void setTimeout(function(){var e;"enum"===i.type||"list"===i.type?(e=m.get(0)).setSelectionRange(e.value.length,e.value.length):i.el.setSelectionRange(i.el.value.length,i.el.value.length)},0);"enum"===i.type&&m.width(8*(m.val().length+2)+"px")}},keyUp:function(e){var t,i;-1!==["list","combo","enum"].indexOf(this.type)&&(b(this.el).prop("readonly")||b(this.el).prop("disabled")||-1==[16,17,18,20,37,39,91].indexOf(e.keyCode)&&(0===(t=b(this.helpers.focus).find("input")).length&&(t=b(this.el)),!0!==(i=this.trigger({phase:"before",type:"search",originalEvent:e,target:t,search:t.val()})).isCancelled&&(this.tmp.force_hide||this.request(),1==t.val().length&&this.refresh(),0!==b("#w2ui-overlay").length&&-1!=[38,40].indexOf(e.keyCode)||this.search(),this.trigger(b.extend(i,{phase:"after"})))))},clearCache:function(){this.options.items=[],this.tmp.xhr_loading=!1,this.tmp.xhr_search="",this.tmp.xhr_total=-1},request:function(e){var t,o=this,a=this.options,r=b(o.el).val()||"";if(a.url){if("enum"===o.type&&(r=0===(t=b(o.helpers.multi).find("input")).length?"":t.val()),"list"===o.type&&(t=b(o.helpers.focus).find("input"),r=0===t.length?"":t.val()),0!==a.minLength&&r.length<a.minLength)return a.items=[],void this.updateOverlay();if(null==e&&(e=a.interval),null==o.tmp.xhr_search&&(o.tmp.xhr_search=""),null==o.tmp.xhr_total&&(o.tmp.xhr_total=-1),a.url&&(0===a.items.length&&0!==o.tmp.xhr_total||o.tmp.xhr_total==a.cacheMax&&r.length>o.tmp.xhr_search.length||r.length>=o.tmp.xhr_search.length&&r.substr(0,o.tmp.xhr_search.length)!==o.tmp.xhr_search||r.length<o.tmp.xhr_search.length)){if(o.tmp.xhr)try{o.tmp.xhr.abort()}catch(e){}o.tmp.xhr_loading=!0,o.search(),clearTimeout(o.tmp.timeout),o.tmp.timeout=setTimeout(function(){var e=a.url,l={search:r,max:a.cacheMax};b.extend(l,a.postData);var t=o.trigger({phase:"before",type:"request",search:r,target:o.el,url:e,postData:l});!0!==t.isCancelled&&(e={type:"GET",url:e=t.url,data:l=t.postData,dataType:"JSON"},a.method&&(e.type=a.method),"JSON"===w2utils.settings.dataType&&(e.type="POST",e.data=JSON.stringify(e.data),e.contentType="application/json"),"HTTPJSON"===w2utils.settings.dataType&&(e.data={request:JSON.stringify(e.data)}),null!=a.method&&(e.type=a.method),o.tmp.xhr=b.ajax(e).done(function(e,t,i){var s,n,i=o.trigger({phase:"before",type:"load",target:o.el,search:l.search,data:e,xhr:i});!0!==i.isCancelled&&(null==(e="string"==typeof(e=i.data)?JSON.parse(e):e).records&&null!=e.items&&(e.records=e.items,delete e.items),"success"===e.status&&null==e.records&&(e.records=[]),"success"===e.status&&Array.isArray(e.records)?(e.records.length>a.cacheMax&&e.records.splice(a.cacheMax,1e5),null==a.recId&&null!=a.recid&&(a.recId=a.recid),(a.recId||a.recText)&&e.records.forEach(function(e){"string"==typeof a.recId&&(e.id=e[a.recId]),"function"==typeof a.recId&&(e.id=a.recId(e)),"string"==typeof a.recText&&(e.text=e[a.recText]),"function"==typeof a.recText&&(e.text=a.recText(e))}),o.tmp.xhr_loading=!1,o.tmp.xhr_search=r,o.tmp.xhr_total=e.records.length,o.tmp.lastError="",a.items=o.normMenu(e.records),""===r&&0===e.records.length?o.tmp.emptySet=!0:o.tmp.emptySet=!1,(s=b(o.el).data("find_selected"))&&(Array.isArray(s)?(n=[],s.forEach(function(t){var i=!1;a.items.forEach(function(e){(e.id==t||t&&t.id==e.id)&&(n.push(b.extend(!0,{},e)),i=!0)}),i||n.push(t)})):(n=s,a.items.forEach(function(e){(e.id==s||s&&s.id==e.id)&&(n=e)})),b(o.el).data("selected",n).removeData("find_selected").trigger("input").change()),o.search(),o.trigger(b.extend(i,{phase:"after"}))):console.log("ERROR: server did not return proper structure. It should return",{status:"success",records:[{id:1,text:"item"}]}))}).fail(function(e,t,i){var s,i={status:t,error:i,rawResponseText:e.responseText},i=o.trigger({phase:"before",type:"error",target:o.el,search:r,error:i,xhr:e});if(!0!==i.isCancelled){if("abort"!==t){try{s=b.parseJSON(e.responseText)}catch(e){}console.log("ERROR: Server communication failed.","\n   EXPECTED:",{status:"success",records:[{id:1,text:"item"}]},"\n         OR:",{status:"error",message:"error message"},"\n   RECEIVED:","object"==typeof s?s:e.responseText)}o.tmp.xhr_loading=!1,o.tmp.xhr_search=r,o.tmp.xhr_total=0,o.tmp.emptySet=!0,o.tmp.lastError=i.error||"Server communication failed",a.items=[],o.clearCache(),o.search(),o.updateOverlay(!1),o.trigger(b.extend(i,{phase:"after"}))}}),o.trigger(b.extend(t,{phase:"after"})))},e)}}},search:function(){var e=this,t=this.options,i=b(e.el).val(),s=e.el,n=[],l=b(e.el).data("selected");if("enum"===e.type)for(var o in s=b(e.helpers.multi).find("input"),i=s.val(),l)l[o]&&n.push(l[o].id);else if("list"===e.type)for(var o in s=b(e.helpers.focus).find("input"),i=s.val(),l)l[o]&&n.push(l[o].id);var a=t.items;if(!0!==e.tmp.xhr_loading){for(var r=0,d=0;d<a.length;d++){var u=a[d];if(null!=t.compare)"function"==typeof t.compare&&(u.hidden=!1===t.compare.call(this,u,i));else{var c="",h="";-1!==["is","begins"].indexOf(t.match)&&(c="^"),-1!==["is","ends"].indexOf(t.match)&&(h="$");try{new RegExp(c+i+h,"i").test(u.text)||"..."===u.text?u.hidden=!1:u.hidden=!0}catch(e){}}!1===t.filter&&(u.hidden=!1),"enum"===e.type&&-1!==b.inArray(u.id,n)&&(u.hidden=!0),!0!==u.hidden&&(r++,delete u.hidden)}for(t.index=-1;a[t.index]&&a[t.index].hidden;)t.index++;r<=0&&(t.index=-1),t.spinner=!1,e.updateOverlay(),setTimeout(function(){var e=b("#w2ui-overlay").html()||"";t.markSearch&&-1!==e.indexOf("$.fn.w2menuHandler")&&b("#w2ui-overlay").w2marker(i)},1)}else a.splice(0,t.cacheMax),t.spinner=!0,e.updateOverlay()},updateOverlay:function(e){var t,n=this,l=this.options;if("color"===this.type){if(b(n.el).prop("readonly")||b(n.el).prop("disabled"))return;b(this.el).w2color({color:b(this.el).val(),transparent:l.transparent,advanced:l.advanced},function(e){null!=e&&b(n.el).val(e).trigger("input").change()})}if("date"===this.type){if(b(n.el).prop("readonly")||b(n.el).prop("disabled"))return;0===b("#w2ui-overlay").length&&b(n.el).w2overlay('<div class="w2ui-reset w2ui-calendar"></div>',{css:{"background-color":"#f5f5f5"},onShow:function(e){w2utils.isIE&&b(".w2ui-calendar").on("mousedown",function(e){e=b(e.target);1===e.length&&"w2ui-jump-year"===e[0].id&&b("#w2ui-overlay").data("keepOpen",!0)})}}),(t=w2utils.isDate(b(n.el).val(),n.options.format,!0))&&(r=t.getMonth()+1,d=t.getFullYear()),function i(e,t){b("#w2ui-overlay > div > div").html(n.getMonthHTML(e,t,b(n.el).val())),b("#w2ui-overlay .w2ui-calendar-title").on("mousedown",function(){var e,t;b(this).next().hasClass("w2ui-calendar-jump")?b(this).next().remove():(b(this).after('<div class="w2ui-calendar-jump" style=""></div>'),b(this).next().hide().html(n.getYearHTML()).fadeIn(200),setTimeout(function(){b("#w2ui-overlay .w2ui-calendar-jump").find(".w2ui-jump-month, .w2ui-jump-year").on("click",function(){b(this).hasClass("w2ui-jump-month")&&(b(this).parent().find(".w2ui-jump-month").removeClass("selected"),b(this).addClass("selected"),t=b(this).attr("name")),b(this).hasClass("w2ui-jump-year")&&(b(this).parent().find(".w2ui-jump-year").removeClass("selected"),b(this).addClass("selected"),e=b(this).attr("name")),null!=e&&null!=t&&(b("#w2ui-overlay .w2ui-calendar-jump").fadeOut(100),setTimeout(function(){i(parseInt(t)+1,e)},100))}),b("#w2ui-overlay .w2ui-calendar-jump >:last-child").prop("scrollTop",2e3)},1))}),b("#w2ui-overlay .w2ui-date").on("mousedown",function(){var e=b(this).attr("date");b(n.el).val(e).trigger("input").change(),b(this).css({"background-color":"#B6D5FB","border-color":"#aaa"})}).on("mouseup",function(){setTimeout(function(){0<b("#w2ui-overlay").length&&b("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)}),b("#w2ui-overlay .previous").on("mousedown",function(){var e=n.options.current.split("/");e[0]=parseInt(e[0])-1,i(e[0],e[1])}),b("#w2ui-overlay .next").on("mousedown",function(){var e=n.options.current.split("/");e[0]=parseInt(e[0])+1,i(e[0],e[1])})}(r,d)}if("time"===this.type){if(b(n.el).prop("readonly")||b(n.el).prop("disabled"))return;0===b("#w2ui-overlay").length&&b(n.el).w2overlay('<div class="w2ui-reset w2ui-calendar-time" onclick="event.stopPropagation();"></div>',{css:{"background-color":"#fff"}});var i="h24"===this.options.format;b("#w2ui-overlay > div").html(n.getHourHTML()),b("#w2ui-overlay .w2ui-time").on("mousedown",function(e){b(this).css({"background-color":"#B6D5FB","border-color":"#aaa"});var t=b(this).attr("hour");b(n.el).val((12<t&&!i?t-12:t)+":00"+(i?"":t<12?" am":" pm")).trigger("input").change()}),null==this.options.noMinutes||!1===this.options.noMinutes?b("#w2ui-overlay .w2ui-time").on("mouseup",function(){var t=b(this).attr("hour");0<b("#w2ui-overlay").length&&b("#w2ui-overlay")[0].hide(),b(n.el).w2overlay('<div class="w2ui-reset w2ui-calendar-time"></div>',{css:{"background-color":"#fff"}}),b("#w2ui-overlay > div").html(n.getMinHTML(t)),b("#w2ui-overlay .w2ui-time").on("mousedown",function(){b(this).css({"background-color":"#B6D5FB","border-color":"#aaa"});var e=b(this).attr("min");b(n.el).val((12<t&&!i?t-12:t)+":"+(e<10?0:"")+e+(i?"":t<12?" am":" pm")).trigger("input").change()}).on("mouseup",function(){setTimeout(function(){0<b("#w2ui-overlay").length&&b("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)})}):b("#w2ui-overlay .w2ui-time").on("mouseup",function(){setTimeout(function(){0<b("#w2ui-overlay").length&&b("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)})}if("datetime"===this.type){if(b(n.el).prop("readonly")||b(n.el).prop("disabled"))return;0<b("#w2ui-overlay .w2ui-time").length&&b("#w2ui-overlay")[0].hide(),0===b("#w2ui-overlay").length&&b(n.el).w2overlay('<div class="w2ui-reset w2ui-calendar" onclick="event.stopPropagation();"></div>',{css:{"background-color":"#f5f5f5"},onShow:function(e){w2utils.isIE&&b(".w2ui-calendar").on("mousedown",function(e){e=b(e.target);1===e.length&&"w2ui-jump-year"===e[0].id&&b("#w2ui-overlay").data("keepOpen",!0)})}}),(t=w2utils.isDateTime(b(n.el).val(),n.options.format,!0))&&(r=t.getMonth()+1,d=t.getFullYear());var s=null;!function i(e,t){b("#w2ui-overlay > div > div").html(n.getMonthHTML(e,t,b(n.el).val())+(l.btn_now?'<div class="w2ui-calendar-now now">'+w2utils.lang("Current Date & Time")+"</div>":"")),b("#w2ui-overlay .w2ui-calendar-title").on("mousedown",function(){var e,t;b(this).next().hasClass("w2ui-calendar-jump")?b(this).next().remove():(b(this).after('<div class="w2ui-calendar-jump" style=""></div>'),b(this).next().hide().html(n.getYearHTML()).fadeIn(200),setTimeout(function(){b("#w2ui-overlay .w2ui-calendar-jump").find(".w2ui-jump-month, .w2ui-jump-year").on("click",function(){b(this).hasClass("w2ui-jump-month")&&(b(this).parent().find(".w2ui-jump-month").removeClass("selected"),b(this).addClass("selected"),t=b(this).attr("name")),b(this).hasClass("w2ui-jump-year")&&(b(this).parent().find(".w2ui-jump-year").removeClass("selected"),b(this).addClass("selected"),e=b(this).attr("name")),null!=e&&null!=t&&(b("#w2ui-overlay .w2ui-calendar-jump").fadeOut(100),setTimeout(function(){i(parseInt(t)+1,e)},100))}),b("#w2ui-overlay .w2ui-calendar-jump >:last-child").prop("scrollTop",2e3)},1))}),b("#w2ui-overlay .w2ui-date").on("mousedown",function(){var e=b(this).attr("date");b(n.el).val(e).trigger("input").change(),b(this).css({"background-color":"#B6D5FB","border-color":"#aaa"}),s=new Date(b(this).attr("data-date"))}).on("mouseup",function(){var i,t;0<b("#w2ui-overlay").length&&b("#w2ui-overlay")[0].hide(),b(n.el).w2overlay('<div class="w2ui-reset w2ui-calendar-time"></div>',{css:{"background-color":"#fff"}});n.options.format;b("#w2ui-overlay > div").html(n.getHourHTML()),b("#w2ui-overlay .w2ui-time").on("mousedown",function(e){b(this).css({"background-color":"#B6D5FB","border-color":"#aaa"}),i=b(this).attr("hour"),s.setHours(i);var t=w2utils.formatDateTime(s,n.options.format);b(n.el).val(t).trigger("input").change()}),null==n.options.noMinutes||!1===n.options.noMinutes?b("#w2ui-overlay .w2ui-time").on("mouseup",function(){var e=b(this).attr("hour");0<b("#w2ui-overlay").length&&b("#w2ui-overlay")[0].hide(),b(n.el).w2overlay('<div class="w2ui-reset w2ui-calendar-time"></div>',{css:{"background-color":"#fff"}}),b("#w2ui-overlay > div").html(n.getMinHTML(e)),b("#w2ui-overlay .w2ui-time").on("mousedown",function(){b(this).css({"background-color":"#B6D5FB","border-color":"#aaa"}),t=b(this).attr("min"),s.setHours(i,t);var e=w2utils.formatDateTime(s,n.options.format);b(n.el).val(e).trigger("input").change()}).on("mouseup",function(){setTimeout(function(){0<b("#w2ui-overlay").length&&b("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)})}):b("#w2ui-overlay .w2ui-time").on("mouseup",function(){setTimeout(function(){0<b("#w2ui-overlay").length&&b("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)})}),b("#w2ui-overlay .previous").on("mousedown",function(){var e=n.options.current.split("/");e[0]=parseInt(e[0])-1,i(e[0],e[1])}),b("#w2ui-overlay .next").on("mousedown",function(){var e=n.options.current.split("/");e[0]=parseInt(e[0])+1,i(e[0],e[1])}),b("#w2ui-overlay .now").on("mousedown",function(){var e=w2utils.formatDateTime(new Date,n.options.format);return b(n.el).val(e).trigger("input").change(),!1}).on("mouseup",function(){setTimeout(function(){0<b("#w2ui-overlay").length&&b("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)})}(r,d)}if(-1!==["list","combo","enum"].indexOf(this.type)){var o,a=this.el,r=this.el;if("enum"===this.type&&(a=b(this.helpers.multi),r=b(a).find("input")),"list"===this.type&&(o=b(r).data("selected"),b.isPlainObject(o)&&!b.isEmptyObject(o)&&-1==l.index&&l.items.forEach(function(e,t){e.id===o.id&&(l.index=t)}),r=b(this.helpers.focus).find("input")),b(this.el).hasClass("has-focus"))if(!1!==l.openOnFocus||""!==b(r).val()||!0===n.tmp.force_open){if(n.tmp.force_hide)return b().w2overlay(),void setTimeout(function(){delete n.tmp.force_hide},1);""!==b(r).val()&&delete n.tmp.force_open;var d=w2utils.lang("No matches");null!=l.url&&String(b(r).val()).length<l.minLength&&!0!==n.tmp.emptySet&&(d=l.minLength+" "+w2utils.lang("letters or more...")),null!=l.url&&""===b(r).val()&&!0!==n.tmp.emptySet&&(d=w2utils.lang(l.msgSearch||"Type to search...")),null==l.url&&0===l.items.length&&(d=w2utils.lang("Empty list")),null!=l.msgNoItems&&(r={search:b(r).val(),options:b.extend(!0,{},l)},l.url&&(r.remote={url:l.url,empty:!!n.tmp.emptySet,error:n.tmp.lastError,minLength:l.minLength}),d="function"==typeof l.msgNoItems?l.msgNoItems(r):l.msgNoItems);var d=(d=n.tmp.lastError?n.tmp.lastError:d)&&'<div style="white-space: normal; line-height: 1.3">'+d+"</div>",u=b.extend(!0,{},l,{search:!1,render:l.renderDrop,maxHeight:l.maxDropHeight,maxWidth:l.maxDropWidth,msgNoItems:d,onSelect:function(i){var e,t,s;"enum"===n.type?(e=b(n.el).data("selected"),!i.item||!0!==(t=n.trigger({phase:"before",type:"add",target:n.el,originalEvent:i.originalEvent,item:i.item})).isCancelled&&(e.length>=l.max&&0<l.max&&e.pop(),delete i.item.hidden,e.push(i.item),b(n.el).data("selected",e).trigger("input").change(),b(n.helpers.multi).find("input").val("").width(20),n.refresh(),!0!==i.keepOpen?0<b("#w2ui-overlay").length&&b("#w2ui-overlay")[0].hide():(u.items.forEach(function(e,t){e.id==i.item.id&&(s=t)}),null!=s&&u.items.splice(s,1),u.selected=e,b(a).w2menu("refresh",u)),n.trigger(b.extend(t,{phase:"after"})))):(b(n.el).data("selected",i.item).val(i.item.text).trigger("input").change(),n.helpers.focus&&n.helpers.focus.find("input").val(""))}});b(a).w2menu(e?"refresh-index":"refresh",u)}else b().w2overlay()}},inRange:function(e,t){var i,s,n,l=!1;if("date"===this.type){var o=w2utils.isDate(e,this.options.format,!0);if(o)if(this.options.start||this.options.end?(c="string"==typeof this.options.start?this.options.start:b(this.options.start).val(),h="string"==typeof this.options.end?this.options.end:b(this.options.end).val(),d=w2utils.isDate(c,this.options.format,!0),u=w2utils.isDate(h,this.options.format,!0),p=new Date(o),u=u||p,(d=d||p)<=p&&p<=u&&(l=!0)):l=!0,this.options.blocked&&-1!==b.inArray(e,this.options.blocked)&&(l=!1),null!==this.options.blockWeekDays&&void 0!==this.options.blockWeekDays&&null!=this.options.blockWeekDays.length)for(var a=this.options.blockWeekDays.length,r=0;r<a;r++)o.getDay()==this.options.blockWeekDays[r]&&(l=!1)}else if("time"===this.type)this.options.start||this.options.end?(i=this.toMin(e),s=this.toMin(this.options.start),n=(n=this.toMin(this.options.end))||i,(s=s||i)<=i&&i<=n&&(l=!0)):l=!0;else if("datetime"===this.type){var d,u,c,h,p,o=w2utils.isDateTime(e,this.options.format,!0);if(o)if(this.options.start||this.options.end?(p=o,d=(d="object"==typeof this.options.start&&this.options.start instanceof Date?this.options.start:""!==(c="string"==typeof this.options.start?this.options.start:b(this.options.start).val()).trim()?w2utils.isDateTime(c,this.options.format,!0):"")||p,u=(u="object"==typeof this.options.end&&this.options.end instanceof Date?this.options.end:""!==(h="string"==typeof this.options.end?this.options.end:b(this.options.end).val()).trim()?w2utils.isDateTime(h,this.options.format,!0):"")||p,t&&d instanceof Date&&(d.setHours(0),d.setMinutes(0),d.setSeconds(0)),d<=p&&p<=u&&(l=!0)):l=!0,l&&this.options.blocked)for(r=0;r<this.options.blocked.length;r++){var f=this.options.blocked[r];if("object"==typeof(f="string"==typeof f?w2utils.isDateTime(f,this.options.format,!0):f)&&f instanceof Date&&f.getFullYear()==o.getFullYear()&&f.getMonth()==o.getMonth()&&f.getDate()==o.getDate()){l=!1;break}}}return l},checkType:function(e,t){var i=this;switch(i.type){case"int":return t&&-1!==["-",i.options.groupSymbol].indexOf(e)?!0:w2utils.isInt(e.replace(i.options.numberRE,""));case"percent":e=e.replace(/%/g,"");case"float":return t&&-1!==["-",w2utils.settings.decimalSymbol,i.options.groupSymbol].indexOf(e)?!0:w2utils.isFloat(e.replace(i.options.numberRE,""));case"money":case"currency":return t&&-1!==["-",i.options.decimalSymbol,i.options.groupSymbol,i.options.currencyPrefix,i.options.currencySuffix].indexOf(e)?!0:w2utils.isFloat(e.replace(i.options.moneyRE,""));case"bin":return w2utils.isBin(e);case"hex":return w2utils.isHex(e);case"alphanumeric":return w2utils.isAlphaNumeric(e)}return!0},addPrefix:function(){var i=this;setTimeout(function(){var e;"clear"!==i.type&&((e=b(i.el).data("tmp")||{})["old-padding-left"]&&b(i.el).css("padding-left",e["old-padding-left"]),e["old-padding-left"]=b(i.el).css("padding-left"),b(i.el).data("tmp",e),i.helpers.prefix&&b(i.helpers.prefix).remove(),""!==i.options.prefix&&(b(i.el).before('<div class="w2ui-field-helper">'+i.options.prefix+"</div>"),(e=b(i.el).prev()).css({color:b(i.el).css("color"),"font-family":b(i.el).css("font-family"),"font-size":b(i.el).css("font-size"),"padding-top":b(i.el).css("padding-top"),"padding-bottom":b(i.el).css("padding-bottom"),"padding-left":b(i.el).css("padding-left"),"padding-right":0,"margin-top":parseInt(b(i.el).css("margin-top"),10)+2+"px","margin-bottom":parseInt(b(i.el).css("margin-bottom"),10)+1+"px","margin-left":b(i.el).css("margin-left"),"margin-right":0}).on("click",function(e){var t;i.options.icon&&"function"==typeof i.onIconClick?!0!==(t=i.trigger({phase:"before",type:"iconClick",target:i.el,el:b(this).find("span.w2ui-icon")[0]})).isCancelled&&i.trigger(b.extend(t,{phase:"after"})):("list"===i.type?b(i.helpers.focus).find("input"):b(i.el)).focus()}),b(i.el).css("padding-left",e.width()+parseInt(b(i.el).css("padding-left"),10)+"px"),i.helpers.prefix=e))},1)},addSuffix:function(){var t,i,n=this;setTimeout(function(){var e;"clear"!==n.type&&((e=b(n.el).data("tmp")||{})["old-padding-right"]&&b(n.el).css("padding-right",e["old-padding-right"]),e["old-padding-right"]=b(n.el).css("padding-right"),b(n.el).data("tmp",e),i=parseInt(b(n.el).css("padding-right"),10),n.options.arrows&&(n.helpers.arrows&&b(n.helpers.arrows).remove(),b(n.el).after('<div class="w2ui-field-helper" style="border: 1px solid transparent">&#160;    <div class="w2ui-field-up" type="up">        <div class="arrow-up" type="up"></div>    </div>    <div class="w2ui-field-down" type="down">        <div class="arrow-down" type="down"></div>    </div></div>'),(t=b(n.el).next()).css({color:b(n.el).css("color"),"font-family":b(n.el).css("font-family"),"font-size":b(n.el).css("font-size"),height:b(n.el).height()+parseInt(b(n.el).css("padding-top"),10)+parseInt(b(n.el).css("padding-bottom"),10)+"px",padding:0,"margin-top":parseInt(b(n.el).css("margin-top"),10)+1+"px","margin-bottom":0,"border-left":"1px solid silver"}).css("margin-left","-"+(t.width()+parseInt(b(n.el).css("margin-right"),10)+12)+"px").on("mousedown",function(t){var i=b("body");function s(e){b(n.el).focus(),n.keyDown(b.Event("keydown"),{keyCode:"up"===b(t.target).attr("type")?38:40}),!1!==e&&b("body").data("_field_update_timer",setTimeout(s,60))}i.on("mouseup",function e(){clearTimeout(i.data("_field_update_timer"));i.off("mouseup",e)}),i.data("_field_update_timer",setTimeout(s,700)),s(!1)}),i+=t.width()+12,b(n.el).css("padding-right",i+"px"),n.helpers.arrows=t),""!==n.options.suffix&&(n.helpers.suffix&&b(n.helpers.suffix).remove(),b(n.el).after('<div class="w2ui-field-helper">'+n.options.suffix+"</div>"),(t=b(n.el).next()).css({color:b(n.el).css("color"),"font-family":b(n.el).css("font-family"),"font-size":b(n.el).css("font-size"),"padding-top":b(n.el).css("padding-top"),"padding-bottom":b(n.el).css("padding-bottom"),"padding-left":"3px","padding-right":b(n.el).css("padding-right"),"margin-top":parseInt(b(n.el).css("margin-top"),10)+2+"px","margin-bottom":parseInt(b(n.el).css("margin-bottom"),10)+1+"px"}).on("click",function(e){("list"===n.type?b(n.helpers.focus).find("input"):b(n.el)).focus()}),t.css("margin-left","-"+(w2utils.getSize(t,"width")+parseInt(b(n.el).css("margin-right"),10)+2)+"px"),i+=t.width()+3,b(n.el).css("padding-right",i+"px"),n.helpers.suffix=t))},1)},addFocus:function(){var i,s=this;this.options;b(s.helpers.focus).remove();var e=parseInt(b(s.el).attr("tabIndex"));isNaN(e)||-1===e||(s.el._tabIndex=e),null==(e=s.el._tabIndex?s.el._tabIndex:e)&&(e=-1),isNaN(e)&&(e=0);var t="",e='<div class="w2ui-field-helper">    <div class="w2ui-icon icon-search" style="opacity: 0; display: none"></div>    <input '+(t=null!=b(s.el).attr("id")?'id="'+b(s.el).attr("id")+'_search"':t)+' type="text" tabIndex="'+e+'" autocapitalize="off" autocomplete="off" autocorrect="off" spellcheck="false"/></div>';b(s.el).attr("tabindex",-1).before(e);e=b(s.el).prev();(s.helpers.focus=e).css({width:b(s.el).width(),"margin-top":b(s.el).css("margin-top"),"margin-left":parseInt(b(s.el).css("margin-left"))+parseInt(b(s.el).css("padding-left"))+"px","margin-bottom":b(s.el).css("margin-bottom"),"margin-right":b(s.el).css("margin-right")}).find("input").css({cursor:"default",width:"100%",outline:"none",opacity:1,margin:0,border:"1px solid transparent",padding:b(s.el).css("padding-top"),"padding-left":0,"margin-left":0,"background-color":"transparent"}),e.find("input").on("click",function(e){0===b("#w2ui-overlay").length&&s.focus(e),e.stopPropagation()}).on("focus",function(e){i=b(s.el).attr("placeholder"),b(s.el).css({outline:"auto 5px #7DB4F3","outline-offset":"2px"}),b(this).val(""),b(s.el).triggerHandler("focus"),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}).on("blur",function(e){b(s.el).css("outline","none"),b(this).val(""),s.refresh(),b(s.el).triggerHandler("blur"),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0,null!=i&&b(s.el).attr("placeholder",i)}).on("keydown",function(e){var t=this;s.keyDown(e),setTimeout(function(){""===t.value?b(s.el).attr("placeholder",i):b(s.el).attr("placeholder","")},10)}).on("keyup",function(e){s.keyUp(e)}).on("keypress",function(e){s.keyPress(e)}),e.on("click",function(e){b(this).find("input").focus()}),s.refresh()},addMulti:function(){var n=this;this.options;b(n.helpers.multi).remove();var e,t="",i="margin-top     : 0px; margin-bottom  : 0px; margin-left    : "+b(n.el).css("margin-left")+"; margin-right   : "+b(n.el).css("margin-right")+"; width          : "+(w2utils.getSize(n.el,"width")-parseInt(b(n.el).css("margin-left"),10)-parseInt(b(n.el).css("margin-right"),10))+"px;",s="";null!=b(n.el).attr("id")&&(s='id="'+b(n.el).attr("id")+'_search" '),"enum"===n.type&&((e=b(n.el).attr("tabIndex"))&&-1!==e&&(n.el._tabIndex=e),null==(e=n.el._tabIndex?n.el._tabIndex:e)&&(e=0),t='<div class="w2ui-field-helper w2ui-list" style="'+i+'; box-sizing: border-box">    <div style="padding: 0px; margin: 0px; display: inline-block" class="w2ui-multi-items">    <ul>        <li style="padding-left: 0px; padding-right: 0px" class="nomouse">            <input '+s+' type="text" style="width: 20px; margin: -3px 0 0; padding: 2px 0; border-color: white" autocapitalize="off" autocomplete="off" autocorrect="off" spellcheck="false"'+(b(n.el).prop("readonly")?' readonly="readonly"':"")+(b(n.el).prop("disabled")?' disabled="disabled"':"")+' tabindex="'+e+'"/>        </li>    </ul>    </div></div>'),"file"===n.type&&(t='<div class="w2ui-field-helper w2ui-list" style="'+i+'; box-sizing: border-box">   <div style="position: absolute; left: 0px; right: 0px; top: 0px; bottom: 0px;">       <input '+s+' name="attachment" class="file-input" type="file" style="width: 100%; height: 100%; opacity: 0;" tabindex="-1"'+(1!==n.options.max?' multiple="multiple"':"")+(b(n.el).prop("readonly")?' readonly="readonly"':"")+(b(n.el).prop("disabled")?' disabled="disabled"':"")+(b(n.el).attr("accept")?' accept="'+b(n.el).attr("accept")+'"':"")+'/>   </div>    <div style="position: absolute; padding: 0px; margin: 0px; display: inline-block" class="w2ui-multi-items">        <ul><li style="padding-left: 0px; padding-right: 0px" class="nomouse"></li></ul>    </div></div>');s=b(n.el).data("tmp")||{};s["old-background-color"]=b(n.el).css("background-color"),s["old-border-color"]=b(n.el).css("border-color"),b(n.el).data("tmp",s),b(n.el).before(t).css({"background-color":"transparent","border-color":"transparent"});var l=b(n.el).prev();n.helpers.multi=l,"enum"===n.type&&(b(n.el).attr("tabindex",-1),l.find("input").on("click",function(e){0===b("#w2ui-overlay").length&&n.focus(e),b(n.el).triggerHandler("click")}).on("focus",function(e){b(l).css({outline:"auto 5px #7DB4F3","outline-offset":"2px"}),b(n.el).triggerHandler("focus"),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}).on("blur",function(e){b(l).css("outline","none"),b(n.el).triggerHandler("blur"),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}).on("keyup",function(e){n.keyUp(e)}).on("keydown",function(e){n.keyDown(e)}).on("keypress",function(e){n.keyPress(e)}),l.on("click",function(e){b(this).find("input").focus()})),"file"===n.type&&(b(n.el).css("outline","none"),l.find("input").off(".drag").on("click.drag",function(e){e.stopPropagation(),b(n.el).prop("readonly")||b(n.el).prop("disabled")||b(n.el).focus()}).on("dragenter.drag",function(e){b(n.el).prop("readonly")||b(n.el).prop("disabled")||b(l).addClass("w2ui-file-dragover")}).on("dragleave.drag",function(e){b(n.el).prop("readonly")||b(n.el).prop("disabled")||b(l).removeClass("w2ui-file-dragover")}).on("drop.drag",function(e){if(!b(n.el).prop("readonly")&&!b(n.el).prop("disabled")){b(l).removeClass("w2ui-file-dragover");for(var t=e.originalEvent.dataTransfer.files,i=0,s=t.length;i<s;i++)n.addFile.call(n,t[i]);b(n.el).focus(),e.preventDefault(),e.stopPropagation()}}).on("dragover.drag",function(e){e.preventDefault(),e.stopPropagation()}).on("change.drag",function(){if(b(n.el).focus(),void 0!==this.files)for(var e=0,t=this.files.length;e<t;e++)n.addFile.call(n,this.files[e])})),n.refresh()},addFile:function(e){var t,i=this,s=this.options,n=b(i.el).data("selected"),l={name:e.name,type:e.type,modified:e.lastModifiedDate,size:e.size,content:null,file:e},o=0,a=0;if(n)for(var r=0;r<n.length;r++){if(n[r].name==e.name&&n[r].size==e.size)return;o+=n[r].size,a++}var d=i.trigger({phase:"before",type:"add",target:i.el,file:l,total:a,totalSize:o});if(!0!==d.isCancelled)return 0!==s.maxFileSize&&l.size>s.maxFileSize?(t="Maximum file size is "+w2utils.formatSize(s.maxFileSize),!1===s.silent&&b(i.el).w2tag(t),void console.log("ERROR: "+t)):0!==s.maxSize&&o+l.size>s.maxSize?(t=w2utils.lang("Maximum total size is")+" "+w2utils.formatSize(s.maxSize),void(!1===s.silent?b(i.el).w2tag(t):console.log("ERROR: "+t))):0!==s.max&&a>=s.max?(t=w2utils.lang("Maximum number of files is")+" "+s.max,void(!1===s.silent?b(i.el).w2tag(t):console.log("ERROR: "+t))):(n.push(l),void("undefined"!=typeof FileReader&&!0===s.readContent?((s=new FileReader).onload=function(e){var t=e.target.result,e=t.indexOf(",");l.content=t.substr(e+1),i.refresh(),b(i.el).trigger("input").trigger("change"),i.trigger(b.extend(d,{phase:"after"}))},s.readAsDataURL(e)):(i.refresh(),b(i.el).trigger("input").trigger("change"),i.trigger(b.extend(d,{phase:"after"})))))},normMenu:function(e,t){if(b.isArray(e)){for(var i=0;i<e.length;i++)"string"==typeof e[i]?e[i]={id:e[i],text:e[i]}:null!=e[i]?(null!=e[i].text&&null==e[i].id&&(e[i].id=e[i].text),null==e[i].text&&null!=e[i].id&&(e[i].text=e[i].id),null!=e[i].caption&&(e[i].text=e[i].caption)):e[i]={id:null,text:"null"};return e}if("function"==typeof e)return w2obj.field.prototype.normMenu.call(this,e.call(this,t));if("object"==typeof e){var s=[];for(i in e)s.push({id:i,text:e[i]});return s}},getMonthHTML:function(e,t,i){var s=new Date,n=w2utils.settings.fullmonths,l=["31","28","31","30","31","30","31","31","30","31","30","31"],o=s.getFullYear()+"/"+(Number(s.getMonth())+1)+"/"+s.getDate(),a=w2utils.settings.fulldays.slice(),r=w2utils.settings.shortdays.slice();"M"!==w2utils.settings.weekStarts&&(a.unshift(a.pop()),r.unshift(r.pop()));var d=this.options;null==d&&(d={}),t=w2utils.isInt(t)?parseInt(t):s.getFullYear(),12<(e=w2utils.isInt(e)?parseInt(e):s.getMonth()+1)&&(e-=12,t++),(e<1||0===e)&&(e+=12,t--),t/4==Math.floor(t/4)?l[1]="29":l[1]="28",d.current=e+"/"+t;for(var u=(s=new Date(t,e-1,1)).getDay(),c="",h=0;h<r.length;h++)c+='<td title="'+a[h]+'">'+r[h]+"</td>";var p='<div class="w2ui-calendar-title title">    <div class="w2ui-calendar-previous previous"> <div></div> </div>    <div class="w2ui-calendar-next next"> <div></div> </div> '+n[e-1]+", "+t+'</div><table class="w2ui-calendar-days" cellspacing="0"><tbody>    <tr class="w2ui-day-title">'+c+"</tr>    <tr>",f=1;"M"!==w2utils.settings.weekStarts&&u++,"datetime"===this.type&&(n=w2utils.isDateTime(i,d.format,!0),i=w2utils.formatDate(n,w2utils.settings.dateFormat));for(var g=1;g<43;g++){if(0===u&&1==g){for(var m=0;m<6;m++)p+='<td class="w2ui-day-empty">&#160;</td>';g+=6}else if(g<u||f>l[e-1]){p+='<td class="w2ui-day-empty">&#160;</td>',g%7==0&&(p+="</tr><tr>");continue}var w=t+"/"+e+"/"+f,v=new Date(w),y="";6===v.getDay()&&(y=" w2ui-saturday"),0===v.getDay()&&(y=" w2ui-sunday"),w==o&&(y+=" w2ui-today");var b,x=f,_="",k="",C="datetime"===this.type?(b=w2utils.formatDateTime(w,d.format),w2utils.formatDate(w,w2utils.settings.dateFormat)):b=w2utils.formatDate(w,d.format);d.colored&&void 0!==d.colored[C]&&(k="background-color: "+(w=d.colored[C].split(":"))[0]+";",_="color: "+w[1]+";"),p+='<td class="'+(this.inRange(b,!0)?"w2ui-date "+(C==i?"w2ui-date-selected":""):"w2ui-blocked")+y+'"    style="'+_+k+'" date="'+b+'" data-date="'+v+'">'+x+"</td>",(g%7==0||0===u&&1==g)&&(p+="</tr><tr>"),f++}return p+="</tr></tbody></table>"},getYearHTML:function(){for(var e=w2utils.settings.shortmonths,t=w2utils.settings.dateStartYear,i=w2utils.settings.dateEndYear,s="",n="",l=0;l<e.length;l++)s+='<div class="w2ui-jump-month" name="'+l+'">'+e[l]+"</div>";for(var o=t;o<=i;o++)n+='<div class="w2ui-jump-year" name="'+o+'">'+o+"</div>";return'<div id="w2ui-jump-month">'+s+'</div><div id="w2ui-jump-year">'+n+"</div>"},getHourHTML:function(){for(var e=[],t=this.options,i=-1<(t=null==t?{format:w2utils.settings.timeFormat}:t).format.indexOf("h24"),s=0;s<24;s++){var n=12!=s||i?(12<=s&&!i?s-12:s)+":00"+(i?"":s<12?" am":" pm"):"12:00 pm";e[Math.floor(s/8)]||(e[Math.floor(s/8)]="");var l,o,a=this.fromMin(this.toMin(n)),r=this.fromMin(this.toMin(n)+59);"datetime"===this.type&&(l=w2utils.isDateTime(this.el.value,t.format,!0),o=t.format.split("|")[0].trim(),a=w2utils.formatDate(l,o)+" "+a,r=w2utils.formatDate(l,o)+" "+r),e[Math.floor(s/8)]+='<div class="'+(this.inRange(a)||this.inRange(r)?"w2ui-time ":"w2ui-blocked")+'" hour="'+s+'">'+n+"</div>"}return'<div class="w2ui-calendar">   <div class="w2ui-calendar-title">'+w2utils.lang("Select Hour")+'</div>   <div class="w2ui-calendar-time"><table><tbody><tr>       <td>'+e[0]+"</td>       <td>"+e[1]+"</td>       <td>"+e[2]+"</td>   </tr></tbody></table></div></div>"},getMinHTML:function(e){null==e&&(e=0);for(var t=this.options,i=-1<(t=null==t?{format:w2utils.settings.timeFormat}:t).format.indexOf("h24"),s=[],n=0;n<60;n+=5){var l,o,a=(12<e&&!i?e-12:e)+":"+(n<10?0:"")+n+" "+(i?"":e<12?"am":"pm"),r=a,d=n<20?0:n<40?1:2;s[d]||(s[d]=""),"datetime"===this.type&&(l=w2utils.isDateTime(this.el.value,t.format,!0),o=t.format.split("|")[0].trim(),r=w2utils.formatDate(l,o)+" "+r),s[d]+='<div class="'+(this.inRange(r)?"w2ui-time ":"w2ui-blocked")+'" min="'+n+'">'+a+"</div>"}return'<div class="w2ui-calendar">   <div class="w2ui-calendar-title">'+w2utils.lang("Select Minute")+'</div>   <div class="w2ui-calendar-time"><table><tbody><tr>       <td>'+s[0]+"</td>       <td>"+s[1]+"</td>       <td>"+s[2]+"</td>   </tr></tbody></table></div></div>"},toMin:function(e){if("string"!=typeof e)return null;var t=e.split(":");return 2!==t.length?null:(t[0]=parseInt(t[0]),t[1]=parseInt(t[1]),-1!==e.indexOf("pm")&&12!==t[0]&&(t[0]+=12),60*t[0]+t[1])},fromMin:function(e){1440<=e&&(e%=1440),e<0&&(e=1440+e);var t=Math.floor(e/60),i=(e%60<10?"0":"")+e%60,e=this.options;return-1!==(e=null==e?{format:w2utils.settings.timeFormat}:e).format.indexOf("h24")?t+":"+i:(t<=12?t:t-12)+":"+i+" "+(12<=t?"pm":"am")}},b.extend(n.prototype,w2utils.event),w2obj.field=n}(jQuery),function(y){function h(e){this.name=null,this.header="",this.box=null,this.url="",this.routeData={},this.formURL="",this.formHTML="",this.page=0,this.recid=0,this.fields=[],this.actions={},this.record={},this.original=null,this.postData={},this.httpHeaders={},this.method=null,this.toolbar={},this.tabs={},this.style="",this.focus=0,this.autosize=!0,this.nestedFields=!0,this.multipart=!1,this.tabindexBase=0,this.isGenerated=!1,this.last={xhr:null,errors:[]},y.extend(!0,this,w2obj.form,e)}y.fn.w2form=function(e){if(!y.isPlainObject(e))return(t=w2ui[y(this).attr("name")])?0<arguments.length?(t[e]&&t[e].apply(t,Array.prototype.slice.call(arguments,1)),this):t:null;var t=this;if(w2utils.checkName(e,"w2form")){var i=e.record,s=e.original,n=e.fields,l=e.toolbar,o=e.tabs,a=new h(e);if(y.extend(a,{record:{},original:null,fields:[],tabs:{},toolbar:{},handlers:[]}),y.isArray(o)){y.extend(!0,a.tabs,{tabs:[]});for(var r=0;r<o.length;r++){var d=o[r];"object"==typeof d?(a.tabs.tabs.push(d),!0===d.active&&(a.tabs.active=d.id)):a.tabs.tabs.push({id:d,text:d})}}else y.extend(!0,a.tabs,o);if(y.extend(!0,a.toolbar,l),n)for(var u=0;u<n.length;u++){var c=y.extend(!0,{},n[u]);null==c.field&&null!=c.name&&(console.log("NOTICE: form field.name property is deprecated, please use field.field. Field ->",c),c.field=c.name),a.fields[u]=c}for(u in i)y.isPlainObject(i[u])?a.record[u]=y.extend(!0,{},i[u]):a.record[u]=i[u];for(u in s)y.isPlainObject(s[u])?a.original[u]=y.extend(!0,{},s[u]):a.original[u]=s[u];return 0<t.length&&(a.box=t[0]),""!==a.formURL?y.get(a.formURL,function(e){a.formHTML=e,a.isGenerated=!0,0===y(a.box).length&&0===e.length||(y(a.box).html(e),a.render(a.box))}):""!==a.formHTML||(0!==y(this).length&&""!==y.trim(y(this).html())?a.formHTML=y(this).html():a.formHTML=a.generateHTML()),""===(w2ui[a.name]=a).formURL&&(-1===String(a.formHTML).indexOf("w2ui-page")&&(a.formHTML='<div class="w2ui-page page-0" style="'+(a.pageStyle||"")+'">'+a.formHTML+"</div>"),y(a.box).html(a.formHTML),a.isGenerated=!0,a.render(a.box)),a}},h.prototype={onRequest:null,onLoad:null,onValidate:null,onSubmit:null,onProgress:null,onSave:null,onChange:null,onInput:null,onRender:null,onRefresh:null,onResize:null,onDestroy:null,onAction:null,onToolbar:null,onError:null,msgNotJSON:"Returned data is not in valid JSON format.",msgAJAXerror:"AJAX error. See console for more details.",msgRefresh:"Loading...",msgSaving:"Saving...",get:function(e,t){if(0===arguments.length){for(var i=[],s=0;s<this.fields.length;s++)null!=this.fields[s].field&&i.push(this.fields[s].field);return i}for(var n=0;n<this.fields.length;n++)if(this.fields[n].field==e)return!0===t?n:this.fields[n];return null},set:function(e,t){for(var i=0;i<this.fields.length;i++)if(this.fields[i].field==e)return y.extend(this.fields[i],t),this.refresh(e),!0;return!1},getValue:function(e){if(this.nestedFields){var t=void 0;try{var i=this.record,t=String(e).split(".").reduce(function(e,t){return e[t]},i)}catch(e){}return t}return this.record[e]},setValue:function(e,s){if(!this.nestedFields)return this.record[e]=s,!0;try{var n=this.record;return String(e).split(".").map(function(e,t,i){i.length-1!==t?n=n[e]||(n[e]={},n[e]):n[e]=s}),!0}catch(e){return!1}},show:function(){for(var e=[],t=0;t<arguments.length;t++){var i=this.get(arguments[t]);i&&i.hidden&&(i.hidden=!1,e.push(i.field))}return 0<e.length&&this.refresh.apply(this,e),this.updateEmptyGroups(),e.length},hide:function(){for(var e=[],t=0;t<arguments.length;t++){var i=this.get(arguments[t]);i&&!i.hidden&&(i.hidden=!0,e.push(i.field))}return 0<e.length&&this.refresh.apply(this,e),this.updateEmptyGroups(),e.length},updateEmptyGroups:function(){y(this.box).find(".w2ui-group").each(function(e,t){var i,s;i=y(t).find(".w2ui-field"),s=!0,i.each(function(e,t){"none"!=t.style.display&&(s=!1)}),s?y(t).hide():y(t).show()})},enable:function(){for(var e=[],t=0;t<arguments.length;t++){var i=this.get(arguments[t]);i&&i.disabled&&(i.disabled=!1,e.push(i.field))}return 0<e.length&&this.refresh.apply(this,e),e.length},disable:function(){for(var e=[],t=0;t<arguments.length;t++){var i=this.get(arguments[t]);i&&!i.disabled&&(i.disabled=!0,e.push(i.field))}return 0<e.length&&this.refresh.apply(this,e),e.length},change:function(){Array.from(arguments).forEach(function(e){e=this.get(e);e.$el&&e.$el.change()}.bind(this))},reload:function(e){("object"!=typeof this.url?this.url:this.url.get)&&0!==this.recid&&null!=this.recid?this.request(e):"function"==typeof e&&e()},clear:function(){0!=arguments.length?Array.from(arguments).forEach(function(e){var s=this.record;String(e).split(".").map(function(e,t,i){i.length-1!==t?s=s[e]:delete s[e]}),this.refresh(e)}.bind(this)):(this.recid=0,this.record={},this.original=null,this.refresh()),y().w2tag()},error:function(e){var t=this,i=this.trigger({target:this.name,type:"error",message:e,xhr:this.last.xhr});!0!==i.isCancelled?(setTimeout(function(){t.message(e)},1),this.trigger(y.extend(i,{phase:"after"}))):"function"==typeof callBack&&callBack()},message:function(e){"string"==typeof e&&(e={width:e.length<300?350:550,height:e.length<300?170:250,body:'<div class="w2ui-centered">'+e+"</div>",buttons:'<button class="w2ui-btn" onclick="w2ui[\''+this.name+"'].message()\">Ok</button>",onOpen:function(e){setTimeout(function(){y(e.box).find(".w2ui-btn").focus()},25)}}),w2utils.message.call(this,{box:this.box,path:"w2ui."+this.name,title:".w2ui-form-header:visible",body:".w2ui-form-box"},e)},validate:function(e){null==e&&(e=!0),y().w2tag();for(var t=[],i=0;i<this.fields.length;i++){var s=this.fields[i];switch(null==this.getValue(s.field)&&this.setValue(s.field,""),s.type){case"int":this.getValue(s.field)&&!w2utils.isInt(this.getValue(s.field))&&t.push({field:s,error:w2utils.lang("Not an integer")});break;case"float":this.getValue(s.field)&&!w2utils.isFloat(this.getValue(s.field))&&t.push({field:s,error:w2utils.lang("Not a float")});break;case"money":this.getValue(s.field)&&!w2utils.isMoney(this.getValue(s.field))&&t.push({field:s,error:w2utils.lang("Not in money format")});break;case"color":case"hex":this.getValue(s.field)&&!w2utils.isHex(this.getValue(s.field))&&t.push({field:s,error:w2utils.lang("Not a hex number")});break;case"email":this.getValue(s.field)&&!w2utils.isEmail(this.getValue(s.field))&&t.push({field:s,error:w2utils.lang("Not a valid email")});break;case"checkbox":1==this.getValue(s.field)?this.setValue(s.field,1):this.setValue(s.field,0);break;case"date":s.options.format||(s.options.format=w2utils.settings.dateFormat),this.getValue(s.field)&&!w2utils.isDate(this.getValue(s.field),s.options.format)&&t.push({field:s,error:w2utils.lang("Not a valid date")+": "+s.options.format})}var n=this.getValue(s.field);s.required&&!0!==s.hidden&&-1==["div","custom","html","empty"].indexOf(s.type)&&(""===n||y.isArray(n)&&0===n.length||y.isPlainObject(n)&&y.isEmptyObject(n))&&t.push({field:s,error:w2utils.lang("Required field")}),s.options&&!0!==s.hidden&&0<s.options.minLength&&-1==["enum","list","combo"].indexOf(s.type)&&this.getValue(s.field).length<s.options.minLength&&t.push({field:s,error:w2utils.lang("Field should be at least "+s.options.minLength+" characters.")})}var l=this.trigger({phase:"before",target:this.name,type:"validate",errors:t});if(!0!==l.isCancelled)return this.last.errors=t,e&&this.showErrors(),this.trigger(y.extend(l,{phase:"after"})),t},showErrors:function(){var s=this.last.errors;if(0<s.length){var e=s[0];this.goto(s[0].field.page),y(e.field.$el).parents(".w2ui-field")[0].scrollIntoView(!0);for(var t=0;t<s.length;t++){var e=s[t],i=y.extend({class:"w2ui-error",hideOnFocus:!0},e.options);null!=e.field&&("radio"===e.field.type?y(y(e.field.el).closest("div")[0]).w2tag(e.error,i):-1!==["enum","file"].indexOf(e.field.type)?function(t){setTimeout(function(){var e=y(t.field.el).data("w2field").helpers.multi;y(t.field.el).w2tag(t.error,t.options),y(e).addClass("w2ui-error")},1)}(e):y(e.field.el).w2tag(e.error,i))}setTimeout(function(){var i=s[0];y(i.field.$el).parents(".w2ui-page").off(".hideErrors").on("scroll.hideErrors",function(e){for(var t=0;t<s.length;t++)i=s[t],y(i.field.el).w2tag();y(i.field.$el).parents(".w2ui-page").off(".hideErrors")})},300)}},getChanges:function(){var e={};return e=null!=this.original&&"object"==typeof this.original&&!y.isEmptyObject(this.record)?function e(t,i,s){for(var n in t)null!=t[n]&&"object"==typeof t[n]?(s[n]=e(t[n],i[n]||{},{}),(!s[n]||y.isEmptyObject(s[n])&&y.isEmptyObject(i[n]))&&delete s[n]):(t[n]!=i[n]||null==t[n]&&null!=i[n])&&(s[n]=t[n]);return y.isEmptyObject(s)?null:s}(this.record,this.original,{}):e},getCleanRecord:function(e){var s=y.extend(!0,{},this.record);return this.fields.forEach(function(e){var t,i;-1!=["list","combo","enum"].indexOf(e.type)&&(t={nestedFields:!0,record:s},i=this.getValue.call(t,e.field),y.isPlainObject(i)&&null!=i.id&&this.setValue.call(t,e.field,i.id),Array.isArray(i)&&i.forEach(function(e,t){y.isPlainObject(e)&&e.id&&(i[t]=e.id)})),"map"==e.type&&(t={nestedFields:!0,record:s},(i=this.getValue.call(t,e.field))._order&&delete i._order)}.bind(this)),!0===e&&Object.keys(s).forEach(function(e){this.get(e)||delete s[e]}.bind(this)),s},request:function(e,s){var n=this;if("function"==typeof e&&(s=e,e=null),null==e&&(e={}),this.url&&("object"!=typeof this.url||this.url.get)){null==this.recid&&(this.recid=0);var t={cmd:"get"};t.recid=this.recid,t.name=this.name,y.extend(t,this.postData),y.extend(t,e);e=this.trigger({phase:"before",type:"request",target:this.name,url:this.url,postData:t,httpHeaders:this.httpHeaders});if(!0!==e.isCancelled){this.record={},this.original=null,this.lock(w2utils.lang(this.msgRefresh));var i=e.url;if("object"==typeof e.url&&e.url.get&&(i=e.url.get),this.last.xhr)try{this.last.xhr.abort()}catch(e){}if(!y.isEmptyObject(n.routeData)){var l=w2utils.parseRoute(i);if(0<l.keys.length)for(var o=0;o<l.keys.length;o++)null!=n.routeData[l.keys[o].name]&&(i=i.replace(new RegExp(":"+l.keys[o].name,"g"),n.routeData[l.keys[o].name]))}var a={type:"POST",url:i,data:e.postData,headers:e.httpHeaders,dataType:"json"},t=n.dataType||w2utils.settings.dataType;switch(t=e.dataType?e.dataType:t){case"HTTP":a.data=String(y.param(a.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]");break;case"HTTPJSON":a.data={request:JSON.stringify(a.data)};break;case"RESTFULL":a.type="GET",a.data=String(y.param(a.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]");break;case"RESTFULLJSON":a.type="GET",a.data=JSON.stringify(a.data),a.contentType="application/json";break;case"JSON":a.type="POST",a.data=JSON.stringify(a.data),a.contentType="application/json"}this.method&&(a.type=this.method),e.method&&(a.type=e.method),this.last.xhr=y.ajax(a).done(function(e,t,i){n.unlock(),null==(e=i.responseJSON)&&(e={status:"error",message:w2utils.lang(n.msgNotJSON),responseText:i.responseText});i=n.trigger({phase:"before",target:n.name,type:"load",data:e,xhr:i});!0!==i.isCancelled?("error"===i.data.status?n.error(w2utils.lang(i.data.message)):n.record=y.extend({},i.data.record),n.trigger(y.extend(i,{phase:"after"})),n.refresh(),n.applyFocus(),"function"==typeof s&&s(i.data)):"function"==typeof s&&s({status:"error",message:"Request aborted."})}).fail(function(e,t,i){var s,i={status:t,error:i,rawResponseText:e.responseText},i=n.trigger({phase:"before",type:"error",error:i,xhr:e});if(!0!==i.isCancelled){if("abort"!==t){try{s="object"==typeof e.responseJSON?e.responseJSON:y.parseJSON(e.responseText)}catch(e){}console.log("ERROR: Server communication failed.","\n   EXPECTED:",{status:"success",items:[{id:1,text:"item"}]},"\n         OR:",{status:"error",message:"error message"},"\n   RECEIVED:","object"==typeof s?s:e.responseText),n.unlock()}n.trigger(y.extend(i,{phase:"after"}))}}),this.trigger(y.extend(e,{phase:"after"}))}else"function"==typeof s&&s({status:"error",message:"Request aborted."})}},submit:function(e,t){return this.save(e,t)},save:function(r,d){var u=this;y(this.box).find(":focus").change(),"function"==typeof r&&(d=r,r=null),0===u.validate(!0).length&&(null==r&&(r={}),u.url&&("object"!=typeof u.url||u.url.save)?(u.lock(w2utils.lang(u.msgSaving)+' <span id="'+u.name+'_progress"></span>'),setTimeout(function(){var e={cmd:"save"};e.recid=u.recid,e.name=u.name,y.extend(e,u.postData),y.extend(e,r),u.multipart||u.fields.forEach(function(e){"file"===e.type&&Array.isArray(u.getValue(e.field))&&u.getValue(e.field).forEach(function(e){delete e.file})}),e.record=y.extend(!0,{},u.record);var t=u.trigger({phase:"before",type:"submit",target:u.name,url:u.url,postData:e,httpHeaders:u.httpHeaders});if(!0!==t.isCancelled){var i=t.url;if("object"==typeof t.url&&t.url.save&&(i=t.url.save),u.last.xhr)try{u.last.xhr.abort()}catch(e){}if(!y.isEmptyObject(u.routeData)){var s=w2utils.parseRoute(i);if(0<s.keys.length)for(var n=0;n<s.keys.length;n++)null!=u.routeData[s.keys[n].name]&&(i=i.replace(new RegExp(":"+s.keys[n].name,"g"),u.routeData[s.keys[n].name]))}var l,o={type:"POST",url:i,data:t.postData,headers:t.httpHeaders,dataType:"json",xhr:function(){var e=new window.XMLHttpRequest;return e.upload.addEventListener("progress",function(e){var t;!e.lengthComputable||!0!==(t=u.trigger({phase:"before",type:"progress",total:e.total,loaded:e.loaded,originalEvent:e})).isCancelled&&(((e=Math.round(e.loaded/e.total*100))&&100!=e||""!=y("#"+u.name+"_progress").text())&&y("#"+u.name+"_progress").text(e+"%"),u.trigger(y.extend(t,{phase:"after"})))},!1),e}},e=u.dataType||w2utils.settings.dataType;switch(e=t.dataType?t.dataType:e){case"HTTP":o.data=String(y.param(o.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]");break;case"HTTPJSON":o.data={request:JSON.stringify(o.data)};break;case"RESTFULL":0!==u.recid&&null!=u.recid&&(o.type="PUT"),o.data=String(y.param(o.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]");break;case"RESTFULLJSON":0!==u.recid&&null!=u.recid&&(o.type="PUT"),o.data=JSON.stringify(o.data),o.contentType="application/json";break;case"JSON":function a(o,e,t,i){for(var s in null==i&&(i=""),e){var n=""==i?s:"${p}[${prop}]",l=t&&t[s];!function e(t,i,s){if("object"==typeof t&&t instanceof File&&o.append(s,t),"object"==typeof t)if(t&&t.constructor===Array)for(var n=0;n<t.length;n++){var l=i&&i[n];e(t[n],l,s+"["+n+"]")}else a(o,t,i,s)}(e[s],l,n)}}o.type="POST",o.contentType="application/json",u.multipart?((l=new FormData).append("__body",JSON.stringify(o.data)),a(l,o.data),o.data=l,o.contentType=!1,o.processData=!1):o.data=JSON.stringify(o.data)}this.method&&(o.type=this.method),t.method&&(o.type=t.method),u.last.xhr=y.ajax(o).done(function(e,t,i){u.unlock();t=u.trigger({phase:"before",target:u.name,type:"save",xhr:i,status:t,data:e});!0!==t.isCancelled&&("error"===(e=null==(e=i.responseJSON)?{status:"error",message:w2utils.lang(u.msgNotJSON),responseText:i.responseText}:e).status?u.error(w2utils.lang(e.message)):u.original=null,u.trigger(y.extend(t,{phase:"after"})),u.refresh(),"function"==typeof d&&d(e,i))}).fail(function(e,t,i){i={status:t,error:i,rawResponseText:e.responseText},e=u.trigger({phase:"before",type:"error",error:i,xhr:e});!0!==e.isCancelled&&(console.log("ERROR: server communication failed. The server should return",{status:"success"},"OR",{status:"error",message:"error message"},", instead the AJAX request produced this: ",i),u.unlock(),u.trigger(y.extend(e,{phase:"after"})))}),u.trigger(y.extend(t,{phase:"after"}))}},50)):console.log("ERROR: Form cannot be saved because no url is defined."))},lock:function(e,t){var i=Array.prototype.slice.call(arguments,0);i.unshift(this.box),setTimeout(function(){w2utils.lock.apply(window,i)},10)},unlock:function(e){var t=this.box;setTimeout(function(){w2utils.unlock(t,e)},25)},lockPage:function(e,t){e=y(this.box).find(".page-"+e);return!!e.length&&(w2utils.lock(e,t),!0)},unlockPage:function(e,t){e=y(this.box).find(".page-"+e);return!!e.length&&(w2utils.unlock(e,t),!0)},goto:function(e){this.page!==e&&(null!=e&&(this.page=e),!0===y(this.box).data("auto-size")&&y(this.box).height(0),this.refresh())},generateHTML:function(){for(var e,t,i=[],s="",n=0;n<this.fields.length;n++){var l,o="",a=' tabindex="'+(l=this.tabindexBase+n+1)+'"',r=this.fields[n];null==r.html&&(r.html={}),null==r.options&&(r.options={}),null!=r.html.caption&&null==r.html.label&&(console.log("NOTICE: form field.html.caption property is deprecated, please use field.html.label. Field ->",r),r.html.label=r.html.caption),null==r.html.label&&(r.html.label=r.field),r.html=y.extend(!0,{label:"",span:6,attr:"",text:"",style:"",page:0,column:0},r.html),null==e&&(e=r.html.page),null==t&&(t=r.html.column);var d,u,c='<input id="'+r.field+'" name="'+r.field+'" class="w2ui-input" type="text" '+r.html.attr+a+">";switch(r.type){case"pass":case"password":c='<input id="'+r.field+'" name="'+r.field+'" class="w2ui-input" type = "password" '+r.html.attr+a+">";break;case"check":case"checks":null==r.options.items&&null!=r.html.items&&(r.options.items=r.html.items);var h=r.options.items,c="";0<(h=!y.isArray(h)?[]:h).length&&(h=w2obj.field.prototype.normMenu.call(this,h,r));for(var p=0;p<h.length;p++)c+='<label class="w2ui-box-label">  <input id="'+r.field+p+'" name="'+r.field+'" class="w2ui-input" type="checkbox" '+r.html.attr+(0===p?a:"")+' data-value="'+h[p].id+'" data-index="'+p+'"><span>&#160;'+h[p].text+"</span></label><br>";break;case"checkbox":c='<label class="w2ui-box-label">   <input id="'+r.field+'" name="'+r.field+'" class="w2ui-input" type="checkbox" '+r.html.attr+a+">   <span>"+r.html.label+"</span></label>";break;case"radio":c="",null==r.options.items&&null!=r.html.items&&(r.options.items=r.html.items);h=r.options.items;0<(h=!y.isArray(h)?[]:h).length&&(h=w2obj.field.prototype.normMenu.call(this,h,r));for(p=0;p<h.length;p++)c+='<label class="w2ui-box-label">  <input id="'+r.field+p+'" name="'+r.field+'" class="w2ui-input" type = "radio" '+r.html.attr+(0===p?a:"")+' value="'+h[p].id+'"><span>&#160;'+h[p].text+"</span></label><br>";break;case"select":c='<select id="'+r.field+'" name="'+r.field+'" class="w2ui-input" '+r.html.attr+a+">",null==r.options.items&&null!=r.html.items&&(r.options.items=r.html.items);h=r.options.items;0<(h=!y.isArray(h)?[]:h).length&&(h=w2obj.field.prototype.normMenu.call(this,h,r));for(p=0;p<h.length;p++)c+='<option value="'+h[p].id+'">'+h[p].text+"</option>";c+="</select>";break;case"textarea":c='<textarea id="'+r.field+'" name="'+r.field+'" class="w2ui-input" '+r.html.attr+a+"></textarea>";break;case"toggle":c='<input id="'+r.field+'" name="'+r.field+'" type="checkbox" '+r.html.attr+a+' class="w2ui-input w2ui-toggle"><div><div></div></div>';break;case"map":case"array":r.html.key=r.html.key||{},r.html.value=r.html.value||{},r.html.tabindex_str=a,c='<span style="float: right">'+(r.html.text||"")+'</span><input id="'+r.field+'" name="'+r.field+'" type="hidden" '+r.html.attr+a+'><div class="w2ui-map-container"></div>';break;case"html":case"div":case"custom":c='<div id="'+r.field+'" name="'+r.field+'" '+r.html.attr+a+">"+(r&&r.html&&r.html.html?r.html.html:"")+"</div>";break;case"empty":c=r&&r.html?(r.html.html||"")+(r.html.text||""):""}""!==s&&(e!=r.html.page||t!=r.html.column||r.html.group&&s!=r.html.group)&&(i[e][t]+="\n   </div>\n  </div>",s=""),r.html.group&&s!=r.html.group&&(u="",r.html.groupCollapsable&&(u='<span class="w2ui-icon-collapse" style="width: 15px; display: inline-block; position: relative; top: -2px;"></span>'),o+='\n <div class="w2ui-group">\n   <div class="w2ui-group-title" style="'+(r.html.groupTitleStyle||"")+(""!=u?"cursor: pointer; user-select: none":"")+'"'+(""!=u?'data-group="'+w2utils.base64encode(r.html.group)+'"':"")+(""!=u?"onclick=\"w2ui['"+this.name+"'].toggleGroup('"+r.html.group+"')\"":"")+">"+u+r.html.group+'</div>\n   <div class="w2ui-group-fields" style="'+(r.html.groupStyle||"")+'">',s=r.html.group),null==r.html.anchor?(d=null!=r.html.span?"w2ui-span"+r.html.span:"",u="<label"+("none"==(d=-1==r.html.span?"w2ui-span-none":d)?' style="display: none"':"")+">"+w2utils.lang("checkbox"!=r.type?r.html.label:r.html.text)+"</label>",r.html.label||(u=""),o+='\n      <div class="w2ui-field '+d+'" style="'+(r.hidden?"display: none;":"")+r.html.style+'">\n         '+u+("empty"===r.type?c:"\n         <div>"+c+("array"!=r.type&&"map"!=r.type?w2utils.lang("checkbox"!=r.type?r.html.text:""):"")+"</div>")+"\n      </div>"):(i[r.html.page].anchors=i[r.html.page].anchors||{},i[r.html.page].anchors[r.html.anchor]='<div class="w2ui-field w2ui-field-inline" style="'+(r.hidden?"display: none;":"")+r.html.style+'">'+("empty"===r.type?c:"<div>"+w2utils.lang("checkbox"!=r.type?r.html.label:r.html.text)+c+w2utils.lang("checkbox"!=r.type?r.html.text:"")+"</div>")+"</div>"),null==i[r.html.page]&&(i[r.html.page]={}),null==i[r.html.page][r.html.column]&&(i[r.html.page][r.html.column]=""),i[r.html.page][r.html.column]+=o,e=r.html.page,t=r.html.column}if(""!==s&&(i[e][t]+="\n   </div>\n  </div>"),this.tabs.tabs)for(p=0;p<this.tabs.tabs.length;p++)null==i[p]&&(i[p]=[]);var f="";if(!y.isEmptyObject(this.actions)){for(var g in f+='\n<div class="w2ui-buttons">',l=this.tabindexBase+this.fields.length+1,this.actions){var m=this.actions[g],w={text:"",style:"",class:""};y.isPlainObject(m)?(null==m.text&&null!=m.caption&&(console.log("NOTICE: form action.caption property is deprecated, please use action.text. Action ->",m),m.text=m.caption),m.text&&(w.text=m.text),m.style&&(w.style=m.style),m.class&&(w.class=m.class)):(w.text=g,-1!==["save","update","create"].indexOf(g.toLowerCase())?w.class="w2ui-btn-blue":w.class=""),f+='\n    <button name="'+g+'" class="w2ui-btn '+w.class+'" style="'+w.style+'" tabindex="'+l+'">'+w2utils.lang(w.text)+"</button>",l++}f+="\n</div>"}o="";for(var v=0;v<i.length;v++)o+='<div class="w2ui-page page-'+v+'" style="'+(0!==v?"display: none;":"")+this.pageStyle+'">',i[v].before&&(o+=i[v].before),o+='<div class="w2ui-column-container">',Object.keys(i[v]).sort().forEach(function(e,t){e==parseInt(e)&&(o+='<div class="w2ui-column col-'+e+'">'+(i[v][e]||"")+"\n</div>")}),o+="\n</div>",i[v].after&&(o+=i[v].after),o+="\n</div>",i[v].anchors&&Object.keys(i[v].anchors).forEach(function(e,t){o=o.replace(e,i[v].anchors[e])});return o+=f},toggleGroup:function(e,t){var i,s=y(this.box).find('.w2ui-group-title[data-group="'+w2utils.base64encode(e)+'"]');"none"==s.next().css("display")&&!0!==t?(s.next().slideDown(300),s.next().next().remove(),s.find("span").addClass("w2ui-icon-collapse").removeClass("w2ui-icon-expand")):(s.next().slideUp(300),i="width: "+s.next().css("width")+";padding-left: "+s.next().css("padding-left")+";padding-right: "+s.next().css("padding-right")+";margin-left: "+s.next().css("margin-left")+";margin-right: "+s.next().css("margin-right")+";",setTimeout(function(){s.next().after('<div style="height: 5px;'+i+'"></div>')},100),s.find("span").addClass("w2ui-icon-expand").removeClass("w2ui-icon-collapse"))},action:function(e,t){var i=this.actions[e],s=i;y.isPlainObject(i)&&i.onClick&&(s=i.onClick);i=this.trigger({phase:"before",target:e,type:"action",action:i,originalEvent:t});!0!==i.isCancelled&&("function"==typeof s&&s.call(this,t),this.trigger(y.extend(i,{phase:"after"})))},resize:function(){var e,t,i,s,n,l,o,a,r=this,d=this.trigger({phase:"before",target:this.name,type:"resize"});function u(){e.width(y(r.box).width()).height(y(r.box).height()),i.css("top",""!==r.header?w2utils.getSize(t,"height"):0),s.css("top",(""!==r.header?w2utils.getSize(t,"height"):0)+("object"==typeof r.toolbar&&y.isArray(r.toolbar.items)&&0<r.toolbar.items.length?w2utils.getSize(i,"height"):0)),n.css("top",(""!==r.header?w2utils.getSize(t,"height"):0)+("object"==typeof r.toolbar&&y.isArray(r.toolbar.items)&&0<r.toolbar.items.length?w2utils.getSize(i,"height")+5:0)+("object"==typeof r.tabs&&y.isArray(r.tabs.tabs)&&0<r.tabs.tabs.length?w2utils.getSize(s,"height")+5:0)),n.css("bottom",0<a.length?w2utils.getSize(a,"height"):0)}!0!==d.isCancelled&&(e=y(this.box).find("> div.w2ui-form-box"),t=y(this.box).find("> div .w2ui-form-header"),i=y(this.box).find("> div .w2ui-form-toolbar"),s=y(this.box).find("> div .w2ui-form-tabs"),n=y(this.box).find("> div .w2ui-page"),l=y(this.box).find("> div .w2ui-page.page-"+this.page),o=y(this.box).find("> div .w2ui-page.page-"+this.page+" > div"),a=y(this.box).find("> div .w2ui-buttons"),u(),this.autosize&&(0!==parseInt(y(this.box).height())&&!0!==y(this.box).data("auto-size")||(y(this.box).height((0<t.length?w2utils.getSize(t,"height"):0)+("object"==typeof this.tabs&&y.isArray(this.tabs.tabs)&&0<this.tabs.tabs.length?w2utils.getSize(s,"height"):0)+("object"==typeof this.toolbar&&y.isArray(this.toolbar.items)&&0<this.toolbar.items.length?w2utils.getSize(i,"height"):0)+(0<n.length?w2utils.getSize(o,"height")+w2utils.getSize(l,"+height")+12:0)+(0<a.length?w2utils.getSize(a,"height"):0)),y(this.box).data("auto-size",!0)),u()),this.toolbar&&this.toolbar.resize&&this.toolbar.resize(),this.tabs&&this.tabs.resize&&this.tabs.resize(),r.trigger(y.extend(d,{phase:"after"})))},refresh:function(){var e=(new Date).getTime(),d=this;if(this.box&&this.isGenerated&&null!=y(this.box).html()){var t=this.trigger({phase:"before",target:this.name,type:"refresh",page:this.page,field:l,fields:arguments});if(!0!==t.isCancelled){var i=Array.from(this.fields.keys());0<arguments.length?i=Array.from(arguments).map(function(e,t){return"string"!=typeof e&&console.log("ERROR: Arguments in refresh functions should be field names"),this.get(e,!0)}.bind(this)).filter(function(e,t){return null!=e}):(y(this.box).find("input, textarea, select").each(function(e,t){var i=null!=y(t).attr("name")?y(t).attr("name"):y(t).attr("id"),s=d.get(i);if(s){var n=y(t).closest(".w2ui-page");if(0<n.length)for(var l=0;l<100;l++)if(n.hasClass("page-"+l)){s.page=l;break}}}),y(this.box).find(".w2ui-page").hide(),y(this.box).find(".w2ui-page.page-"+this.page).show(),y(this.box).find(".w2ui-form-header").html(this.header),"object"==typeof this.tabs&&y.isArray(this.tabs.tabs)&&0<this.tabs.tabs.length?(y("#form_"+this.name+"_tabs").show(),this.tabs.active=this.tabs.tabs[this.page].id,this.tabs.refresh()):y("#form_"+this.name+"_tabs").hide(),"object"==typeof this.toolbar&&y.isArray(this.toolbar.items)&&0<this.toolbar.items.length?(y("#form_"+this.name+"_toolbar").show(),this.toolbar.refresh()):y("#form_"+this.name+"_toolbar").hide());for(var s,n=0;n<i.length;n++)null==(l=this.fields[i[n]]).name&&null!=l.field&&(l.name=l.field),null==l.field&&null!=l.name&&(l.field=l.name),l.$el=y(this.box).find('[name="'+String(l.name).replace(/\\/g,"\\\\")+'"]'),l.el=l.$el[0],l.el&&(l.el.id=l.name),(m=y(l).data("w2field"))&&m.clear(),y(l.$el).off(".w2form").on("change.w2form",function(e){var t=this,i=d.get(this.name);if(null!=i)if(1!=y(this).data("skip_change")){var s,n=this.value;if(null==(r=d.getValue(this.name))&&(r=""),-1!==["list","enum","file"].indexOf(i.type)&&y(this).data("selected")){var l=y(this).data("selected"),o=d.getValue(this.name);if(y.isArray(l))for(var n=[],a=0;a<l.length;a++)n[a]=y.extend(!0,{},l[a]);if(y.isPlainObject(l)&&(n=y.extend(!0,{},l)),y.isArray(o))for(var r=[],a=0;a<o.length;a++)r[a]=y.extend(!0,{},o[a]);y.isPlainObject(o)&&(r=y.extend(!0,{},o))}-1!==["toggle","checkbox"].indexOf(i.type)&&(n=!!y(this).prop("checked")&&("on"===y(this).prop("value")||y(this).prop("value"))),-1!==["check","checks"].indexOf(i.type)&&(n=(r=!Array.isArray(r)?[]:r).slice(),s=i.options.items[y(this).attr("data-index")],y(this).prop("checked")?n.push(s.id):n.splice(n.indexOf(s.id),1)),(n=-1!==["int","float","percent","money","currency"].indexOf(i.type)?y(this).data("w2field").clean(n):n)!==r&&(!0===(s=d.trigger({phase:"before",target:this.name,type:"change",value_new:n,value_previous:r,originalEvent:e})).isCancelled&&(s.value_new=d.getValue(this.name),y(this).val()!==s.value_new&&(y(this).data("skip_change",!0),setTimeout(function(){y(t).data("skip_change",!1)},10)),y(this).val(s.value_new)),e=s.value_new,-1!==["enum","file"].indexOf(i.type)&&0<e.length&&(i=y(i.el).data("w2field").helpers.multi,y(i).removeClass("w2ui-error")),(""===e||null==e||y.isArray(e)&&0===e.length||y.isPlainObject(e)&&y.isEmptyObject(e))&&(e=null),d.setValue(this.name,e),d.trigger(y.extend(s,{phase:"after"})))}else y(this).data("skip_change",!1)}).on("input.w2form",function(e){var t=this.value;"checkbox"==e.target.type&&(t=e.target.checked),null==d.original&&(y.isEmptyObject(d.record)?d.original={}:d.original=y.extend(!0,{},d.record));e=d.trigger({phase:"before",target:this.name,type:"input",value_new:t,originalEvent:e});!0!==e.isCancelled&&d.trigger(y.extend(e,{phase:"after"}))}),l.required?y(l.el).parent().parent().addClass("w2ui-required"):y(l.el).parent().parent().removeClass("w2ui-required"),null!=l.disabled&&(s=y(l.el),l.disabled?(null==s.data("w2ui-tabIndex")&&s.data("w2ui-tabIndex",s.prop("tabIndex")),y(l.el).prop("readonly",!0).prop("tabindex",-1).closest(".w2ui-field").addClass("w2ui-disabled")):y(l.el).prop("readonly",!1).prop("tabIndex",s.data("w2ui-tabIndex")).closest(".w2ui-field").removeClass("w2ui-disabled")),m=(m=l.el)||y(this.box).find("#"+l.field),l.hidden?y(m).closest(".w2ui-field").hide():y(m).closest(".w2ui-field").show();y(this.box).find("button, input[type=button]").each(function(e,t){y(t).off("click").on("click",function(e){var t=this.value;this.id&&(t=this.id),this.name&&(t=this.name),d.action(t,e)})});for(n=0;n<i.length;n++){var l=this.fields[i[n]],o=null!=this.getValue(l.name)?this.getValue(l.name):"";if(l.el)switch(y(l.el).hasClass("w2ui-input")||y(l.el).addClass("w2ui-input"),l.type=String(l.type).toLowerCase(),l.options||(l.options={}),l.type){case"text":case"textarea":case"email":case"pass":case"password":l.el.value=o;break;case"int":case"float":case"money":case"currency":case"percent":l.el.value=o,y(l.el).w2field(y.extend({},l.options,{type:l.type}));break;case"hex":case"alphanumeric":case"color":case"date":case"time":l.el.value=o,y(l.el).w2field(y.extend({},l.options,{type:l.type}));break;case"toggle":w2utils.isFloat(o)&&(o=parseFloat(o)),y(l.el).prop("checked",!!o),this.setValue(l.name,o||!1);break;case"radio":y(l.$el).prop("checked",!1).each(function(e,t){y(t).val()==o&&y(t).prop("checked",!0)});break;case"checkbox":y(l.el).prop("checked",!!o),!0!==l.disabled&&!1!==l.disabled||y(l.el).prop("disabled",!!l.disabled);break;case"check":case"checks":Array.isArray(o)&&o.forEach(function(e){y(l.el).closest("div").find('[data-value="'+e+'"]').prop("checked",!0)}),l.disabled?y(l.el).closest("div").find("input[type=checkbox]").prop("disabled",!0):y(l.el).closest("div").find("input[type=checkbox]").removeProp("disabled");break;case"list":case"combo":if("list"===l.type){var a,r=y.isPlainObject(o)?o.id:y.isPlainObject(l.options.selected)?l.options.selected.id:o;l.options.items||(l.options.items=[]),"function"==typeof(a=l.options.items)&&(a=a());var u=!1;if(Array.isArray(a))for(var c=0;c<a.length;c++){var h=a[c];if(h.id==r){o=y.extend(!0,{},h),d.setValue(l.name,o),u=!0;break}}u||null==o||""===o||l.$el.data("find_selected",o)}else"combo"!==l.type||y.isPlainObject(o)?y.isPlainObject(o)&&null!=o.text?l.el.value=o.text:l.el.value="":l.el.value=o;y.isPlainObject(o)||(o={}),y(l.el).w2field(y.extend({},l.options,{type:l.type,selected:o}));break;case"enum":case"file":var p=[],u=!1;y.isArray(o)||(o=[]),"function"!=typeof l.options.items&&(y.isArray(l.options.items)||(l.options.items=[]),o.forEach(function(t){l.options.items.forEach(function(e){e&&(e.id==t||y.isPlainObject(t)&&e.id==t.id)&&(p.push(y.isPlainObject(e)?y.extend(!0,{},e):e),u=!0)})})),u||null==o||0===o.length||(l.$el.data("find_selected",o),p=o);var f=y.extend({},l.options,{type:l.type,selected:p});Object.keys(l.options).forEach(function(e){"function"==typeof l.options[e]&&(f[e]=l.options[e])}),y(l.el).w2field(f);break;case"select":if(null!=(a=l.options.items)&&0<a.length){a=w2obj.field.prototype.normMenu.call(this,a,l),y(l.el).html("");for(var g=0;g<a.length;g++)y(l.el).append('<option value="'+a[g].id+'">'+a[g].text+"</option")}y(l.el).val(o);break;case"map":case"array":"map"!=l.type||null!=o&&y.isPlainObject(o)||(this.setValue(l.field,{}),o=this.getValue(l.field)),"array"!=l.type||null!=o&&Array.isArray(o)||(this.setValue(l.field,[]),o=this.getValue(l.field)),function(h,p){p.el.mapAdd=function(e,t,i){var s=(e.disabled?" readOnly ":"")+(e.html.tabindex_str||""),e='<div class="w2ui-map-field" style="margin-bottom: 5px"><input id="'+e.field+"_key_"+i+'" data-cnt="'+i+'" type="text" '+e.html.key.attr+s+' class="w2ui-input w2ui-map key">'+(e.html.key.text||"")+'<input id="'+e.field+"_value_"+i+'" data-cnt="'+i+'" type="text" '+e.html.value.attr+s+' class="w2ui-input w2ui-map value">'+(e.html.value.text||"")+"</div>";t.append(e)},p.el.mapRefresh=function(u,c){var s,n,l=1;"map"==p.type&&(null==(u=!y.isPlainObject(u)?{}:u)._order&&(u._order=Object.keys(u)),t=u._order),(t="array"==p.type?(u=!Array.isArray(u)?[]:u).map(function(e){return e.key}):t).forEach(function(t){s=c.find("#"+w2utils.escapeId(p.name)+"_key_"+l),n=c.find("#"+w2utils.escapeId(p.name)+"_value_"+l),0!=s.length&&0!=n.length||(p.el.mapAdd(p,c,l),s=c.find("#"+w2utils.escapeId(p.name)+"_key_"+l),n=c.find("#"+w2utils.escapeId(p.name)+"_value_"+l));var e,i=u[t];"array"!=p.type||0<(e=u.filter(function(e){return e.key==t})).length&&(i=e[0].value),s.val(t),n.val(i),!0!==p.disabled&&!1!==p.disabled||(s.prop("readOnly",!!p.disabled),n.prop("readOnly",!!p.disabled)),s.parents(".w2ui-map-field").attr("data-key",t),l++});var e=c.find("#"+w2utils.escapeId(p.name)+"_key_"+l).parent(),t=c.find("#"+w2utils.escapeId(p.name)+"_key_"+(l+1)).parent();0!==e.length||s&&(!0===s.prop("readOnly")||!0===s.prop("disabled"))||p.el.mapAdd(p,c,l),1==e.length&&1==t.length&&(e.removeAttr("data-key"),e.find(".key").val(t.find(".key").val()),e.find(".value").val(t.find(".value").val()),t.remove()),!0!==p.disabled&&!1!==p.disabled||(e.find(".key").prop("readOnly",!!p.disabled),e.find(".value").prop("readOnly",!!p.disabled)),y(p.el).next().find("input.w2ui-map").off(".mapChange").on("keyup.mapChange",function(e){var t=y(e.target).parents(".w2ui-map-field");13==e.keyCode&&t.next().find("input.key").focus()}).on("change.mapChange",function(){var e=y(event.target).parents(".w2ui-map-field"),i=e.attr("data-key"),t=e.find(".key").val(),s=e.find(".value").val(),n={},l={},o=null,a=null;n[t]=s,"array"==p.type&&(u.forEach(function(e,t){e.key==i&&(a=t)}),o=u[a]),null!=i&&"map"==p.type&&(l[i]=u[i]),null!=i&&"array"==p.type&&(l[i]=o.value);n=h.trigger({phase:"before",target:p.field,type:"change",originalEvent:event,value_new:n,value_previous:l});if(!0!==n.isCancelled){if("map"==p.type){delete u[i];l=u._order.indexOf(i);if(""!=t){if(null!=u[t]){for(var r,d=0;r=t+ ++d,null!=u[r];);t=r,e.find(".key").val(r)}u[t]=s,e.attr("data-key",t),-1!=l?u._order[l]=t:u._order.push(t)}else u._order.splice(l,1),e.find(".value").val("")}else"array"==p.type&&(""!=t?null==o?u.push({key:t,value:s}):(o.key=t,o.value=s):u.splice(a,1));h.setValue(p.field,u),p.el.mapRefresh(u,c),h.trigger(y.extend(n,{phase:"after"}))}})},p.el.mapRefresh(o,y(p.el).parent().find(".w2ui-map-container"))}(this,l);break;case"div":case"custom":y(l.el).html(o);break;case"html":case"empty":break;default:y(l.el).val(o),y(l.el).w2field(y.extend({},l.options,{type:l.type}))}}for(var m=y(this.box).find(".w2ui-page"),c=0;c<m.length;c++)1<y(m[c]).find("> *").length&&y(m[c]).wrapInner("<div></div>");return this.trigger(y.extend(t,{phase:"after"})),this.resize(),(new Date).getTime()-e}}},render:function(e){var t=(new Date).getTime(),i=this;if("object"==typeof e&&(0<y(this.box).find("#form_"+this.name+"_tabs").length&&y(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-form").html(""),this.box=e),this.isGenerated&&this.box){var s=this.trigger({phase:"before",target:this.name,type:"render",box:null!=e?e:this.box});if(!0!==s.isCancelled){e='<div class="w2ui-form-box">'+(""!==this.header?'<div class="w2ui-form-header">'+this.header+"</div>":"")+'    <div id="form_'+this.name+'_toolbar" class="w2ui-form-toolbar" style="display: none"></div>    <div id="form_'+this.name+'_tabs" class="w2ui-form-tabs" style="display: none"></div>'+this.formHTML+"</div>";return y(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-form").html(e),0<y(this.box).length&&(y(this.box)[0].style.cssText+=this.style),"function"!=typeof this.toolbar.render&&(this.toolbar=y().w2toolbar(y.extend({},this.toolbar,{name:this.name+"_toolbar",owner:this})),this.toolbar.on("click",function(e){e=i.trigger({phase:"before",type:"toolbar",target:e.target,originalEvent:e});!0!==e.isCancelled&&i.trigger(y.extend(e,{phase:"after"}))})),"object"==typeof this.toolbar&&"function"==typeof this.toolbar.render&&this.toolbar.render(y("#form_"+this.name+"_toolbar")[0]),"function"!=typeof this.tabs.render&&(this.tabs=y().w2tabs(y.extend({},this.tabs,{name:this.name+"_tabs",owner:this,active:this.tabs.active})),this.tabs.on("click",function(e){i.goto(this.get(e.target,!0))})),"object"==typeof this.tabs&&"function"==typeof this.tabs.render&&(this.tabs.render(y("#form_"+this.name+"_tabs")[0]),this.tabs.active&&this.tabs.click(this.tabs.active)),this.trigger(y.extend(s,{phase:"after"})),this.resize(),("object"!=typeof this.url?this.url:this.url.get)&&0!==this.recid&&null!=this.recid?this.request():this.refresh(),0===y(".w2ui-layout").length&&(this.tmp_resize=function(e){null==w2ui[i.name]?y(window).off("resize.w2uiResize",i.tmp_resize):w2ui[i.name].resize()},y(window).off("resize.w2uiResize").on("resize.w2uiResize",i.tmp_resize)),-1!=this.focus&&setTimeout(function(){0===y(i.box).find("input, select, textarea").length?setTimeout(focusEl,500):i.applyFocus()},50),(new Date).getTime()-t}}},applyFocus:function(){for(var e=this.focus,t=y(this.box).find("div:not(.w2ui-field-helper) > input, select, textarea, div > label:nth-child(1) > :radio").not(".file-input");y(t[e]).is(":hidden")&&t.length>=e;)e++;t[e]&&t[e].focus()},destroy:function(){var e=this.trigger({phase:"before",target:this.name,type:"destroy"});!0!==e.isCancelled&&("object"==typeof this.toolbar&&this.toolbar.destroy&&this.toolbar.destroy(),"object"==typeof this.tabs&&this.tabs.destroy&&this.tabs.destroy(),0<y(this.box).find("#form_"+this.name+"_tabs").length&&y(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-form").html(""),delete w2ui[this.name],this.trigger(y.extend(e,{phase:"after"})),y(window).off("resize","body"))}},y.extend(h.prototype,w2utils.event),w2obj.form=h}(jQuery),function(e,t){if("function"==typeof define&&define.amd)return define(function(){return t});if("undefined"!=typeof exports){if("undefined"!=typeof module&&module.exports)return exports=module.exports=t;e=exports}for(var i in t)e[i]=t[i]}(this,{w2ui:w2ui,w2obj:w2obj,w2utils:w2utils,w2popup:w2popup,w2alert:w2alert,w2confirm:w2confirm,w2prompt:w2prompt});