var Layout=function(){var o="layouts/layout5/img/",e="layouts/layout5/css/",n=App.getResponsiveBreakpoint("md"),t=function(){var o=$(".page-content"),e=($(".copyright"),$("body"),App.getViewPort().height-$(".copyright").outerHeight()-$(".page-header").outerHeight()-50);o.css("min-height",e)},a=function(){$(window).scrollTop()>60?$("body").addClass("page-on-scroll"):$("body").removeClass("page-on-scroll")},i=function(){var o=function(){var o=$(window).scrollTop();o>100?$(".go2top").show():$(".go2top").hide()};o(),navigator.userAgent.match(/iPhone|iPad|iPod/i)?$(window).bind("touchend touchcancel touchleave",function(e){o()}):$(window).scroll(function(){o()}),$(".go2top").click(function(o){o.preventDefault(),$("html, body").animate({scrollTop:0},600)})},r=function(){$(".page-header .navbar-nav > .dropdown-fw, .page-header .navbar-nav > .more-dropdown, .page-header .navbar-nav > .dropdown > .dropdown-menu  > .dropdown").click(function(o){if(App.getViewPort().width>n){if($(this).hasClass("more-dropdown")||$(this).hasClass("more-dropdown-sub"))return;o.stopPropagation()}else o.stopPropagation();var e=$(this).parent().find("> .dropdown");if(App.getViewPort().width<n)$(this).hasClass("open")?e.removeClass("open"):(e.removeClass("open"),$(this).addClass("open"),App.scrollTo($(this)));else{if($(this).hasClass("more-dropdown"))return;e.removeClass("open"),$(this).addClass("open"),App.scrollTo($(this))}}),$(".page-header .navbar-nav .more-dropdown-sub .dropdown-menu, .page-header .navbar-nav .dropdown-sub .dropdown-menu").click(function(){})},d=function(){var o=App.getViewPort().width,e=$(".page-header .navbar-nav");o>=n&&"desktop"!==e.data("breakpoint")?(e.data("breakpoint","desktop"),$('[data-hover="extended-dropdown"]',e).not(".hover-initialized").each(function(){$(this).dropdownHover(),$(this).addClass("hover-initialized")})):o<n&&"mobile"!==e.data("breakpoint")&&(e.data("breakpoint","mobile"),$('[data-hover="extended-dropdown"].hover-initialized',e).each(function(){$(this).unbind("hover"),$(this).parent().unbind("hover").find(".dropdown-submenu").each(function(){$(this).unbind("hover")}),$(this).removeClass("hover-initialized")}))};return{init:function(){t(),i(),a(),r(),d(),App.addResizeHandler(d),$(window).scroll(function(){a()})},getLayoutImgPath:function(){return App.getAssetsPath()+o},getLayoutCssPath:function(){return App.getAssetsPath()+e}}}();jQuery(document).ready(function(){Layout.init()});