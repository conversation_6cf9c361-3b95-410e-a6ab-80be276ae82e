var Layout=function(){var e="layouts/layout3/img/",t="layouts/layout3/css/",o=App.getResponsiveBreakpoint("md"),n=function(){$(".page-header").on("click",".search-form",function(e){$(this).addClass("open"),$(this).find(".form-control").focus(),$(".page-header .search-form .form-control").on("blur",function(e){$(this).closest(".search-form").removeClass("open"),$(this).unbind("blur")})}),$(".page-header").on("keypress",".hor-menu .search-form .form-control",function(e){if(13==e.which)return $(this).closest(".search-form").submit(),!1}),$(".page-header").on("mousedown",".search-form.open .submit",function(e){e.preventDefault(),e.stopPropagation(),$(this).closest(".search-form").submit()}),$("body").on("click",".page-header-top-fixed .page-header-top .menu-toggler",function(){App.scrollTop()})},i=function(){$(".page-header .menu-toggler").on("click",function(e){if(App.getViewPort().width<o){var t=$(".page-header .page-header-menu");t.is(":visible")?t.slideUp(300):t.slideDown(300),$("body").hasClass("page-header-top-fixed")&&App.scrollTop()}}),$(".hor-menu .menu-dropdown > a, .hor-menu .dropdown-submenu > a").on("click",function(e){App.getViewPort().width<o&&$(this).next().hasClass("dropdown-menu")&&(e.stopPropagation(),$(this).parent().hasClass("opened")?$(this).parent().removeClass("opened"):$(this).parent().addClass("opened"))}),$(".hor-menu li > a").on("click",function(e){App.getViewPort().width<o&&($(this).parent("li").hasClass("classic-menu-dropdown")||$(this).parent("li").hasClass("mega-menu-dropdown")||$(this).parent("li").hasClass("dropdown-submenu")||($(".page-header .page-header-menu").slideUp(300),App.scrollTop()))}),$(document).on("click",".mega-menu-dropdown .dropdown-menu, .classic-menu-dropdown .dropdown-menu",function(e){e.stopPropagation()}),$(window).scroll(function(){var e=75;$("body").hasClass("page-header-menu-fixed")&&($(window).scrollTop()>e?$(".page-header-menu").addClass("fixed"):$(".page-header-menu").removeClass("fixed")),$("body").hasClass("page-header-top-fixed")&&($(window).scrollTop()>e?$(".page-header-top").addClass("fixed"):$(".page-header-top").removeClass("fixed"))})},a=function(e,t,o){var n=encodeURI(location.hash).toLowerCase(),i=$(".hor-menu");"click"===e||"set"===e?t=$(t):"match"===e&&i.find("li > a").each(function(){var e=$(this).attr("ui-sref");if(o&&e){if(o.is(e))return void(t=$(this))}else{var i=$(this).attr("href");if(i&&(i=i.toLowerCase(),i.length>1&&n.substr(1,i.length-1)==i.substr(1)))return void(t=$(this))}}),t&&0!=t.size()&&"javascript:;"!=t.attr("href")&&"javascript:;"!=t.attr("ui-sref")&&"#"!=t.attr("href")&&"#"!=t.attr("ui-sref")&&(i.find("li.active").removeClass("active"),i.find("li > a > .selected").remove(),i.find("li.open").removeClass("open"),t.parents("li").each(function(){$(this).addClass("active"),1===$(this).parent("ul.navbar-nav").size()&&$(this).find("> a").append('<span class="selected"></span>')}))},s=function(){var e=App.getViewPort().width;$(".page-header-menu");e>=o?$(".page-header-menu").css("display","block"):e<o&&$(".page-header-menu").css("display","none")},r=function(){$("body").height()<App.getViewPort().height&&(height=App.getViewPort().height-$(".page-header").outerHeight()-($(".page-container").outerHeight()-$(".page-content").outerHeight())-$(".page-prefooter").outerHeight()-$(".page-footer").outerHeight(),$(".page-content").css("min-height",height))},p=function(){var e=100,t=500;navigator.userAgent.match(/iPhone|iPad|iPod/i)?$(window).bind("touchend touchcancel touchleave",function(o){$(this).scrollTop()>e?$(".scroll-to-top").fadeIn(t):$(".scroll-to-top").fadeOut(t)}):$(window).scroll(function(){$(this).scrollTop()>e?$(".scroll-to-top").fadeIn(t):$(".scroll-to-top").fadeOut(t)}),$(".scroll-to-top").click(function(e){return e.preventDefault(),$("html, body").animate({scrollTop:0},t),!1})};return{initHeader:function(e){n(),i(),App.addResizeHandler(s),App.isAngularJsApp()&&a("match",null,e)},initContent:function(){r()},initFooter:function(){p()},init:function(){this.initHeader(),this.initContent(),this.initFooter()},setMainMenuActiveLink:function(e,t){a(e,t)},setAngularJsMainMenuActiveLink:function(e,t,o){a(e,t,o)},closeMainMenu:function(){$(".hor-menu").find("li.open").removeClass("open"),App.getViewPort().width<o&&$(".page-header-menu").is(":visible")&&$(".page-header .menu-toggler").click()},getLayoutImgPath:function(){return App.getAssetsPath()+e},getLayoutCssPath:function(){return App.getAssetsPath()+t}}}();App.isAngularJsApp()===!1&&jQuery(document).ready(function(){Layout.init()});