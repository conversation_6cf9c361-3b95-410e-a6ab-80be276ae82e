var Layout=function(){var e="layouts/layout6/img/",t="layouts/layout6/css/",o=App.getResponsiveBreakpoint("md"),a=(App.getResponsiveBreakpoint("sm"),function(){$(".page-header").on("click",".search-form",function(e){$(this).addClass("open"),$(this).find(".form-control").focus(),$(".page-header .search-form .form-control").on("blur",function(e){$(this).closest(".search-form").removeClass("open"),$(this).unbind("blur")})}),$(".page-header").on("keypress",".hor-menu .search-form .form-control",function(e){if(13==e.which)return $(this).closest(".search-form").submit(),!1}),$(".page-header").on("mousedown",".search-form.open .submit",function(e){e.preventDefault(),e.stopPropagation(),$(this).closest(".search-form").submit()})}),n=function(){var e=function(){var e=$(window).scrollTop();e>100?$(".go2top").show():$(".go2top").hide()};e(),navigator.userAgent.match(/iPhone|iPad|iPod/i)?$(window).bind("touchend touchcancel touchleave",function(t){e()}):$(window).scroll(function(){e()}),$(".go2top").click(function(e){e.preventDefault(),$("html, body").animate({scrollTop:0},600)})},s=function(){$(".page-sidebar").on("click","li > a",function(e){if(!(App.getViewPort().width>=o&&1===$(this).parents(".page-sidebar-menu-hover-submenu").size())){if($(this).next().hasClass("sub-menu")===!1)return void(App.getViewPort().width<o&&$(".page-sidebar").hasClass("in")&&$(".page-header .responsive-toggler").click());var t=$(this).parent().parent(),a=$(this),n=$(".page-sidebar-menu"),s=$(this).next(),i=n.data("auto-scroll"),r=parseInt(n.data("slide-speed")),p=n.data("keep-expanded");p!==!0&&(t.children("li.open").children("a").children(".arrow").removeClass("open"),t.children("li.open").children(".sub-menu:not(.always-open)").slideUp(r),t.children("li.open").removeClass("open"));var l=-200;s.is(":visible")?($(".arrow",$(this)).removeClass("open"),$(this).parent().removeClass("open"),s.slideUp(r,function(){i===!0&&$("body").hasClass("page-sidebar-closed")===!1&&($("body").hasClass("page-sidebar-fixed")?n.slimScroll({scrollTo:a.position().top}):App.scrollTo(a,l))})):($(".arrow",$(this)).addClass("open"),$(this).parent().addClass("open"),s.slideDown(r,function(){i===!0&&$("body").hasClass("page-sidebar-closed")===!1&&($("body").hasClass("page-sidebar-fixed")?n.slimScroll({scrollTo:a.position().top}):App.scrollTo(a,l))})),e.preventDefault()}}),$(".page-sidebar").on("click"," li > a.ajaxify",function(e){e.preventDefault(),App.scrollTop();var t=$(this).attr("href"),a=$(".page-sidebar ul"),n=($(".page-content"),$(".page-content .page-content-body"));a.children("li.active").removeClass("active"),a.children("arrow.open").removeClass("open"),$(this).parents("li").each(function(){$(this).addClass("active"),$(this).children("a > span.arrow").addClass("open")}),$(this).parents("li").addClass("active"),App.getViewPort().width<o&&$(".page-sidebar").hasClass("in")&&$(".page-header .responsive-toggler").click(),App.startPageLoading();var s=$(this);$.ajax({type:"GET",cache:!1,url:t,dataType:"html",success:function(e){0===s.parents("li.open").size()&&$(".page-sidebar-menu > li.open > a").click(),App.stopPageLoading(),n.html(e),Layout.fixContentHeight(),App.initAjax()},error:function(e,t,o){App.stopPageLoading(),n.html("<h4>Could not load the requested content.</h4>")}})}),$(".page-content").on("click",".ajaxify",function(e){e.preventDefault(),App.scrollTop();var t=$(this).attr("href"),a=($(".page-content"),$(".page-content .page-content-body"));App.startPageLoading(),App.getViewPort().width<o&&$(".page-sidebar").hasClass("in")&&$(".page-header .responsive-toggler").click(),$.ajax({type:"GET",cache:!1,url:t,dataType:"html",success:function(e){App.stopPageLoading(),a.html(e),Layout.fixContentHeight(),App.initAjax()},error:function(e,t,o){a.html("<h4>Could not load the requested content.</h4>"),App.stopPageLoading()}})}),$(document).on("click",".page-header-fixed-mobile .responsive-toggler",function(){App.scrollTop()})};return{init:function(){a(),n(),s()},getLayoutImgPath:function(){return App.getAssetsPath()+e},getLayoutCssPath:function(){return App.getAssetsPath()+t}}}();jQuery(document).ready(function(){Layout.init()});