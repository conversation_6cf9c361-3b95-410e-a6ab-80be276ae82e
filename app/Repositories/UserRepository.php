<?php

/**
 * Created by PhpStorm.
 * User: pag
 * Date: 29/06/2016
 * Time: 00:32
 */

namespace App\Repositories;

use App\Evento;
use App\Models\User;
use App\Models\Pais;
use App\Models\Estado_user;
use DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Yajra\Datatables\Datatables;

class UserRepository
{


    protected $mensagem;
    protected $id_evento;
    protected $id_clube;

    // protected $estadosUser = array('Inactivo', 'Activo', 'A aguardar STAM');


    public function checkLogin(string $stam, string $password): bool
    {
        $result = DB::table('users')
            ->select('id', 'password')
            ->where('stam', $stam)
            ->first();
        $output = false;
        if ($result === null) {
            $output = false;
        } else {
            $output = Hash::check($password, $result->password);
        }
        DB::disconnect();
        return $output;
    }

    public function getEstados()
    {

        $estados = Estado_user::pluck('estado', 'id');
        $estados->prepend("-- Estados --");
        //dd($estados);

        return $estados;

    }

    public function getPaisByUserId($id_user)
    {

        $pais_user = DB::table('users')
            ->join('paises', 'users.pais_id', '=', 'paises.id')
            ->select('users.id', 'users.pais_id', 'paises.pais')
            ->where('users.id', $id_user)
            ->first();

        return $pais_user;


    }

    public function getPaises()
    {

        //$paises = Pais::pluck('pais', 'id');
        $paises = DB::table('paises')
            ->orderBy('pais', 'asc')
            ->select('id', 'pais')
            ->get();


        //dd($paises);


        //$paises->prepend("-- Países --");

        //$paises = array_sort($paises, null);

        //$paises = ['0'=>'Escolha um país'] + $paises;
        //dd($paises);

        return $paises;
    }



//      TODO: VER SE É UTILIZADO POR ALGUMA COISA
//    public function getUsers()
//    {
//
//        $users = DB::table('users')
//            ->join('paises', 'users.pais_id', '=', 'paises.id')
//            ->select('users.name', 'users.email', 'users.estado', 'paises.pais as pais')
//            ->get();
//
//
//        return $users;
//    }

    public function getUserList()
    {

        $users = DB::table('users')
            ->join('paises', 'users.pais_id', '=', 'paises.id')
            ->select('users.name', 'users.email', 'users.estado', 'paises.pais as pais')
            ->get();


        return $users;
    }

//  TODO: VER SE É UTILIZADO
//    public function getUsersByAjax2()
//    {
//
//
//        $users = User::select(['id', 'stam', 'estado', 'name', 'email'])->get();
//
//
//        return Datatables::of($users)
//            ->addColumn('action', function ($user) {
//                return '<a href="#edit-' . $user->id . '" class="btn btn-xs btn-primary"><i class="glyphicon glyphicon-edit"></i> Editar</a>';
//            })
//            ->editColumn('id', 'ID: {{$id}}')
//            ->make(true);
//
//    }

    public function getUsersByAjaxAdminPending()
    {


        //$users = User::select(['id', 'stam', 'estado_id', 'name', 'email'])->get();


        $users = DB::table('users as u')
            ->select(['u.*', 'p.pais', 'eu.estado'])
            ->join('paises as p', 'u.pais_id', '=', 'p.id')
            ->join('estados_user as eu', 'u.estado_user_id', '=', 'eu.id')
            ->whereIn('estado_user_id', [3, 4])
            ->get();



        //dd($users);


        $out = ["status" => "success", "records" => $users];

        return $out;


    }

    public function activarCriadoresPendentes()
    {

        DB::table('users')
            ->where('estado_user_id', 4)
            ->update(['estado_user_id' => 1]);


        return "success";

    }


//    public function getUsersByAjaxAdmin()
//    {
//
//
//        //$users = User::select(['id', 'stam', 'estado_id', 'name', 'email'])->get();
//
//
//        $users = DB::table('users as u')
//            ->select(['u.*', 'p.pais', 'eu.estado'])
//            ->join('paises as p', 'u.pais_id', '=', 'p.id')
//            ->join('estados_user as eu', 'u.estado_user_id', '=', 'eu.id')
//            ->get();
//
//
//        //dd($users);
//
//        $out = ["status" => "success", "total" => count($users), "records" => $users];
//
//        return $out;
//
//    }


    public function getUsersByAjaxAdmin($estado_id, $pais_id)
    {


        //$users = User::select(['id', 'stam', 'estado_id', 'name', 'email'])->get();

        $estadocond = ($estado_id > 0) ? 'u.estado_user_id=' . $estado_id : 1;
        $paiscond = ($pais_id > 0) ? 'u.pais_id=' . $pais_id : 1;


        $users = DB::select(DB::raw("SELECT
p.pais,
p.pais_abrev,
u.id,
u.stam,
u.estado_user_id,
u.`name`,
eu.estado,
u.email,
u.pais_id
FROM
users AS u
INNER JOIN paises AS p ON u.pais_id = p.id
INNER JOIN estados_user AS eu ON u.estado_user_id = eu.id
where " . $estadocond . " and " . $paiscond
        ));


        $out = ["status" => "success", "total" => count($users), "records" => $users];
        DB::disconnect();
        return $out;

    }

    public function getUsersByAjaxGestorN($pais_id)
    {

        $users = DB::select(DB::raw("SELECT
p.pais,
p.pais_abrev,
u.id,
u.stam,
u.estado_user_id,
u.`name`,
u.estado_user_id,
eu.estado as estado1,
u.email,
u.pais_id
FROM
users AS u
INNER JOIN paises AS p ON u.pais_id = p.id
INNER JOIN estados_user AS eu ON u.estado_user_id = eu.id
where u.pais_id =" . $pais_id
        ));

        //dd($users);

        $data = [];
        foreach ($users as $ui) {

            switch ($ui->estado_user_id) {
                case 1:
                    $ui->estado = "<span class=\"label label-sm label-success\"> A </span>";
                    break;
                case 2:
                    $ui->estado = "<span class=\"label label-sm label-danger\"> P </span>";
                    break;
                case 3:
                    $ui->estado = "<span class=\"label label-sm label-warning\"> WAct </span>";
                    break;
                case 4:
                    $ui->estado = "<span class=\"label label-sm label-warning\"> Wstam </span>";
                    break;


            }

            $data[] = $ui;

        }


        $out = ["status" => "success", "total" => count($users), "records" => $users];
        DB::disconnect();
        return $out;

    }

    public function testefiltro($user)
    {

        //dd($user);


    }


    public function getUsersByAjax($id_evento, $tipo)
    {

        // lista criadores ainda não inscritos no evento X


        $this->id_evento = $id_evento;
        //$this->id_clube = $id_clube;

        if ($tipo == 1) {// editar users

            $rota = 'editar';
            $this->mensagem = "Editar";
            $users = User::select(['id', 'stam', 'name', 'email'])->get();
        } else {  // inscrever users
            $rota = 'inscrever';

            $insc = DB::table('eventos_inscricoes as ei')
                ->where('ei.evento_id', '=', $id_evento)
                ->select('ei.user_id')
                ->get();

            $ids = [];
            foreach ($insc as $i) {

                $ids[] = $i->user_id;

            }

            $users = User::select(['id', 'stam', 'name', 'email'])
                ->whereNotIn('id', $ids)
                ->get();
            $this->mensagem = "Inscrever";

            //dd($users);

        }
        DB::disconnect();
        return Datatables::of($users)
            ->addColumn('action', function ($user) {
                return '<a href="' . route('ajaxinscrevercriador', [
                        $this->id_evento,
                        $user->id
                    ]) . '" class="inscrever_pop fancybox.ajax btn btn-xs btn-primary"><i class="glyphicon glyphicon-edit"></i> ' . $this->mensagem . '</a>';
            })
            ->editColumn('id', 'ID: {{$id}}')
            ->make(true);

    }

    public function isUserAdmin($id_user)
    {

        $roleu = DB::table('role_user as ru')
            ->where('ru.user_id', '=', $id_user)
            ->where('ru.role_id', '=', '1')
            ->first();
        DB::disconnect();

        return ($roleu) ? true : false;

    }

    public function getUserById($id_user)
    {


//        $user = DB::table('users as u')
//            ->where('u.id', '=', $id_user)
//            ->select('u.*')
//            ->first();

        $user = User::find($id_user);

        //dd($user);

// check para verificar se user é admin
//        if ($this->isUserAdmin($id_user)) {
//            $user->isadmin = true;
//
//        } else {
//            $user->isadmin = false;
//        }

        if ($user->isAdmin()) {
            $user->isadmin = true;

        } else {
            $user->isadmin = false;
        }
        if ($user->hasRole('juiz')) {
            $user->isjuiz = true;

        } else {
            $user->isjuiz = false;
        }
        if ($user->hasRole('gestor_nacional')) {
            $user->isgestornacional = true;

        } else {
            $user->isgestornacional = false;
        }

        //dd($user);

        return $user;
    }

    public function getUsersByClube($id_clube)
    {

        $users_clube = DB::table('rel_users_clubes as ruc')
            ->join('users as u', 'ruc.user_id', '=', 'u.id')
            ->join('paises', 'ruc.pais_id', '=', 'u.pais_id')
            ->select('u.*', 'paises.pais')
            ->where('ruc.clube_id', '=', $id_clube)->get();
        DB::disconnect();
        return $users_clube;
    }

    public function getUsersNotInEventoSelect($id_evento, $pesquisa)
    {

        $sql = '';
        if ($pesquisa == '') {

            // QUERY COM JOIN PARA CLUBES(?) QUE CAUSAVA DUPLICAÇÕES E FALHAS - VER A VALIDADE
            //$users = DB::select(DB::raw("SELECT ruc.user_id as id,concat(u.stam,' - ',u.name) as text FROM rel_users_clubes as ruc join users as u on ruc.user_id=u.id WHERE ruc.user_id NOT IN (SELECT ei.user_id FROM eventos_inscricoes as ei where ei.evento_id=:id_evento) order by u.name asc"), ['id_evento' => $id_evento]);


            $users = DB::select(DB::raw("SELECT u.id as id,concat(u.stam,' - ',u.name) as text FROM users as u WHERE u.id NOT IN (SELECT ei.user_id FROM eventos_inscricoes as ei where ei.evento_id=:id_evento) order by u.name asc"),
                ['id_evento' => $id_evento]);

        } else {

            // QUERY COM JOIN PARA CLUBES(?) QUE CAUSAVA DUPLICAÇÕES E FALHAS - VER A VALIDADE
            //$sql = "SELECT ruc.user_id as id,concat(u.stam,' - ',u.name) as text FROM rel_users_clubes as ruc join users as u on ruc.user_id=u.id WHERE (u.stam like '%" . $pesquisa . "%' or u.name like '%" . $pesquisa . "%') and ruc.user_id NOT IN (SELECT ei.user_id FROM eventos_inscricoes as ei where ei.evento_id=" . $id_evento . ") order by u.name asc";

            // 2018 07 24 -
            $sql = "SELECT u.id as id,concat(u.stam,' - ',u.name) as text FROM users as u WHERE (u.stam like '%" . $pesquisa . "%' or u.name like '%" . $pesquisa . "%') and u.id NOT IN (SELECT ei.user_id FROM eventos_inscricoes as ei where ei.evento_id=" . $id_evento . ") order by u.name asc";
            $users = DB::select(DB::raw($sql));


        }

        DB::disconnect();
        return $users;

    }

    public function getUsersNotInEventoSelectGestorN($id_evento, $pais_id, $pesquisa)
    {

        $sql = '';
        if ($pesquisa == '') {

            // QUERY COM JOIN PARA CLUBES(?) QUE CAUSAVA DUPLICAÇÕES E FALHAS - VER A VALIDADE
            //$users = DB::select(DB::raw("SELECT ruc.user_id as id,concat(u.stam,' - ',u.name) as text FROM rel_users_clubes as ruc join users as u on ruc.user_id=u.id WHERE ruc.user_id NOT IN (SELECT ei.user_id FROM eventos_inscricoes as ei where ei.evento_id=:id_evento) order by u.name asc"), ['id_evento' => $id_evento]);


            $users = DB::select(DB::raw("SELECT u.id as id,concat(u.stam,' - ',u.name) as text FROM users as u WHERE u.id NOT IN (SELECT ei.user_id FROM eventos_inscricoes as ei where ei.evento_id=:id_evento and u.pais_id=:pais_id) order by u.name asc"),
                ['id_evento' => $id_evento, 'id_evento' => $pais_id]);

        } else {

            // QUERY COM JOIN PARA CLUBES(?) QUE CAUSAVA DUPLICAÇÕES E FALHAS - VER A VALIDADE
            //$sql = "SELECT ruc.user_id as id,concat(u.stam,' - ',u.name) as text FROM rel_users_clubes as ruc join users as u on ruc.user_id=u.id WHERE (u.stam like '%" . $pesquisa . "%' or u.name like '%" . $pesquisa . "%') and ruc.user_id NOT IN (SELECT ei.user_id FROM eventos_inscricoes as ei where ei.evento_id=" . $id_evento . ") order by u.name asc";

            // 2018 07 24 -
            $sql = "SELECT u.id as id,concat(u.stam,' - ',u.name) as text FROM users as u WHERE (u.stam like '%" . $pesquisa . "%' or u.name like '%" . $pesquisa . "%') and u.pais_id = " . $pais_id . " and u.id NOT IN (SELECT ei.user_id FROM eventos_inscricoes as ei where ei.evento_id=" . $id_evento . ") order by u.name asc";
            $users = DB::select(DB::raw($sql));


        }

        DB::disconnect();
        return $users;

    }

    public function getUsersInscritos($id_evento, $pais_id = null)
    {
        $this->id_evento = $id_evento;
        if ($pais_id == null) {
            $users_inscritos = DB::table('eventos_inscricoes as ei')
                ->leftjoin('users as u', 'ei.user_id', '=', 'u.id')
                ->leftjoin('eventos_locais_recolha as elr', 'ei.local_recolha', '=', 'elr.id')
                ->leftjoin('eventos_inscricoes_passaros as eip', 'eip.inscricao_id', '=', 'ei.id')
                ->orderBy('u.name', 'asc')
                ->selectraw('ei.estado_inscricao,ei.tipo_caixa,ei.data_inscricao,ei.num_jantares,ei.num_mesas_venda,ei.num_stands,ei.evento_id, ei.id,ei.prioridade_devolucao, count(eip.inscricao_id) as nump, u.stam, u.name, u.email, IFNULL(elr.id, 0) as local_recolha_id, IFNULL(elr.local_nome, "EVT") as local_nome')
                ->where('ei.evento_id', $id_evento)
                ->groupBy('ei.id')
                ->get();


        } else {
            $users_inscritos = DB::table('eventos_inscricoes as ei')
                ->leftjoin('users as u', 'ei.user_id', '=', 'u.id')
                ->leftjoin('eventos_locais_recolha as elr', 'ei.local_recolha', '=', 'elr.id')
                ->leftjoin('eventos_inscricoes_passaros as eip', 'eip.inscricao_id', '=', 'ei.id')
                ->orderBy('u.name', 'asc')
                ->selectraw('ei.estado_inscricao,ei.tipo_caixa,ei.data_inscricao,ei.num_jantares,ei.num_mesas_venda,ei.num_stands,ei.evento_id, ei.id,ei.prioridade_devolucao, count(eip.inscricao_id) as nump, u.stam, u.name, u.email, IFNULL(elr.id, 0) as local_recolha_id, IFNULL(elr.local_nome, "EVT") as local_nome')
                ->where('ei.evento_id', $id_evento)
                ->where('u.pais_id', $pais_id)
                ->groupBy('ei.id')
                ->get();

        }


        //$sql = "select ei.id,concat(u.stam,' - ',u.name) as text  from eventos_inscricoes as ei join users as u on ei.user_id=u.id where ei.evento_id=".$id_evento." order by u.name asc";

        //$users_inscritos = DB::select(DB::raw($sql));

        //dd($users_inscritos);

        //$sum[] = (object) array('name' => '<span style="float: right;">Totais</span>','nump' => '403', 'tipo_caixa' => 3456, 'estado_inscricao'=> '302', 'num_jantares'=> '302', 'num_mesas_venda'=> '302', 'num_stands'=> '302' );
        $total_inscritos_pendentes = 0;
        $total_inscritos_activos = 0;
        foreach ($users_inscritos as $ui) {
            if ($ui->estado_inscricao == 1) {
                $total_inscritos_activos++;
            } else {
                $total_inscritos_pendentes++;
            }

        }


        $totais["total_passaros"] = $this->getTotalPassarosByEventoId($id_evento, $pais_id);
        $totais["total_inscritos"] = count($users_inscritos);
        $totais["total_inscritos_p"] = $total_inscritos_pendentes;
        $totais["total_inscritos_a"] = $total_inscritos_activos;

        //$out = ["status" => "success", "total" => count($users_inscritos), "records" => $users_inscritos, "summary" => $sum];
        $out = [
            "status" => "success",
            "total" => count($users_inscritos),
            "records" => $users_inscritos,
            "totais" => $totais
        ];

        //dd($out);
        DB::disconnect();
        return $out;
    }


    public function getTotalPassarosByEventoId($id_evento, $pais_id = null)
    {


        if ($pais_id == null) {
            $passaros = DB::select(DB::raw("SELECT
ei.evento_id,
u.pais_id,
ei.id,
count(ei.id) c
FROM
eventos_inscricoes AS ei
INNER JOIN users AS u ON ei.user_id = u.id
INNER JOIN eventos_inscricoes_passaros eip ON eip.inscricao_id = ei.id
where ei.evento_id=" . $id_evento . " group by ei.id"));
        } else {
            $passaros = DB::select(DB::raw("SELECT
ei.evento_id,
u.pais_id,
ei.id,
count(ei.id) c
FROM
eventos_inscricoes AS ei
INNER JOIN users AS u ON ei.user_id = u.id
INNER JOIN eventos_inscricoes_passaros eip ON eip.inscricao_id = ei.id
where ei.evento_id=" . $id_evento . " and u.pais_id=" . $pais_id . " group by ei.id"));

        }


        $ptotal = 0;
        foreach ($passaros as $p) {
            $ptotal += $p->c;


        }

//        $total = DB::table('eventos_inscricoes_passaros as eip')
//            ->selectraw('count(eip.id) as ptotal')
//            ->where('eip.evento_id', $id_evento)
//            ->get();

        DB::disconnect();
        return $ptotal;


    }


    public function getUsersInscritosGestorN($id_evento, $pais_id, $estado)
    {

        $search = [['ei.evento_id', '=', $id_evento], ['u.pais_id', '=', $pais_id]];
        if ($estado != null) {
            $search[] = ['ei.estado_inscricao', '=', $estado];
        }

        $users_inscritos_pais = DB::table('eventos_inscricoes as ei')
            ->leftjoin('users as u', 'ei.user_id', '=', 'u.id')
            ->leftjoin('eventos_locais_recolha as elr', 'ei.local_recolha', '=', 'elr.id')
            ->leftjoin('eventos_inscricoes_passaros as eip', 'eip.inscricao_id', '=', 'ei.id')
            ->orderBy('u.name', 'asc')
            ->selectraw('ei.estado_inscricao,ei.tipo_caixa,ei.data_inscricao,ei.num_jantares,ei.num_mesas_venda,ei.num_stands,ei.evento_id, ei.id,ei.prioridade_devolucao, count(eip.inscricao_id) as nump, u.stam, u.name, u.email, IFNULL(elr.id, 0) as local_recolha_id, IFNULL(elr.local_nome, "EVT") as local_nome')
//            ->where('ei.evento_id', $id_evento)
//            ->where('u.pais_id', $pais_id)
            ->where($search)
            ->groupBy('ei.id')
            ->get();

        // dd($users_inscritos_pais);
        //$out = ["status" => "success", "total" => count($users_inscritos_pais), "records" => $users_inscritos_pais];
        DB::disconnect();
        return $users_inscritos_pais;
    }


    public function getNumUsers()
    {

        $num = DB::table('users')->count();
        DB::disconnect();
        return $num;

    }

    public function getNumUsersPais($pais_id)
    {

        $num = DB::table('users')
            ->where('pais_id', $pais_id)
            ->count();
        DB::disconnect();
        return $num;

    }


    public function getNumUsersByClube($id_clube)
    {

        $num = DB::table('rel_users_clubes as ruc')
            ->where('clube_id', '=', $id_clube)->count();
        DB::disconnect();
        return $num;

    }

    public function removeUserAssociaClube($ruc_id, $user_id)
    {


        // o cascade trata de remover todos os roles associados a este criador<->clube
        $rem_uc = DB::table('rel_users_clubes')
            ->where('id', $ruc_id)
            ->delete();

//        $rem_puc = DB::table('rel_clubes_users_roles')
//            ->where('user_id', $user_id)
//            ->where('clube_id', $clube_id)
//            ->delete();

        //  dd("apa");
        DB::disconnect();
        return true;
    }


    public function userAssociaClube($user_id, $clube_id)
    {

        //dd($clube_id."---".$user_id);
        $blah = DB::table('rel_users_clubes')
            ->insert(['user_id' => $user_id, 'clube_id' => $clube_id]);

        DB::disconnect();
        return $blah;


    }

    public function editUserPermClube($user_id, $ruc_id, $role_id)
    {

        // verificar se está activo
        $q = DB::table('rel_users_clubes_roles')
            ->where('ruc_id', $ruc_id)
            ->where('role_id', $role_id)
            ->count();


        if ($q == 0) { // não existe -> activar


            $perm = DB::table('rel_users_clubes_roles')
                ->insert(['ruc_id' => $ruc_id, 'role_id' => $role_id]);


        } else { // já existe -> DESACTIVAR


            $perm = DB::table('rel_users_clubes_roles')
                ->where('ruc_id', $ruc_id)
                ->where('role_id', $role_id)
                ->delete();

        }

        DB::disconnect();
        return true;

    }


}

