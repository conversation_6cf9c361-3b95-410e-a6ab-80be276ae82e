<?php

/**
 * Created by PhpStorm.
 * User: pag
 * Date: 29/06/2016
 * Time: 00:32
 */

namespace App\Repositories;

use App\Evento;
use App\Models\User;
use DB;

//use Yajra\Datatables\Datatables;
use Auth;
use LaravelLocalization;

class EventoRepository
{

    protected $id_evento;
    protected $id_clube;
    protected $id_inscricao;

    protected $hoje;


    public function getTiposEvento()
    {

        $tipos_evento = DB::table('tipos_evento')
            ->orderBy('id', 'asc')
            ->select('id', 'tipo_evento')
            ->get();


        return $tipos_evento;
    }


    public function getGaiolaByNum($evento_id, $numgaiola)
    {


        $gaiola = DB::table('eventos_inscricoes_passaros as eip')
            ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
            ->join('eventos_inscricoes as ei', 'eip.inscricao_id', '=', 'ei.id')
            ->join('users as u', 'u.id', '=', 'ei.user_id')
            ->where('eip.numgaiola', '=', $numgaiola)
            ->where('eip.evento_id', '=', $evento_id)
            ->select('u.name', 'u.stam', 'eip.id', 'eip.evento_id', 'eip.numgaiola', 'eip.pontos', 'eip.classificacao',
                'eip.estado', 'resc.seccao', 'eip.rel_evento_seccao_classe_id', 'resc.classe', 'resc.nome')
            ->orderBy(DB::raw('cast(numgaiola as unsigned)'))
            ->get();
//dd($gaiola);
        return $gaiola;

    }

    /*
     *
     * JULGAMENTOS
     *
     */

    public function getPassaroById($id_passaro)
    {
        $passaro = DB::table('eventos_inscricoes_passaros as eip')
            ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
            ->select('eip.*', 'resc.seccao', 'resc.classe', 'resc.nome', 'resc.id as rescid')
            ->where('eip.id', '=', $id_passaro)
            ->get();
        DB::disconnect();
        // dd($passaro);

        return $passaro;
    }

    public function verificaGaiolaEquipaById($id_passaro)
    {
        $gaiola = DB::table('eventos_inscricoes_passaros')
            ->where('eip.id', '=', $id_passaro)
            ->get();

        $equipa = (!in_array(substr($gaiola[0]->numgaiola, -1), array('A', 'B', 'C', 'D'))) ? false : true;

        //dd($equipa);

        return $equipa;

    }


    public function verificaProblemasClassificacoesClasse($id_evento, $resc_id)
    {


        return "";
    }

    public function calculaClassificacoesEquipaClasse($id_evento, $resc_id)
    {

        $evento = $this->getEventoById($id_evento);

        $pontos_minimos = $evento->pontos_equipa_minimos_premio;


        $top = DB::select('SELECT
eip.id,
ei.evento_id,
eip.inscricao_id,
eip.rel_evento_seccao_classe_id AS resc_id,
eip.numgaiola,
eip.classificacao,
eip.pontosequipa
FROM
eventos_inscricoes AS ei
INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id and eip.pontosequipa >= ? AND eip.ausente <> 1 and nj<>1 and dq<>1 and fc<>1 and ne<>1
WHERE
ei.evento_id = ? AND
eip.rel_evento_seccao_classe_id = ? and
RIGHT(eip.numgaiola, 1) = \'A\'
order by eip.pontosequipa desc
limit 15', [$pontos_minimos, $id_evento, $resc_id]);
        DB::disconnect();

        // limpar classificações
        DB::update('update eventos_inscricoes as ei, eventos_inscricoes_passaros as eip
set eip.classificacao=\'\'
where eip.inscricao_id=ei.id AND RIGHT(eip.numgaiola, 1) IN (\'A\', \'B\', \'C\', \'D\') and
    ei.evento_id = ? and eip.rel_evento_seccao_classe_id=?', [$id_evento, $resc_id]);


        $i = 0;

        //dd($top);
        $checkpoints = 0;
        foreach ($top as $p) {


            if ($checkpoints != $p->pontosequipa) {
                $i++;
                $checkpoints = $p->pontosequipa;
            }
            if ($i < 4) {
                $pr = DB::table('eventos_inscricoes_passaros as eip')
                    ->where('eip.id', '=', $p->id)
                    ->update(['classificacao' => $i]);


            }


        }

        DB::disconnect();
    }

    public function getJuizesSistema()
    {

        $juizes = DB::table('role_user as ru')
            ->join('users as u', 'ru.user_id', '=', 'u.id')
            ->join('paises as p', 'p.id', '=', 'u.pais_id')
            ->select('u.name', 'p.pais_abrev', 'u.stam', 'u.id')
            ->where('ru.role_id', '=', 8)
            ->get();
        DB::disconnect();

        $out = ["status" => "success", "total" => count($juizes), "records" => $juizes];

        return $out;

    }

    public
    function getJuizesAtribuidos(
        $id_evento
    ) {

        $juizes = DB::table('juizes_eventos_seccoes_classes as jesc')
            ->join('users as u', 'jesc.user_id', '=', 'u.id')
            ->join('paises as p', 'u.pais_id', '=', 'p.id')
            ->leftjoin('rel_eventos_seccoes_classes as resc', 'jesc.resc_id', '=', 'resc.id')
            ->where('jesc.evento_id', '=', $id_evento)
            ->selectRaw('CONCAT(resc.seccao, " ", resc.classe, " - ",resc.nome) as seccaoclasse,jesc.id, p.pais_abrev, jesc.seccao, resc.classe,u.name as juiz,u.stam')
            ->orderBy('jesc.id', 'desc')
            ->get();

        DB::disconnect();
        $out = ["status" => "success", "total" => count($juizes), "records" => $juizes];

        return $out;

    }


    public
    function getJuizClasse(
        $id_evento,
        $rescid
    ) {

        $res = DB::table('rel_eventos_seccoes_classes')
            ->where('evento_id', $id_evento)
            ->where('id', $rescid)
            ->select('juiz_user_id')
            ->first();
        DB::disconnect();

        return $res->juiz_user_id;

    }


    public
    function calculaClassificacoesIndividuaisClasse(
        $id_evento,
        $resc_id
    ) {

        $evento = $this->getEventoById($id_evento);

        $pontos_minimos = $evento->pontos_indiv_minimos_premio;


        $top = DB::select('SELECT
eip.id,
ei.evento_id,
eip.inscricao_id,
eip.rel_evento_seccao_classe_id AS resc_id,
eip.numgaiola,
eip.classificacao,
eip.pontos
FROM
eventos_inscricoes AS ei
INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id and eip.pontos >= ? AND eip.ausente <> 1 and nj<>1 and dq<>1 and fc<>1 and ne<>1
WHERE
ei.evento_id = ? AND
eip.rel_evento_seccao_classe_id = ? and
RIGHT(eip.numgaiola, 1) NOT IN (\'A\', \'B\', \'C\', \'D\')
order by eip.pontos desc
limit 15', [$pontos_minimos, $id_evento, $resc_id]);
        DB::disconnect();
//limpar todas as classificações das gaiolas desta classe

        DB::update('update eventos_inscricoes as ei, eventos_inscricoes_passaros as eip
set eip.classificacao=\'\'
where eip.inscricao_id=ei.id AND RIGHT(eip.numgaiola, 1) NOT IN (\'A\', \'B\', \'C\', \'D\') and
    ei.evento_id = ? and eip.rel_evento_seccao_classe_id=?', [$id_evento, $resc_id]);


        $i = 0;

        // dd($top);

        $checkpoints = 0;
        foreach ($top as $p) {


            if ($checkpoints != $p->pontos) {
                $i++;
                $checkpoints = $p->pontos;
            }
            if ($i < 4) {
                $pr = DB::table('eventos_inscricoes_passaros as eip')
                    ->where('eip.id', '=', $p->id)
                    ->update(['classificacao' => $i]);


            }


        }
        DB::disconnect();

    }


    public
    function calculaClassificacoes2(
        $id_evento
    ) {

        $evento = $this->getEventoById($id_evento);

        $classes = DB::select('SELECT
eip.id,
eip.rel_evento_seccao_classe_id as rescid,
eip.numgaiola
FROM
eventos_inscricoes_passaros AS eip
where eip.pontos >=? and
eip.ausente=0 and
RIGHT(eip.numgaiola, 1) NOT IN (\'A\',\'B\',\'C\',\'D\')
group by rescid', [$evento->pontos_indiv_minimos_premio]);
        DB::disconnect();
        DB::table('eventos_inscricoes_passaros as eip')
            ->where('eip.evento_id')
            ->update(['classificacao' => null]);
        foreach ($classes as $c) {


            $premios = DB::table('eventos_inscricoes_passaros as eip')
                ->select('eip.numgaiola', 'eip.rel_evento_seccao_classe_id as rescid', 'eip.pontos', 'eip.id')
                ->where('eip.rel_evento_seccao_classe_id', '=', $c->rescid)
                ->where('pontos', '>=', $evento->pontos_indiv_minimos_premio)
                ->orderBy('pontos', 'desc')
                ->take(3)->get();

            DB::disconnect();

            $i = 1;
            //echo "class: " . $c->rescid . "<br>";

            foreach ($premios as $p) {
                echo "id " . $p->id . "pontos: " . $p->pontos . "<br>";
                $pr = DB::table('eventos_inscricoes_passaros as eip')
                    ->where('eip.id', '=', $p->id)
                    ->update(['classificacao' => $i]);

                $i++;

            }

        }
        DB::disconnect();
    }


    public
    function getGaiolaByIdPassaro(
        $id_passaro
    ) {
        $passaro = DB::table('eventos_inscricoes_passaros')
            ->where('id', $id_passaro)
            ->first();
        DB::disconnect();
        $num_gaiola = $passaro->numgaiola;


        return $num_gaiola;

    }


    public
    function getEquipaByIdEventoIdPrimeiraGaiola(
        $id_evento,
        $num_gaiola
    ) {


        $eg = array("A", "B", "C", "D");


        $num_gaiola_simples = substr($num_gaiola, 0, strlen($num_gaiola) - 1);

        $equipa = [];

        for ($i = 0; $i < 4; $i++) {

            $g = $num_gaiola_simples . $eg[$i];

            $equipa[] = $this->getPassaroByEventoGaiola($id_evento, $g);


        }


        //dd($equipa);
        return $equipa;

    }


    public
    function verificaExistenciaPontuacao(
        $id_passaro
    ) {

        $pont = DB::table('pontuacoes_eventos_passaros')
            ->where('passaro_id', $id_passaro)
            ->first();

        if ($pont) {

            return 1;

        } else {

            return 0;
        }


    }

    public
    function getPontuacoesEquipaByPrimeiraGaiola(
        $id_evento,
        $num_gaiola
    ) {

        // numero de gaiola sem a letra
        $num_gaiola_simples = substr($num_gaiola, 0, strlen($num_gaiola) - 1);

        $gaiolas = array(
            $num_gaiola_simples . "A",
            $num_gaiola_simples . "B",
            $num_gaiola_simples . "C",
            $num_gaiola_simples . "D"
        );

        $eg = array("A", "B", "C", "D");

        $pontuacoes[$num_gaiola_simples . $eg[0]] = '';


        for ($i = 0; $i < 4; $i++) {

            $g = $num_gaiola_simples . $eg[$i];

            $id_p = $this->getPassaroByEventoGaiola($id_evento, $g)->id;


            $pontuacoes[$g] = $this->getPontuacaoByIdPassaro($id_p);


        }


        return $pontuacoes;
    }


    public
    function getPontuacaoByIdPassaro(
        $id_passaro
    ) {

        $pontuacao = DB::table('pontuacoes_eventos_passaros as pep')
            ->where('pep.passaro_id', '=', $id_passaro)
            ->get();

        //dd($pontuacao);
        return $pontuacao;

    }


    public
    function getGaiolasByEventoJuiz(
        $id_evento,
        $rescid
    ) {

        $classe = $this->getSeccaoClasseById($rescid)->classe;


        if ($classe % 2 == 0) { //individual
            $gaiolas = DB::table('eventos_inscricoes_passaros as eip')
                ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
                ->where('eip.rel_evento_seccao_classe_id', $rescid)
                ->where('ausente', '0')
                ->select('eip.id', 'eip.outro_premio', 'eip.numgaiola', 'eip.pontos', 'eip.pontosequipa',
                    'eip.classificacao', 'eip.estado', 'resc.seccao', 'eip.rel_evento_seccao_classe_id', 'resc.classe',
                    'resc.nome')
                ->orderBy(DB::raw('numgaiola*1'))
                ->get();

            DB::disconnect();
            $classificacoes_duplicadas = DB::select('select  classificacao from `eventos_inscricoes_passaros` as `eip` 
inner join `rel_eventos_seccoes_classes` as `resc` on `eip`.`rel_evento_seccao_classe_id` = `resc`.`id` 
where `eip`.`rel_evento_seccao_classe_id` = ? and `ausente` = 0 and classificacao <> \'\'
group by classificacao asc 
having count(classificacao)>1', [$rescid]);
            DB::disconnect();

            $dups = array();
            foreach ($classificacoes_duplicadas as $cd) {
                $dups[] = $cd->classificacao;

            }

            foreach ($gaiolas as $gi) {
                if (in_array($gi->classificacao, $dups)) {
                    $gi->dup_color = 'background-color: #ffa6a6';
                } else {
                    $gi->dup_color = '';
                }


            }
            //dd($dups);
            //dd($gaiolas);


        } else { // equipa


            $gaiolas = DB::table('eventos_inscricoes_passaros as eip')
                ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
                ->where('eip.rel_evento_seccao_classe_id', $rescid)
                ->where('ausente', '0')
                ->select('eip.id', 'eip.outro_premio', 'eip.numgaiola', 'eip.pontos', 'eip.pontosequipa',
                    'eip.classificacao', 'eip.estado', 'resc.seccao', 'eip.rel_evento_seccao_classe_id', 'resc.classe',
                    'resc.nome')
                ->orderBy(DB::raw('numgaiola'))
                ->get();
            DB::disconnect();
            $classificacoes_duplicadase = DB::select('select classificacao from `eventos_inscricoes_passaros` as `eip`
inner join `rel_eventos_seccoes_classes` as `resc` on `eip`.`rel_evento_seccao_classe_id` = `resc`.`id`
where `eip`.`rel_evento_seccao_classe_id` = ? and `ausente` = 0 and classificacao <> \'\'
group by classificacao asc 
having count(classificacao)>1', [$rescid]);
            DB::disconnect();
            //dd($classificacoes_duplicadase);


            $dupse = array();
            foreach ($classificacoes_duplicadase as $cde) {
                $dupse[] = $cde->classificacao;

            }

            foreach ($gaiolas as $ge) {
                if (in_array($ge->classificacao, $dupse)) {
                    $ge->dup_color = 'background-color: #ffa6a6';
                } else {
                    $ge->dup_color = '';
                }


            }


        }


        // remove gaiolas de elementos de equipa e mantém a primeira - xxxxA
        $out = array();

        $gequipa = array('B', 'C', 'D');
        foreach ($gaiolas as $g) {

            if (!in_array(substr($g->numgaiola, -1), $gequipa)) {

                $out[] = $g;

            }


        }

        return $out;
    }


    // todo este metodo lista as classes atribuidas a um juiz, depois de escolher a seccao
    // E verifica quais dessas classes estao atribuidas (como classe) a algum outro juiz)
    // este cruzamento e que estoura com o servidor
    // criei o metodo getClassesByEventoJuiz2() abaixo em que apenas lista as classes da seccao atribuida ao juiz
    public
    function getClassesByEventoJuiz(
        $id_evento,
        $user_id,
        $seccao
    ) {


        $locale = LaravelLocalization::getCurrentLocale();
// ESTA QUERY ABAIXO ERA A QUE ESTAVA EM 2019 10 08

//        $classes = DB::select('(SELECT
//jesc.user_id,
//jesc.evento_id,
//resc.id as resc_id,
//eip.id passaro_id,
//resc.seccao,
//resc.classe,
//substr(resc.nome,1,50) as nome,
//resc.juiz_user_id
//FROM
//juizes_eventos_seccoes_classes AS jesc
//INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
//INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id and ausente=0
//INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id
//WHERE
//jesc.user_id = ? AND
//ei.evento_id = ? AND
//resc.seccao = ? AND
//(resc.juiz_user_id = 0 or resc.juiz_user_id = ?) AND
//jesc.seccao COLLATE utf8_general_ci = resc.seccao COLLATE utf8_general_ci
//group by resc.seccao asc, resc.classe*1 asc)
//UNION(SELECT
//jesc.user_id,
//jesc.evento_id,
//jesc.resc_id,
//eip.id passaro_id,
//resc.seccao,
//resc.classe,
//resc.nome,
//resc.juiz_user_id
//FROM
//juizes_eventos_seccoes_classes AS jesc
//INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
//INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id and ausente=0
//INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id
//WHERE
//jesc.user_id = ? AND
//ei.evento_id = ? AND
//resc.seccao = ? AND
//(resc.juiz_user_id = 0 or resc.juiz_user_id = ?) AND
//jesc.resc_id = eip.rel_evento_seccao_classe_id
//group by resc.seccao asc, resc.classe*1 asc)
//', [$user_id, $id_evento, $seccao, $user_id, $user_id, $id_evento, $seccao, $user_id]);

        $classes = DB::select('SELECT
jesc.user_id,
jesc.evento_id,
resc.id as resc_id,
eip.id passaro_id,
resc.seccao,
resc.classe,
substr(resc.nome,1,50) as nome,
resc.juiz_user_id
FROM
juizes_eventos_seccoes_classes AS jesc
INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id and ausente=0
INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id
WHERE
jesc.user_id = ? AND
ei.evento_id = ? AND
resc.seccao = ? AND
(resc.juiz_user_id = 0 or resc.juiz_user_id = ?) AND
(jesc.seccao COLLATE utf8_general_ci = resc.seccao COLLATE utf8_general_ci or jesc.resc_id = eip.rel_evento_seccao_classe_id)
 and resc.id 
not in (SELECT
jesc.resc_id
FROM
juizes_eventos_seccoes_classes AS jesc
INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id and ausente=0
INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id
WHERE
ei.evento_id = ? AND
resc.seccao = ? AND
jesc.user_id <> ? AND
jesc.resc_id = eip.rel_evento_seccao_classe_id
group by resc.seccao asc, resc.classe*1 asc)
group by resc.seccao asc, resc.classe*1 asc', [$user_id, $id_evento, $seccao, $user_id, $id_evento, $seccao, $user_id]);


        // dd($classes);


        $out = [];
        DB::disconnect();
        foreach ($classes as $c) {

            $ntotal = DB::select('SELECT
count(eip.rel_evento_seccao_classe_id) as ntotal,
eip.id
FROM
eventos_inscricoes_passaros AS eip
WHERE
eip.rel_evento_seccao_classe_id = ? and ausente=0
group by eip.rel_evento_seccao_classe_id', [$c->resc_id]);

            DB::disconnect();
            $njulgados = DB::select('SELECT
count(eip.rel_evento_seccao_classe_id) as njulg,
eip.id
FROM
eventos_inscricoes_passaros AS eip
WHERE
(pontos>0 or pontosequipa>0 or nj=1 or dq=1 or fc=1 or ne=1) and
eip.rel_evento_seccao_classe_id = ?
group by rel_evento_seccao_classe_id', [$c->resc_id]);
            DB::disconnect();

            if ($ntotal == null) {
                $c->ntotal = 0;

            } else {

                $c->ntotal = $ntotal[0]->ntotal;
            }

            if ($njulgados == null) {
                $c->njulgados = 0;

            } else {

                $c->njulgados = $njulgados[0]->njulg;
            }

            if ($c->ntotal == 0) {
                $percentage = 0;
            } else {
                $percentage = $c->njulgados * 100 / $c->ntotal;
            }


            // tipo de barra de progresso - para facilitar
            // pode ser danger(vermelho), warning(laranja) e success (azul)
            $progress_bar_type = 'danger';
            if ($percentage < 100) {
                $progress_bar_type = 'danger';
            } else {
                $progress_bar_type = 'success green-jungle';
            }

            $c->percentage = round($percentage);


            $classificacoes_duplicadas = DB::select('select classificacao from `eventos_inscricoes_passaros` as `eip`
inner join `rel_eventos_seccoes_classes` as `resc` on `eip`.`rel_evento_seccao_classe_id` = `resc`.`id`
where `eip`.`rel_evento_seccao_classe_id` = ? and `ausente` = 0 and classificacao <> \'\'
group by classificacao asc 
having count(classificacao)>1 limit 1', [$c->resc_id]);
            DB::disconnect();

            $c->problemas = ($classificacoes_duplicadas) ? 1 : 0;

            if ($classificacoes_duplicadas) {
                $progress_bar_type = 'warning';
            }
            $c->progress_bar_type = $progress_bar_type;
            //dd($classificacoes_duplicadas);
//dd($njulgados);
            // echo $c->resc_id."| ".$c->seccao." - ".$c->classe.": total:".$c->ntotal."<br>";

            if ($c->ntotal > 0) {
                $out[] = $c;
            }

        }

        //dd($out);

        return $out;

    }

    // ESTE METODO APENAS LISTA CLASSES DE UMA SECCAO ATRIUBUIDA A UM DETERMINADO JUIZ
    // RESOLVEU PROBLEMA COM AVIXIRA 2021 - que estourava com o servidor mysql
    public
    function getClassesByEventoJuiz2(
        $id_evento,
        $user_id,
        $seccao
    ) {

        // get classes da seccao atribuidas ao juiz

        $classes = DB::select('SELECT
jesc.user_id,
jesc.evento_id,
resc.id as resc_id,
resc.seccao,
resc.classe,
substr(resc.nome,1,50) as nome,
resc.juiz_user_id
FROM
juizes_eventos_seccoes_classes AS jesc
INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id and ausente=0
INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id and (resc.juiz_user_id = 0 or resc.juiz_user_id = ?)
WHERE
jesc.user_id = ? AND
ei.evento_id = ? AND
resc.seccao = ? AND
(jesc.seccao COLLATE utf8_general_ci = resc.seccao COLLATE utf8_general_ci or jesc.resc_id = eip.rel_evento_seccao_classe_id)
group by resc.seccao asc, resc.classe*1 asc', [$user_id, $user_id, $id_evento, $seccao]);
//
//
//        $notin = DB::select('SELECT
//jesc.resc_id
//FROM
//juizes_eventos_seccoes_classes AS jesc
//INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
//INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id and ausente=0 and jesc.resc_id = eip.rel_evento_seccao_classe_id
//INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id
//WHERE
//ei.evento_id = ? AND
//resc.seccao = ? AND
//jesc.user_id <> ?
//group by resc.seccao asc, resc.classe*1 asc', [$id_evento, $seccao, $user_id]);
//
//
//        foreach ($classes as $classe){
//
//            $rescid = $classe->resc_id;
//            $found = false;
//            foreach($notin as $not){
//if($rescid == $not->id){
//
//}
//
//
//            }
//
//        }


        // dd($classes);


        $out = [];
        DB::disconnect();
        foreach ($classes as $c) {

            $ntotal = DB::select('SELECT
count(eip.rel_evento_seccao_classe_id) as ntotal,
eip.id
FROM
eventos_inscricoes_passaros AS eip
WHERE
eip.rel_evento_seccao_classe_id = ? and ausente=0
group by eip.rel_evento_seccao_classe_id', [$c->resc_id]);

            DB::disconnect();
            $njulgados = DB::select('SELECT
count(eip.rel_evento_seccao_classe_id) as njulg,
eip.id
FROM
eventos_inscricoes_passaros AS eip
WHERE
(pontos>0 or pontosequipa>0 or nj=1 or dq=1 or fc=1 or ne=1) and
eip.rel_evento_seccao_classe_id = ?
group by rel_evento_seccao_classe_id', [$c->resc_id]);
            DB::disconnect();

            if ($ntotal == null) {
                $c->ntotal = 0;

            } else {

                $c->ntotal = $ntotal[0]->ntotal;
            }

            if ($njulgados == null) {
                $c->njulgados = 0;

            } else {

                $c->njulgados = $njulgados[0]->njulg;
            }

            if ($c->ntotal == 0) {
                $percentage = 0;
            } else {
                $percentage = $c->njulgados * 100 / $c->ntotal;
            }


            // tipo de barra de progresso - para facilitar
            // pode ser danger(vermelho), warning(laranja) e success (azul)
            $progress_bar_type = 'danger';
            if ($percentage < 100) {
                $progress_bar_type = 'danger';
            } else {
                $progress_bar_type = 'success green-jungle';
            }

            $c->percentage = round($percentage);


            $classificacoes_duplicadas = DB::select('select classificacao from `eventos_inscricoes_passaros` as `eip`
inner join `rel_eventos_seccoes_classes` as `resc` on `eip`.`rel_evento_seccao_classe_id` = `resc`.`id`
where `eip`.`rel_evento_seccao_classe_id` = ? and `ausente` = 0 and classificacao <> \'\'
group by classificacao asc 
having count(classificacao)>1 limit 1', [$c->resc_id]);
            DB::disconnect();

            $c->problemas = ($classificacoes_duplicadas) ? 1 : 0;

            if ($classificacoes_duplicadas) {
                $progress_bar_type = 'warning';
            }
            $c->progress_bar_type = $progress_bar_type;
            //dd($classificacoes_duplicadas);
//dd($njulgados);
            // echo $c->resc_id."| ".$c->seccao." - ".$c->classe.": total:".$c->ntotal."<br>";

            if ($c->ntotal > 0) {
                $out[] = $c;
            }

        }

        //dd($out);

        return $out;


    }


    public function getSeccoesByEventoJuiz($id_evento, $user_id)
    {

        $seccoes = DB::select('SELECT
jesc.user_id,
jesc.evento_id,
resc.seccao
FROM
juizes_eventos_seccoes_classes AS jesc
INNER JOIN rel_eventos_seccoes_classes AS resc ON jesc.resc_id = resc.id
WHERE
jesc.user_id = ? AND
jesc.evento_id = ? AND
jesc.resc_id = resc.id
group by resc.seccao', [$user_id, $id_evento]);

        $seccoes2 = DB::select('SELECT
jesc.user_id,
jesc.evento_id,
jesc.seccao
FROM
juizes_eventos_seccoes_classes AS jesc
WHERE
jesc.user_id = ? AND
jesc.evento_id = ? and
jesc.seccao <> \'\'
group by jesc.seccao', [$user_id, $id_evento]);
        DB::disconnect();


        if (count($seccoes2) > 0) {
            for ($i = 0; $i < count($seccoes2); $i++) {
                $found = false;
                for ($j = 0; $j < count($seccoes); $j++) {
                    if ($seccoes[$j]->seccao == $seccoes2[$i]->seccao) {
                        $found = true;
                    }

                }
                if ($found == false) {
                    $seccoes[] = $seccoes2[$i];
                }


            }

        }
        //  dd($seccoes);

        //


//
//        $seccoes = DB::select('(SELECT
//jesc.user_id,
//jesc.evento_id,
//resc.seccao
//FROM
//juizes_eventos_seccoes_classes AS jesc
//INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
//INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id
//INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id
//WHERE
//jesc.user_id = ? AND
//ei.evento_id = ? AND
//jesc.seccao COLLATE utf8_general_ci = resc.seccao COLLATE utf8_general_ci
//group by resc.seccao)
//UNION(SELECT
//jesc.user_id,
//jesc.evento_id,
//resc.seccao
//FROM
//juizes_eventos_seccoes_classes AS jesc
//INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
//INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id
//INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id
//WHERE
//jesc.user_id = ? AND
//ei.evento_id = ? AND
//jesc.resc_id = eip.rel_evento_seccao_classe_id
//group by resc.seccao)', [$user_id, $id_evento, $user_id, $id_evento]);

        DB::disconnect();
        return $seccoes;
    }


    public
    function getEventosJulgamento(
        $id_user
    ) {

        $hoje = date('Y-m-d H:i:s');

        $ej = DB::select('SELECT
jesc.evento_id,
jesc.id,
e.nome,
jesc.user_id
FROM
juizes_eventos_seccoes_classes AS jesc, eventos AS e 
 where jesc.evento_id = e.id and e.data_inicio_julg <= "' . $hoje . '" and e.data_fim_julg >= "' . $hoje . '" and jesc.user_id = ?
GROUP BY
jesc.evento_id', [$id_user]);

        DB::disconnect();
        return $ej;

    }

    /*
     *
     * FIM - JULGAMENTOS
     *
     */


    public
    function getCriadoresInscritosByAjax(
        $id_evento
    ) {

        $this->id_evento = $id_evento;
        //$this->id_clube = $id_clube;

        // $this->id_inscricao = $users

        //$users = User::select(['id', 'stam', 'name', 'email'])->get();

        $users_inscritos = DB::table('eventos_inscricoes as ei')
            ->join('users as u', 'ei.user_id', '=', 'u.id')
            ->orderBy('u.name', 'asc')
            ->select('ei.evento_id', 'ei.id', 'u.stam', 'u.name', 'u.email')
            ->where('ei.evento_id', $id_evento);
        DB::disconnect();

        return $users_inscritos;
    }

    public function getSeccaoClasseByPassaroId($id_passaro)
    {


    }


    public
    function getSeccaoClasseByRelSC(
        $seccaoclasse_id
    ) {

        $seccaoclasse = DB::table('rel_eventos_seccoes_classes')
            ->where('id', '=', $seccaoclasse_id)
            ->select('seccao', 'classe')
            ->get();

        DB::disconnect();

        return $seccaoclasse;


    }

    public
    function verificaCriadorSocio(
        $id_criador,
        $id_clube
    ) {

        $socio = DB::table('rel_users_clubes')
            ->where('user_id', '=', $id_criador)
            ->where('clube_id', '=', $id_clube)
            ->get();

        DB::disconnect();

        return (count($socio) == 0) ? false : true;

    }


    public
    function existeGaiolaNoEvento(
        $numgaiola,
        $evento_id
    ) {

//        $d = DB::table('eventos_inscricoes_passaros')
//            ->where('numgaiola', '=', $numgaiola)
//            ->orwhere('numgaiola', '=', $numgaiola . 'A')
//            ->orwhere('numgaiola', '=', $numgaiola . 'B')
//            ->orwhere('numgaiola', '=', $numgaiola . 'C')
//            ->orwhere('numgaiola', '=', $numgaiola . 'D')
//            ->count();

        $sql = "select id from `eventos_inscricoes_passaros` where evento_id = " . $evento_id . " and ( numgaiola = '" . $numgaiola . "' or numgaiola = '" . $numgaiola . "A' or numgaiola = '" . $numgaiola . "B' or numgaiola = '" . $numgaiola . "C' or numgaiola = '" . $numgaiola . "D') limit 1";

        $d = DB::select($sql);
        $v = (count($d) == 0) ? false : true;

        DB::disconnect();
        return $v;

    }

    public
    function getListaEventosAdmin()
    {

        $listaeventosadmin = DB::select('SELECT
e.data_fim,
e.data_inicio,
e.nome,
e.data_inicio_inscricoes,
e.data_fim_inscricoes,
e.data_inicio_julg,
e.data_fim_julg,
e.iban,
e.preco_inscricao_ind_socio,
e.preco_inscricao_ind,
e.preco_inscricao_equipa,
e.preco_inscricao_equipa_socio,
e.id,
e.tipo_evento_id,
e.estado,
e.img_logotipo,
e.img_patrocinador,
e.preco_inscricao_ave_feira,
e.preco_inscricao_catalogo,
e.preco_jantar,
e.preco_mesa_venda,
e.preco_stand,
e.desconto_gaiola_propria,
e.percentagem_venda_aves,
e.percentagem_venda_aves_feira,
e.pontos_indiv_minimos_premio,
e.pontos_equipa_minimos_premio,
te.tipo_evento
FROM
eventos AS e
INNER JOIN tipos_evento AS te ON e.tipo_evento_id = te.id
ORDER BY
e.data_inicio desc,
e.data_fim desc');

        //dd($eventos_historico);

        DB::disconnect();
        return $listaeventosadmin;
    }

    public
    function getEventosHistoricoCriador(
        $user_id
    ) {
        $hoje = date('Y-m-d');


        $eventos_historico = DB::select('SELECT
e.data_fim,
e.data_inicio,
e.nome,
e.data_inicio_inscricoes,
e.data_fim_inscricoes,
e.data_inicio_julg,
e.data_fim_julg,
e.preco_inscricao_ind_socio,
e.preco_inscricao_ind,
e.preco_inscricao_equipa,
e.preco_inscricao_equipa_socio,
e.id,
e.tipo_evento_id,
e.estado,
e.img_logotipo,
e.img_patrocinador,
e.preco_inscricao_ave_feira,
e.preco_inscricao_catalogo,
e.preco_jantar,
e.preco_mesa_venda,
e.preco_stand,
e.desconto_gaiola_propria,
e.percentagem_venda_aves,
e.percentagem_venda_aves_feira,
e.pontos_indiv_minimos_premio,
e.pontos_equipa_minimos_premio,
ei.num_expositor,
ei.num_jantares,
ei.num_mesas_venda,
ei.num_stands,
ei.data_inscricao
FROM
eventos_inscricoes AS ei
INNER JOIN eventos AS e ON ei.evento_id = e.id
WHERE
ei.user_id = ? AND
e.data_fim < ?
ORDER BY e.data_inicio desc, e.data_fim desc 
', [$user_id, (string)$hoje]);

        //dd($eventos_historico);

        DB::disconnect();
        return $eventos_historico;
    }

    public
    function getEventosHistoricoAdmin()
    {
        $this->hoje = date('Y-m-d');

        $eventos_historico = DB::table('eventos as e')
            ->where('estado', '=', 1)
            ->where('data_fim', '<', $this->hoje)
            ->get();

        $ev_out = [];
        foreach ($eventos_historico as $e) {

            $e->num_passaros = $this->getNumPassarosByEvento($e->id);

            $e->num_inscricoes = $this->getNumInscricoesByEvento($e->id);

            $e->num_fichas_julgamento = $this->getNumFichasJulgamentoByEvento($e->id);
            $e->num_seccoes_classes = $this->getNumClassesByEvento($e->id);
            $e->num_juizes = $this->getNumJuizesByEvento($e->id);
            $ev_out[] = $e;

        }

        DB::disconnect();
        //dd($ev_out);
        return $ev_out;

    }


    public
    function getEventosActivosCriador(
        $user_id
    ) {
        $this->hoje = date('Y-m-d');


        $eventos = DB::table('rel_eventos_clubes as rec')
            ->join('eventos as e', 'rec.evento_id', '=', 'e.id')
            ->select('rec.evento_id', 'e.id', 'e.data_inicio', 'e.data_fim', 'e.nome', 'e.data_inicio_inscricoes',
                'e.data_fim_inscricoes')
            ->where('estado', '=', 1)
            ->where(function ($query) {
                $query->where('data_inicio_inscricoes', '<=', $this->hoje)
                    ->where('data_fim', '>=', $this->hoje);
            })
            ->orderBy('data_inicio_inscricoes', 'desc')
            ->orderBy('data_fim_inscricoes', 'desc')
            ->groupBy('evento_id')
            ->get();


        $ev_out = [];
        foreach ($eventos as $e) {

            $e->editavelcriador = ($e->data_fim_inscricoes >= $this->hoje) ? true : false;


            $e->inscricao = $this->getInscricaoCriadorByEvento($user_id, $e->evento_id);

            $ev_out[] = $e;


        }


        DB::disconnect();
        //dd($ev_out);
        return $ev_out;
    }

    public
    function getEventosActivosByClube(
        $clube_id
    ) {

        $this->hoje = date('Y-m-d');

        $eventos = DB::table('rel_eventos_clubes as rec')
            ->join('eventos as e', 'rec.evento_id', '=', 'e.id')
            ->where('estado', '=', 1)
            ->where(function ($query) {
                $query->where('data_inicio_inscricoes', '<=', $this->hoje)
                    ->where('data_fim', '>=', $this->hoje);
            })
            ->where('clube_id', $clube_id)
            ->groupBy('evento_id')
            ->get();


        //dd($eventos);

        DB::disconnect();
        return $eventos;


    }

    public
    function getHistoricoEventosByClube(
        $clube_id
    ) {

        $this->hoje = date('Y-m-d');

        $eventos_historico = DB::table('rel_eventos_clubes as rec')
            ->join('eventos as e', 'rec.evento_id', '=', 'e.id')
            ->where('estado', '=', 1)
            ->where('data_fim', '<', $this->hoje)
            ->where('clube_id', $clube_id)
            ->groupBy('evento_id')
            ->get();

        //dd($eventos_historico);

        DB::disconnect();
        return $eventos_historico;

    }

    public
    function getInscricaoIdByEventoId(
        $id_evento
    ) {

        $user_id = Auth::id();
        $inscricao = DB::table('eventos_inscricoes')
            ->where('user_id', '=', $user_id)
            ->where('evento_id', '=', $id_evento)
            ->first();

        DB::disconnect();
        return $inscricao->id;


    }


    public
    function getInscricaoCriadorByEvento(
        $user_id,
        $evento_id
    ) {


        $inscricao = DB::table('eventos_inscricoes as ei')
            ->where('ei.user_id', '=', $user_id)
            ->where('ei.evento_id', '=', $evento_id)
            ->first();

        if ($inscricao) {

            if ($inscricao->local_recolha == 0) {
                $inscricao->local_recolha_nome = trans('general.local_evento');
                $inscricao->local_recolha_morada = '';
            } else {
                $local_recolha = DB::table('eventos_locais_recolha as elr')
                    ->where('elr.id', '=', $inscricao->local_recolha)
                    ->where('elr.evento_id', '=', $evento_id)
                    ->first();
                $inscricao->local_recolha_nome = $local_recolha->local_nome;
                $inscricao->local_recolha_morada = $local_recolha->local_morada;
            }
        }

        DB::disconnect();
        //dd($inscricao);
        return $inscricao;

    }

    public
    function getProximoNumExpositor(
        $evento_id
    ) {

        $numexp = DB::table('eventos_inscricoes')
            ->where('evento_id', $evento_id)
            ->orderBy('num_expositor', 'desc')
            ->select('num_expositor')
            ->first();

        if ($numexp == null) {

            $numexp = 1;

        } else {

            $numexp = $numexp->num_expositor + 1;

        }

        //dd($numexp);

        DB::disconnect();
        return $numexp;
    }

    public
    function getEventosNaoPublicados(
        $estado
    ) {

        // se estado=2 - evento não publicado


        $this->hoje = date('Y-m-d');

        // para funcionar, necessita que o evento tenha clubes atribuidos

        // ESTA QUERY MOSTRA TODOS NO SISTEMA, MESMO COM DATAS DISTANTES
        $eventos = DB::table('rel_eventos_clubes as rec')
            ->join('eventos as e', 'rec.evento_id', '=', 'e.id')
            ->where('estado', $estado)
            ->where('data_fim', '>=', $this->hoje)
            ->groupBy('evento_id')
            ->get();


        // ESTA QUERY MOSTRA POR DATAS
//        $eventos = DB::table('rel_eventos_clubes as rec')
//            ->join('eventos as e', 'rec.evento_id', '=', 'e.id')
//            ->where('estado', $estado)
//            ->Where(function ($query) {
//                $query->where('data_inicio_inscricoes', '<=', $this->hoje)
//                    ->where('data_fim', '>=', $this->hoje);
//            })
//            ->groupBy('evento_id')
//            ->get();

        //dd($eventos);

        $ev_out = [];
        foreach ($eventos as $e) {

            $e->num_passaros = $this->getNumPassarosByEvento($e->id);

            $e->num_inscricoes = $this->getNumInscricoesByEvento($e->id);

            $e->num_fichas_julgamento = $this->getNumFichasJulgamentoByEvento($e->id);
            $e->num_seccoes_classes = $this->getNumClassesByEvento($e->id);
            $e->num_juizes = $this->getNumJuizesByEvento($e->id);
            $ev_out[] = $e;

        }

        DB::disconnect();
        //dd($ev_out);
        return $ev_out;

    }


    public
    function getEventosActivosAdmin()
    {
        $this->hoje = date('Y-m-d');

        // para funcionar, necessita que o evento tenha clubes atribuidos
        /*
         *
         * ESTE QUERY MOSTRA EVENTOS QUE ESTÃO EM INSCRIÇÕES
         *
         */
//        $eventos = DB::table('rel_eventos_clubes as rec')
//            ->join('eventos as e', 'rec.evento_id', '=', 'e.id')
//            ->where('estado', '=', 1)
//            ->Where(function ($query) {
//                $query->where('data_inicio_inscricoes', '<=', $this->hoje)
//                    ->where('data_fim', '>=', $this->hoje);
//            })
//            ->groupBy('evento_id')
//            ->get();


        $eventos = DB::table('rel_eventos_clubes as rec')
            ->join('eventos as e', 'rec.evento_id', '=', 'e.id')
            ->where('estado', '=', 1)
            ->where('data_fim', '>=', $this->hoje)
            ->orderBy('e.data_inicio_inscricoes')
            ->groupBy('evento_id')
            ->get();


        //dd($eventos);

        $ev_out = [];
        foreach ($eventos as $e) {

            $e->num_passaros = $this->getNumPassarosByEvento($e->id);

            $e->num_inscricoes = $this->getNumInscricoesByEvento($e->id);

            $e->num_fichas_julgamento = $this->getNumFichasJulgamentoByEvento($e->id);
            $e->num_seccoes_classes = $this->getNumClassesByEvento($e->id);
            $e->num_juizes = $this->getNumJuizesByEvento($e->id);
            $ev_out[] = $e;

        }

        DB::disconnect();
        //dd($ev_out);
        return $ev_out;
    }

    public
    function getEventosActivosGestorNacional()
    {
        $this->hoje = date('Y-m-d');


        $eventos = DB::table('rel_eventos_clubes as rec')
            ->join('eventos as e', 'rec.evento_id', '=', 'e.id')
            ->where('estado', '=', 1)
            ->where('data_fim', '>=', $this->hoje)
            ->where('tipo_evento_id', '=', 6)
            ->orderBy('e.data_inicio_inscricoes')
            ->groupBy('evento_id')
            ->get();


        //dd($eventos);

        $ev_out = [];
        foreach ($eventos as $e) {

            $e->num_passaros = $this->getNumPassarosByEvento($e->id);

            $e->num_inscricoes = $this->getNumInscricoesByEvento($e->id);

            $e->num_fichas_julgamento = $this->getNumFichasJulgamentoByEvento($e->id);
            $e->num_seccoes_classes = $this->getNumClassesByEvento($e->id);
            $e->num_juizes = $this->getNumJuizesByEvento($e->id);
            $ev_out[] = $e;

        }

        DB::disconnect();
        //dd($ev_out);
        return $ev_out;
    }

    public
    function getNumInscricoesByEvento(
        $id_evento
    ) {
        $num = DB::table('eventos_inscricoes')
            ->where('evento_id', '=', $id_evento)
            ->count();


        DB::disconnect();
        return $num;

    }

    public
    function getEventsByAjaxAdmin()
    {


        //$users = User::select(['id', 'stam', 'estado_id', 'name', 'email'])->get();


        $events = DB::table('eventos as e')
            ->select([
                'e.id',
                'e.nome',
                'te.tipo_evento',
                'e.data_inicio',
                'e.data_fim',
                'e.data_inicio_inscricoes',
                'e.data_fim_inscricoes',
                'e.estado'
            ])
            ->join('tipos_evento as te', 'e.tipo_evento_id', '=', 'te.id')
            ->orderBy('e.data_inicio', 'desc')
            ->orderBy('e.data_fim', 'desc')
            ->get();

//        $events = DB::select('SELECT
//e.data_fim,
//e.data_inicio,
//e.nome,
//e.data_inicio_inscricoes,
//e.data_fim_inscricoes,
//e.data_inicio_julg,
//e.data_fim_julg,
//e.preco_inscricao_ind_socio,
//e.preco_inscricao_ind,
//e.preco_inscricao_equipa,
//e.preco_inscricao_equipa_socio,
//e.id,
//e.tipo_evento_id,
//e.estado,
//e.img_logotipo,
//e.img_patrocinador,
//e.preco_inscricao_ave_feira,
//e.preco_inscricao_catalogo,
//e.preco_jantar,
//e.preco_mesa_venda,
//e.preco_stand,
//e.desconto_gaiola_propria,
//e.percentagem_venda_aves,
//e.percentagem_venda_aves_feira,
//e.pontos_indiv_minimos_premio,
//e.pontos_equipa_minimos_premio,
//te.tipo_evento
//FROM
//eventos AS e
//INNER JOIN tipos_evento AS te ON e.tipo_evento_id = te.id
//ORDER BY
//e.data_inicio desc,
//e.data_fim desc');


//$events=$events->toJson();

        //$out = '{ "data": [ '.$events."]}";

        //$records["data"] = $events;


        DB::disconnect();
        return $events;

    }


    public
    function getSeccoesByEvento(
        $id_evento
    ) {


        $seccoes = DB::table('rel_eventos_seccoes_classes as resc')
            ->groupBy('seccao')
            ->where('resc.evento_id', '=', $id_evento)
            ->orderBy('resc.seccao', 'asc')
            ->select('resc.evento_id', 'resc.seccao')
            ->get();

        //dd($seccoes);

        DB::disconnect();
        return $seccoes;

    }

    public
    function getSeccoesClassesByEventoByAjaxAdmin(
        $id_evento
    ) {

        //$users = User::select(['id', 'stam', 'estado_id', 'name', 'email'])->get();

        $sce = DB::table('rel_eventos_seccoes_classes as resc')
            ->select(['resc.*'])
            ->where('resc.evento_id', '=', $id_evento)
            ->orderBy('resc.seccao', 'asc')
            ->orderBy(DB::raw('resc.classe*1'))
            ->get();

        //dd($sce);

        //dd($users);

        $out = ["status" => "success", "total" => count($sce), "records" => $sce];

        DB::disconnect();
        return $out;

    }

    public function getClassesByEventoByTipo($id_evento, $tipo)
    {
        if ($tipo == 'individual') {
            $classes = DB::table('rel_eventos_seccoes_classes as resc')
                ->where('resc.evento_id', '=', $id_evento)
                ->orderByRaw('resc.seccao asc, resc.classe*1 asc')
                ->selectRaw('resc.id, resc.evento_id, resc.seccao, resc.classe, LEFT(resc.nome, 80) nome')
                ->whereRaw('mod(resc.classe, 2) = 0')
                ->get();
        } else {
            $classes = DB::table('rel_eventos_seccoes_classes as resc')
                ->where('resc.evento_id', '=', $id_evento)
                ->orderByRaw('resc.seccao asc, resc.classe*1 asc')
                ->selectRaw('resc.id, resc.evento_id, resc.seccao, resc.classe, LEFT(resc.nome, 80) nome')
                ->whereRaw('mod(resc.classe, 2) <> 0')
                ->get();

        }


//        $classes = DB::table('rel_eventos_seccoes_classes as resc')
//            ->where('resc.evento_id', '=', $id_evento)
//            ->orderBy('resc.seccao', 'asc')
//            ->orderBy('resc.classe', 'asc')
//            ->select('resc.id', 'resc.evento_id', 'resc.seccao', 'resc.classe', 'resc.nome')
//            ->get();

        //dd($classes);

        DB::disconnect();
        return $classes;
    }

    public function getClassesByEvento($id_evento)
    {
        $classes = DB::table('rel_eventos_seccoes_classes as resc')
            ->where('resc.evento_id', '=', $id_evento)
            ->orderByRaw('resc.seccao asc, resc.classe*1 asc')
            ->selectRaw('resc.id, resc.evento_id, resc.seccao, resc.classe, LEFT(resc.nome, 80) nome')
            ->get();

//        $classes = DB::table('rel_eventos_seccoes_classes as resc')
//            ->where('resc.evento_id', '=', $id_evento)
//            ->orderBy('resc.seccao', 'asc')
//            ->orderBy('resc.classe', 'asc')
//            ->select('resc.id', 'resc.evento_id', 'resc.seccao', 'resc.classe', 'resc.nome')
//            ->get();

        //dd($classes);

        DB::disconnect();
        return $classes;

    }

    public
    function getNumPassarosGaiolaPropriaByInscricao(
        $id_inscricao
    ) {

        $num_gaiolas_proprias = DB::table('eventos_inscricoes_passaros')
            ->where('inscricao_id', $id_inscricao)
            ->where('gaiola_propria', 1)
            ->count();

        // TODO: verificar se funciona
        //dd($num_gaiolas_proprias);


        DB::disconnect();
        return $num_gaiolas_proprias;


    }

    public
    function getNumPassarosByEvento(
        $id_evento
    ) {

        return DB::table('eventos_inscricoes_passaros as eip')
            ->join('eventos_inscricoes as ei', 'eip.inscricao_id', '=', 'ei.id')
            ->where('ei.evento_id', '=', $id_evento)
            ->count();

        DB::disconnect();
    }

    public
    function getSeccaoClasseById(
        $id_resc
    ) {
        $seccaoclasse = DB::table('rel_eventos_seccoes_classes as resc')
            ->select('resc.nome', 'resc.seccao', 'resc.classe')
            ->where('resc.id', '=', $id_resc)
            ->first();
        DB::disconnect();
        return $seccaoclasse;
    }

    public
    function getFichaRubricasByIdPassaro(
        $id_passaro
    ) {

        $locale = LaravelLocalization::getCurrentLocale();
        $locales = ['pt', 'fr', 'en', 'es', 'nl', 'it', 'tr'];
        if (!in_array($locale, $locales)) {
            $locale = 'pt';

        }

        $ficha = DB::select('
select 
t.id,
t.numgaiola,
t.resc_id,
t.seccao,
rfesc.*
FROM
(SELECT
eip.id,
eip.numgaiola,
eip.rel_evento_seccao_classe_id as resc_id,
resc.seccao,
resc.evento_id
FROM
eventos_inscricoes_passaros AS eip
join rel_eventos_seccoes_classes as resc on resc.id=eip.rel_evento_seccao_classe_id
where eip.id = ?) as t
join rel_fichas_eventos_seccoes_classes as rfesc on (t.seccao = rfesc.seccao or t.resc_id = rfesc.resc_id) and rfesc.evento_id=t.evento_id',
            [$id_passaro]);
        DB::disconnect();


        //dd($ficha);
//        $ficharubricas = DB::table('rel_fichas_rubricas as rfr')
//            ->join('rubricas as r', 'rfr.rubrica_id', '=', 'r.id')
//            ->select('rfr.*', ($locale == 'pt') ? 'r.nome' : 'r.' . $locale . ' as nome')
//            ->where('rfr.ficha_id', '=', $ficha[0]->ficha_julgamento_id)
//            ->get();

        // OVERRIDE PARA RUBRICAS SEMPRE EM FRANCES
        $ficharubricas = DB::table('rel_fichas_rubricas as rfr')
            ->join('rubricas as r', 'rfr.rubrica_id', '=', 'r.id')
            ->select('rfr.*', 'r.' . $locale . ' as nome')
            ->where('rfr.ficha_id', '=', $ficha[0]->ficha_julgamento_id)
            ->get();

        DB::disconnect();

        return $ficharubricas;

    }

    public
    function verificaUserIsClubEventManager(
        $clube_id,
        $user_id
    ) {
        $perm = DB::select('SELECT
ruc.user_id,
ruc.clube_id,
ruce.role_id
FROM
rel_users_clubes AS ruc
INNER JOIN rel_users_clubes_roles AS ruce ON ruce.ruc_id = ruc.id and ruce.role_id=5
WHERE
ruc.user_id = ? AND
ruc.clube_id = ?
', [$user_id, $clube_id]);
        DB::disconnect();

        if ($perm) {

            return true;


        }

        return false;


    }

    public
    function verificaUserIsEventManager(
        $evento_id,
        $user_id
    ) {

        $clubes_org = $this->getClubesOrganizadores($evento_id);

        //dd($clubes_org);

        foreach ($clubes_org as $co) {

            $perm = DB::select('SELECT
ruc.user_id,
ruc.clube_id,
ruce.role_id
FROM
rel_users_clubes AS ruc
INNER JOIN rel_users_clubes_roles AS ruce ON ruce.ruc_id = ruc.id and ruce.role_id=5
WHERE
ruc.user_id = ? AND
ruc.clube_id = ?
', [$user_id, $co->id]);
            DB::disconnect();

            if ($perm) {

                return true;


            }


        }

        return false;

    }


    public
    function getClubesOrganizadores(
        $id_evento
    ) {
        $clubes_organizadores = DB::select('select ca.*, rec.clube_id, rec.evento_id 
        from
        rel_eventos_clubes as rec
        left join clubes_associacoes as ca on ca.id = rec.clube_id
        where rec.evento_id = ?
        order by ca.nome asc', [$id_evento]);


        DB::disconnect();
        return $clubes_organizadores;

    }


    public
    function getJuizByRescId(
        $resc_id
    ) {
        $juiz = DB::select('SELECT
u.`name` as nome_juiz
FROM
rel_eventos_seccoes_classes AS resc
INNER JOIN users AS u ON resc.juiz_user_id = u.id
WHERE
resc.id = ?', [$resc_id]);
        DB::disconnect();

//dd($juiz);

        if ($juiz == null) {

            return "não classificado";

        } else {

            return $juiz[0]->nome_juiz;

        }


    }


    public
    function getEventoIdByInscricaoId(
        $id_inscricao
    ) {

        $evento = DB::table('eventos_inscricoes as ei')
            ->select('ei.evento_id')
            ->where('ei.id', $id_inscricao)
            ->first();


        DB::disconnect();
        return $evento->evento_id;
    }


    /**
     * @param $passaro_id
     * @return mixed
     */
    public
    function getRubricasByPassaroId(
        $passaro_id
    ) // para pdf fichas julgamento
    {

        $rubricas = DB::select('SELECT
pep.passaro_id,
pep.ficharubrica_id,
r.nome AS rubrica,
pep.pontos,
pep.evento_id,
rfr.valor_max,
r.pt,
r.en,
r.es,
r.fr,
r.nl
FROM
pontuacoes_eventos_passaros AS pep
INNER JOIN rel_fichas_rubricas AS rfr ON pep.ficharubrica_id = rfr.id
INNER JOIN rubricas AS r ON rfr.rubrica_id = r.id
WHERE
pep.passaro_id = ?', [$passaro_id]);
        DB::disconnect();

        return $rubricas;

    }

    public function getSobretaxaPais($evento_id, $pais_id)
    {

        $sobretaxa = DB::table('eventos_taxas_paises as etp')
            ->select('*')
            ->where('etp.evento_id', '=', $evento_id)
            ->where('etp.pais_id', '=', $pais_id)
            ->first();

        if (!$sobretaxa) {
            $sobretaxa = (object)array('taxa' => '-- ');
        }

        DB::disconnect();
        return $sobretaxa;
    }

    public function getSobretaxasPaises($evento_id)
    {

        $sobretaxas = DB::table('eventos_taxas_paises as etp')
            ->select('etp.*', 'p.pais')
            ->join('paises as p', 'etp.pais_id', '=', 'p.id')
            ->where('etp.evento_id', '=', $evento_id)
            ->get();

        DB::disconnect();
        return $sobretaxas;
    }

    public function getLocaisRecolha($evento_id, $pais_id = null)
    {

        if ($pais_id == null) {
            $locaisrecolha = DB::table('eventos_locais_recolha as elr')
                ->select('elr.*', 'p.pais_abrev', 'p.pais as paisnome', 'elr.pais_id')
                ->join('paises as p', 'elr.pais_id', '=', 'p.id')
                ->where('elr.evento_id', '=', $evento_id)
                ->orderBy('p.pais_abrev', 'asc')
                ->orderBy('elr.local_nome', 'asc')
                ->get();
        } else {
            $locaisrecolha = DB::table('eventos_locais_recolha as elr')
                ->select('elr.*', 'p.pais_abrev', 'p.pais as paisnome', 'elr.pais_id')
                ->join('paises as p', 'elr.pais_id', '=', 'p.id')
                ->where('elr.evento_id', '=', $evento_id)
                ->where('elr.pais_id', '=', $pais_id)
                ->orderBy('p.pais_abrev', 'asc')
                ->orderBy('elr.local_nome', 'asc')
                ->get();
        }

        DB::disconnect();
        return $locaisrecolha;
    }

    public
    function getEventoById(
        $id_evento
    ) {

        $hoje = date('Y-m-d');

        $evento = DB::table('eventos as e')
            ->join('tipos_evento as te', 'e.tipo_evento_id', '=', 'te.id')
            ->select('e.*', 'te.tipo_evento')
            ->where('e.id', '=', $id_evento)->first();

        //dd($evento);

        $evento->editavelcriador = ($evento->data_fim_inscricoes >= $hoje) ? true : false;

        DB::disconnect();
        return $evento;
    }

    public
    function getInscricaoById(
        $id_inscricao
    ) {

        $inscricao = DB::table('eventos_inscricoes as ei')
            ->join('users as u', 'ei.user_id', '=', 'u.id')
            ->join('paises as p', 'u.pais_id', '=', 'p.id')
            ->leftjoin('eventos_locais_recolha as lr', 'ei.local_recolha', '=', 'lr.id')
            ->select('ei.*', 'u.name', 'lr.local_nome', 'u.stam', 'u.morada', 'u.pais_id', 'p.pais_abrev', 'p.pais',
                'u.cod_postal', 'u.localidade1', 'u.email', 'u.telefone')
            ->orderBy('u.name', 'asc')
            ->where('ei.id', $id_inscricao)->first();


        // versão anterior à acima - funciona
        // alterei para a listagem de ficha de inscrição que necessita de dados do utilizador associado à inscrição
        //$inscricao = DB::table('eventos_inscricoes')
        //    ->where('id', $id_inscricao)->first();

//dd($inscricao);

        DB::disconnect();
        return $inscricao;
    }

    public
    function getNumPassarosByInscricao(
        $id_inscricao
    ) {

        $num = DB::table('eventos_inscricoes_passaros')
            ->where('inscricao_id', $id_inscricao)
            ->count();
        //dd($num);

        DB::disconnect();
        return $num;
    }

    public
    function verificaCriadorSocioByEvento(
        $user_id,
        $evento_id
    ) {

        $associado = DB::table('rel_users_clubes')
            ->select('clube_id')
            ->where('user_id', $user_id)
            ->get();

        //dd($associado);

        $clubes_evento = DB::table('rel_eventos_clubes')
            ->select('clube_id')
            ->where('evento_id', $evento_id)
            ->get();

        //dd($clubes_evento);
        DB::disconnect();

        foreach ($associado as $a) {


            // dd($a->clube_id);

            foreach ($clubes_evento as $ce) {

                if ($a->clube_id == $ce->clube_id) {
                    // criador está associado a clube organizador do evento
                    return true;
                }

            }
        }


// se chegou aqui, o criador não está associado a nenhum clube organizador do evento
        return false;

    }

    public
    function getCriadoresInscritosByEventoByPais(
        $id_evento,
        $pais_id,
        $ordenacao = 'nome'
    ) {
        $inscricoes = DB::table('eventos_inscricoes as ei')
            ->join('users as u', 'ei.user_id', '=', 'u.id')
            ->join('paises as p', 'u.pais_id', '=', 'p.id')
            ->select('ei.*', 'u.name', 'u.stam', 'u.pais_id', 'u.morada', 'p.pais_abrev', 'p.pais', 'u.cod_postal',
                'u.localidade1', 'u.email', 'u.telefone')
            ->orderBy('p.pais', 'asc')
            ->orderBy('u.name', 'asc')
            ->where('ei.evento_id', '=', $id_evento)
            ->where('u.pais_id', '=', $pais_id)
            ->get();

        DB::disconnect();
        return $inscricoes;

    }

    public
    function getCriadoresInscritosByEvento(
        $id_evento,
        $ordenacao = 'nome'
    ) {

        switch ($ordenacao) {
            case 'local':
                $inscricoes = DB::table('eventos_inscricoes as ei')
                    ->join('users as u', 'ei.user_id', '=', 'u.id')
                    ->join('paises as p', 'u.pais_id', '=', 'p.id')
                    ->leftjoin('eventos_locais_recolha as lr', 'ei.local_recolha', '=', 'lr.id')
                    ->select('ei.*', 'lr.local_nome', 'u.name', 'u.stam', 'u.pais_id', 'u.morada', 'p.pais_abrev',
                        'p.pais', 'u.cod_postal', 'u.localidade1', 'u.email', 'u.telefone')
                    ->orderBy('p.pais', 'asc')
                    ->orderBy('lr.local_nome', 'asc')
                    ->orderBy('u.name', 'asc')
                    ->where('ei.evento_id', '=', $id_evento)->get();
                break;
            case 'pais_expositor':
                $inscricoes = DB::table('eventos_inscricoes as ei')
                    ->join('users as u', 'ei.user_id', '=', 'u.id')
                    ->join('paises as p', 'u.pais_id', '=', 'p.id')
                    ->leftjoin('eventos_locais_recolha as lr', 'ei.local_recolha', '=', 'lr.id')
                    ->select('ei.*', 'lr.local_nome', 'u.name', 'u.stam', 'u.pais_id', 'u.morada', 'p.pais_abrev',
                        'p.pais', 'u.cod_postal', 'u.localidade1', 'u.email', 'u.telefone')
                    ->orderBy('p.pais', 'asc')
                    ->orderBy('ei.num_expositor', 'asc')
                    ->where('ei.evento_id', '=', $id_evento)->get();
                break;
            case 'pais':
                $inscricoes = DB::table('eventos_inscricoes as ei')
                    ->join('users as u', 'ei.user_id', '=', 'u.id')
                    ->join('paises as p', 'u.pais_id', '=', 'p.id')
                    ->leftjoin('eventos_locais_recolha as lr', 'ei.local_recolha', '=', 'lr.id')
                    ->select('ei.*', 'lr.local_nome', 'u.name', 'u.stam', 'u.pais_id', 'u.morada', 'p.pais_abrev',
                        'p.pais', 'u.cod_postal', 'u.localidade1', 'u.email', 'u.telefone')
                    ->orderBy('p.pais', 'asc')
                    ->orderBy('u.name', 'asc')
                    ->where('ei.evento_id', '=', $id_evento)->get();
                break;

            case 'nome':
                $inscricoes = DB::table('eventos_inscricoes as ei')
                    ->join('users as u', 'ei.user_id', '=', 'u.id')
                    ->join('paises as p', 'u.pais_id', '=', 'p.id')
                    ->leftjoin('eventos_locais_recolha as lr', 'ei.local_recolha', '=', 'lr.id')
                    ->select('ei.*', 'lr.local_nome', 'u.name', 'u.stam', 'u.pais_id', 'u.morada', 'p.pais_abrev',
                        'p.pais', 'u.cod_postal', 'u.localidade1', 'u.email', 'u.telefone')
                    ->orderBy('u.name', 'asc')
                    ->where('ei.evento_id', '=', $id_evento)->get();
                break;
            case 'num_expositor':

                $inscricoes = DB::table('eventos_inscricoes as ei')
                    ->join('users as u', 'ei.user_id', '=', 'u.id')
                    ->join('paises as p', 'u.pais_id', '=', 'p.id')
                    ->leftjoin('eventos_locais_recolha as lr', 'ei.local_recolha', '=', 'lr.id')
                    ->select('ei.*', 'lr.local_nome', 'u.name', 'u.stam', 'u.pais_id', 'u.morada', 'p.pais_abrev',
                        'p.pais', 'u.cod_postal', 'u.localidade1', 'u.email', 'u.telefone')
                    ->orderBy('ei.num_expositor', 'asc')
                    ->where('ei.evento_id', '=', $id_evento)->get();
                break;
        }

        DB::disconnect();
        return $inscricoes;

    }

    public
    function getNumEquipasByInscricao(
        $id_inscricao
    ) {

        $num = DB::select("select count(id) as n from eventos_inscricoes_passaros where inscricao_id=? and RIGHT(numgaiola,1)='A'",
            [$id_inscricao]);

        DB::disconnect();
        return $num[0]->n;
    }

    public
    function getPassarosByEvento(
        $id_evento
    ) {

        $passaros = DB::table('eventos_inscricoes_passaros as eip')
            ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
            ->join('eventos_inscricoes as ei', 'eip.inscricao_id', '=', 'ei.id')
            ->orderBy('resc.seccao', 'asc')
            ->orderByRaw('CAST(resc.classe AS SIGNED) asc')
            ->orderByRaw('CAST(numerogaiola AS SIGNED) asc')
            ->orderByRaw('letragaiola asc')
            ->where('ei.evento_id', '=', $id_evento)
            ->selectRaw("resc.nome,eip.id, eip.gaiola_propria,eip.anilha, eip.ano, eip.preco,eip.vendido,eip.ausente,eip.observacao,eip.numgaiola,resc.seccao, resc.classe, IF(RIGHT(eip.numgaiola,1) IN ('A', 'B', 'C', 'D'),RIGHT(eip.numgaiola,1), ' ') as letragaiola,
IF(RIGHT(eip.numgaiola,1) IN ('A', 'B', 'C', 'D'),LEFT(eip.numgaiola, LENGTH(eip.numgaiola) - 1) , numgaiola) as numerogaiola")
            ->get();

        DB::disconnect();
        // dd(count($passaros));
        return $passaros;
    }

    /*
     * napoles 2023
     */
    public
    function getPassarosByEventoNapoles2023Order(
        $id_evento
    ) {

        $passaros = DB::table('eventos_inscricoes_passaros as eip')
            ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
            ->join('eventos_inscricoes as ei', 'eip.inscricao_id', '=', 'ei.id')
            ->orderBy('resc.seccao', 'asc')
            ->orderByRaw('CAST(resc.classe AS SIGNED) asc')
            ->orderByRaw('CAST(numerogaiola AS SIGNED) asc')
            ->orderByRaw('letragaiola asc')
            ->where('ei.evento_id', '=', $id_evento)
            ->selectRaw("resc.nome,eip.id, eip.gaiola_propria,eip.anilha, eip.ano, eip.preco,eip.vendido,eip.ausente,eip.observacao,eip.numgaiola,resc.seccao, resc.classe, IF(RIGHT(eip.numgaiola,1) IN ('A', 'B', 'C', 'D'),RIGHT(eip.numgaiola,1), ' ') as letragaiola,
IF(RIGHT(eip.numgaiola,1) IN ('A', 'B', 'C', 'D'),LEFT(eip.numgaiola, LENGTH(eip.numgaiola) - 1) , numgaiola) as numerogaiola")
            ->get();

        DB::disconnect();
        // dd(count($passaros));
        return $passaros;
    }




    public
    function getPassarosByEventoEquipas(
        $id_evento
    ) {

        $passaros = DB::table('eventos_inscricoes_passaros as eip')
            ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
            ->join('eventos_inscricoes as ei', 'eip.inscricao_id', '=', 'ei.id')
            ->orderBy('resc.seccao', 'asc')
            ->orderByRaw('resc.classe*1 asc')
            ->orderByRaw('numerogaiola asc')
            ->orderByRaw('letragaiola asc')
            ->where('ei.evento_id', '=', $id_evento)
            ->whereRaw('resc.classe%2 <> 0')
            ->selectRaw("resc.nome,eip.id, eip.gaiola_propria,eip.anilha, eip.ano, eip.preco,eip.vendido,eip.ausente,eip.observacao,eip.numgaiola,resc.seccao, resc.classe, IF(RIGHT(eip.numgaiola,1) IN ('A', 'B', 'C', 'D'),RIGHT(eip.numgaiola,1), ' ') as letragaiola,
IF(RIGHT(eip.numgaiola,1) IN ('A', 'B', 'C', 'D'),LEFT(eip.numgaiola, LENGTH(eip.numgaiola) - 1) , numgaiola) as numerogaiola")
            ->get();

        DB::disconnect();
        return $passaros;
    }

    public function getPassarosByEventoIndividuais($id_evento)
    {

        $passaros = DB::table('eventos_inscricoes_passaros as eip')
            ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
            ->join('eventos_inscricoes as ei', 'eip.inscricao_id', '=', 'ei.id')
            ->orderBy('resc.seccao', 'asc')
            ->orderByRaw('resc.classe*1 asc')
            ->orderByRaw('eip.numgaiola*1 asc')
            ->where('ei.evento_id', '=', $id_evento)
            ->whereRaw('resc.classe%2 = 0')
            ->selectRaw('resc.nome,eip.id, eip.gaiola_propria,eip.anilha, eip.ano, eip.preco,eip.vendido,eip.ausente,eip.observacao,eip.numgaiola,resc.seccao, resc.classe')
            ->get();


        DB::disconnect();
        // dd(count($passaros));
        return $passaros;

    }


    public
    function getPassaroByEventoGaiola(
        $id_evento,
        $numgaiola
    ) {

        $passaro = DB::table('eventos_inscricoes_passaros as eip')
            ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
            ->join('eventos_inscricoes as ei', 'eip.inscricao_id', '=', 'ei.id')
            ->orderBy('resc.seccao', 'asc')
            ->orderBy('resc.classe', 'asc')
            ->orderBy('eip.numgaiola', 'asc')
            ->where('eip.numgaiola', '=', $numgaiola)
            ->where('ei.evento_id', '=', $id_evento)
            ->selectRaw('resc.nome,eip.id, eip.gaiola_propria,eip.anilha, eip.ano, eip.preco,eip.vendido,eip.ausente,eip.observacao,eip.numgaiola,resc.seccao, resc.classe,eip.pontos,eip.pontosequipa,eip.fc,eip.nj,eip.dq,eip.harmonia')
            ->get();

        DB::disconnect();
        return $passaro[0];
    }


    public
    function getPassarosByInscricao(
        $id_inscricao
    ) {

        $passaros = DB::table('eventos_inscricoes_passaros as eip')
            ->join('rel_eventos_seccoes_classes as resc', 'eip.rel_evento_seccao_classe_id', '=', 'resc.id')
            ->where('eip.inscricao_id', '=', $id_inscricao)
            ->selectRaw('LEFT(resc.nome,40) nome,eip.id, eip.anilha, eip.ano, eip.preco,eip.vendido,eip.ausente,eip.observacao,eip.numgaiola,eip.gaiola_propria,resc.seccao, resc.classe, resc.id as rescid')
            ->orderByRaw('resc.seccao asc, resc.classe*1 asc, eip.numgaiola asc')
            ->get();

        foreach ($passaros as $p) {
            if ($p->preco == "0") {
                $p->preco = '';
            }
            if ($p->vendido == "0") {
                $p->vendido = false;
            } else {
                $p->vendido = true;
            }
            if ($p->ausente == "0") {
                $p->ausente = false;
            } else {
                $p->ausente = true;
            }
            if ($p->gaiola_propria == "0") {
                $p->gaiola_propria = false;
            } else {
                $p->gaiola_propria = true;
            }

        }

        DB::disconnect();
        //dd($passaros);
        return $passaros;

    }

    public
    function getJuizesDistinctByEventoDistinct(
        $id_evento
    ) {

        $ljuizes = DB::table('juizes_eventos_seccoes_classes')
            ->join('users', 'users.id', '=', 'user_id')
            ->select('user_id', 'evento_id', 'seccao', 'resc_id')
            ->where('evento_id', '=', $id_evento)
            ->groupBy('users.id')
            ->get();


        DB::disconnect();
        return $ljuizes;

    }

    public
    function getSeccaoGaiola(
        $id_evento,
        $id_user
    ) {

        $lista_resc_id = DB::table('juizes_eventos_seccoes_classes')
            ->join('eventos_inscricoes', 'juizes_eventos_seccoes_classes.evento_id', '=', 'eventos_inscricoes.id')
            ->join('eventos_inscricoes_passaros', 'eventos_inscricoes_passaros.inscricao_id', '=',
                'eventos_inscricoes.id')
            ->select('juizes_eventos_seccoes_classes.user_id', 'juizes_eventos_seccoes_classes.evento_id', 'seccao',
                'resc_id', 'inscricao_id', 'anilha', 'numgaiola',
                'eventos_inscricoes_passaros.rel_evento_seccao_classe_id')
            ->where('juizes_eventos_seccoes_classes.user_id', '=', $id_user)
            ->where('eventos_inscricoes.evento_id', '=', $id_evento)
            ->where('juizes_eventos_seccoes_classes.resc_id', '=',
                'eventos_inscricoes_passaros.rel_evento_seccao_classe_id')
            ->get();


        DB::disconnect();
        return $lista_resc_id;
    }

    public
    function getAvesJuizesSeccao(
        $id_evento,
        $id_user
    ) {

        $aves_juizes_seccao = DB::table('juizes_eventos_seccoes_classes')
            ->join('eventos_inscricoes', 'eventos_inscricoes.evento_id', '=',
                'juizes_eventos_seccoes_classes.evento_id')
            ->join('eventos_inscricoes_passaros', 'eventos_inscricoes_passaros.inscricao_id', '=',
                'eventos_inscricoes_passaros.id')
            ->join('rel_eventos_seccoes_classes', 'eventos_inscricoes_passaros.rel_evento_seccao_classe_id', '=',
                'rel_eventos_seccoes_classes.id')
            ->where('juizes_eventos_seccoes_classes.user_id', '=', $id_user)
            ->where('eventos_inscricoes.evento_id', '=', $id_evento)
            ->where('juizes_eventos_seccoes_classes.seccao', 'like', 'rel_eventos_seccoes_classes.seccao')
            ->get();


        DB::disconnect();
        return $aves_juizes_seccao;

    }


    public
    function getNomeById(
        $id_user
    ) {

        $nome = DB::table('users')
            ->select('name')
            ->where('users.id', '=', $id_user)
            ->get();


        DB::disconnect();
        return $nome[0]->name;
    }

    public
    function getGaiolasJulgamento(
        $id_evento,
        $id_user
    ) {

        $first = '(SELECT jesc.user_id, jesc.evento_id,jesc.seccao as jseccao,
                    jesc.resc_id,
                    eip.inscricao_id,
                    eip.anilha,
                    eip.id,
                    eip.numgaiola,
                    resc.seccao,
                    resc.classe,
                    resc.nome
                    FROM
                    juizes_eventos_seccoes_classes AS jesc
                    INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
                    INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id
                    INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id
                    WHERE
                    jesc.user_id = ';
        $second = ' AND
                    ei.evento_id = ';
        $third = ' AND jesc.seccao COLLATE utf8_general_ci = resc.seccao COLLATE utf8_general_ci)
                    UNION(SELECT
                    jesc.user_id,
                    jesc.evento_id,
                    jesc.seccao,
                    jesc.resc_id,
                    eip.inscricao_id,
                    eip.anilha,
                    eip.id,
                    eip.numgaiola,
                    resc.seccao,
                    resc.classe,
                    resc.nome
                    FROM
                    juizes_eventos_seccoes_classes AS jesc
                    INNER JOIN eventos_inscricoes AS ei ON jesc.evento_id = ei.evento_id
                    INNER JOIN eventos_inscricoes_passaros AS eip ON eip.inscricao_id = ei.id
                    INNER JOIN rel_eventos_seccoes_classes AS resc ON eip.rel_evento_seccao_classe_id = resc.id
                    WHERE
                    jesc.user_id = ';
        $forth = ' AND ei.evento_id = ';
        $five = ' AND jesc.resc_id = eip.rel_evento_seccao_classe_id)';

        $lista = DB::select($first . $id_user . $second . $id_evento . $third . $id_user . $forth . $id_evento . $five);
        DB::disconnect();
        return $lista;

    }

    public
    function getNumjuizesByEvento(
        $evento_id
    ) {


        $n_juizes = DB::table('juizes_eventos_seccoes_classes')
            ->where('evento_id', '=', $evento_id)
            ->count();


        DB::disconnect();
        return $n_juizes;
    }

    public
    function getNumFichasJulgamentoByEvento(
        $evento_id
    ) {


        $n_fichas = DB::table('rel_fichas_eventos_seccoes_classes')
            ->where('evento_id', '=', $evento_id)
            ->count();
        DB::disconnect();
        //dd($n_fichas);
        return $n_fichas;


    }


    public
    function getNumClassesByEvento(
        $id_evento
    ) {
        $n_inscricoes = DB::table('rel_eventos_seccoes_classes')
            ->where('evento_id', '=', $id_evento)
            ->count();


        DB::disconnect();
        return $n_inscricoes;
    }

    public
    function getInscricaoByNumExpositor(
        $id_evento,
        $num_expositor
    ) {

        $inscricao = DB::table('eventos_inscricoes')
            ->where('evento_id', $id_evento)
            ->where('num_expositor', $num_expositor)
            ->select('id')
            ->first();

        DB::disconnect();
        return $inscricao->id;
    }


    public
    function verificaEventoActivoParaInscricaoById(
        $id_evento
    ) {
        $evento = $this->getEventoById($id_evento);

        $hoje = date('Y-m-d');

        //dd($evento);

        if ($evento->data_inicio_inscricoes <= $hoje && $evento->data_fim_inscricoes >= $hoje) {

            return true;
        }

        return false;
    }


// verifica se evento está dentro das inscrições
    public
    function verificaEventoActivoById(
        $id_evento
    ) {

        $evento = $this->getEventoById($id_evento);

        $hoje = date('Y-m-d');

        //dd($evento->data_inicio_inscricoes." - ".$hoje." - ".$evento->data_fim);

        if ($evento->data_inicio_inscricoes <= $hoje && $evento->data_fim >= $hoje) {

            return true;
        }

        return false;

    }

// verifica se evento ainda não terminou - data julgamento, etc.
    public
    function verificaEventoActivoAdminById(
        $id_evento
    ) {

        $evento = $this->getEventoById($id_evento);

        $hoje = date('Y-m-d');

        //dd($evento);

        if ($evento->data_inicio_inscricoes <= $hoje && $evento->data_fim >= $hoje) {

            return true;
        }

        return false;

    }

    public
    function verificaInscricaoUserActivoById(
        $inscricao_id
    ) {

        $inscricao = $this->getInscricaoById($inscricao_id);


        $user_id = Auth::id();

        // verifica também se a inscricao pertence a um evento activo
        if (!$this->verificaEventoActivoById($inscricao->evento_id)) {

            return false;

        }

        if (!$inscricao == null) {
            if ($inscricao->user_id == $user_id) {

                return true;
            }

        }

        return false;

    }

}

