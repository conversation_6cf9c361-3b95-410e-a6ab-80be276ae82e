<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


/*
 * Authentication routes taken from
 * C:\wamp\www\fonpadmin5.4\fonpadmin\vendor\laravel\framework\src\Illuminate\Support\Facades\Auth.php
 *
 *  replaces Auth::routes();
 */


/*
 * LOCALIZATION ROUTE GROUPS
 *
 */

use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
use Vsch\TranslationManager\Translator;

Route::group(['middleware' => 'web', 'prefix' => 'translations'], function () {
    Translator::routes();
});

Route::group(
    [
        'prefix' => LaravelLocalization::setLocale(),
        'middleware' => ['localeSessionRedirect', 'localizationRedirect']
    ],
    function () {


// Authentication Routes...
        Route::get('login', 'Auth\LoginController@showLoginForm')->name('login');
        Route::post('login', 'Auth\LoginController@login');
        //Route::post('logout', 'Auth\LoginController@logout')->name('logout');
        Route::get('logout', '\App\Http\Controllers\Auth\LoginController@logout');

// Registration Routes...
        Route::get('register', 'Auth\RegisterController@showRegistrationForm')->name('register');
        Route::post('register', 'Auth\RegisterController@register');

// Password Reset Routes...
        Route::get('password/reset', 'Auth\ForgotPasswordController@showLinkRequestForm')->name('password.request');
        Route::post('password/email', 'Auth\ForgotPasswordController@sendResetLinkEmail')->name('password.email');
        Route::get('password/reset/{token}', 'Auth\ResetPasswordController@showResetForm')->name('password.reset');
        Route::post('password/reset', 'Auth\ResetPasswordController@reset');

        // GDPR
        Route::get('gdpr', ['as' => 'gdpr_accept', 'uses' => 'UserController@gdprAccept']);
        Route::post('gdpr2', ['as' => 'gdpr_accept_store', 'uses' => 'UserController@gdprAcceptStore']);
        Route::get('gdprno', ['as' => 'gdpr', 'uses' => 'UserController@gdprAcceptDeny']);

// Activação de conta já existente - inicio do sistema

// input stam
        Route::get('activarregisto', ['as' => 'activarregisto', 'uses' => 'ActivacaoController@activarRegisto']);

// visualiza dados pessoais e confirma
        Route::post('activarregisto1', ['as' => 'activarregisto1', 'uses' => 'ActivacaoController@activarRegisto1']);

        Route::get('activarregisto2/{user_id}',
            ['as' => 'activarregisto2', 'uses' => 'ActivacaoController@activarRegisto2']);


// Activação concluida
        Route::post('activarregisto3', ['as' => 'activarregisto3', 'uses' => 'ActivacaoController@activarRegisto3']);


    });


Route::group(
    [
        'prefix' => LaravelLocalization::setLocale(),
        'middleware' => ['localeSessionRedirect', 'localizationRedirect', 'CheckGdprAccepted']
    ],
    function () {
        /** ADD ALL LOCALIZED ROUTES INSIDE THIS GROUP **/

        // neste método faz a escolha de dashboard de admin ou de criador
        Route::get('/', ['as' => 'casa', 'uses' => 'HomeController@index0']);

        // dashboard de admin
        Route::get('/adminhome',
            ['as' => 'casaadmin', 'uses' => 'HomeController@indexadmin'])->middleware(['userIsAdmin']);

        // dashboard de criador
        Route::get('/home', ['as' => 'homecriador', 'uses' => 'HomeController@indexcriador']);


        /*
        *  _    _
        * | |  | |
        * | |  | |___  ___ _ __
        * | |  | / __|/ _ \ '__|
        * | |__| \__ \  __/ |
        *  \____/|___/\___|_|
        *
        */


        /*
         * PDFS EXPORTS
         */

        Route::get('lista_geral_passaros/{id_evento}', [
            'as' => 'listageralpassarospais',
            'uses' => 'ListagensController@listaGeralPassaroPais'
        ])->middleware(['userIsEventManagerOrAdmin']);


        Route::get('pdf_catalogo_aves/{id_evento}/{com_classificacoes?}',
            ['as' => 'pdfcatalogoavesclasse', 'uses' => 'ListagensController@pdfCatalogoAvesClasse'])->middleware(['userIsAdmin']);;

        Route::get('pdf_catalogo_aves_condensado/{id_evento}', [
            'as' => 'pdfcatalogoavesclassecondensado',
            'uses' => 'ListagensController@pdfCatalogoAvesClasseCondensado'
        ])->middleware(['userIsAdmin']);;
        Route::get('pdf_catalogo_aves_condensado_mundial/{id_evento}', [
            'as' => 'pdfcatalogoavesclassecondensadomundial',
            'uses' => 'ListagensController@pdfCatalogoAvesClasseCondensadoMundial'
        ]);

        Route::get('pdf_fichas_inscricao/{id_evento}',
            ['as' => 'pdffichasinscricaoadmin', 'uses' => 'ListagensController@pdfFichasInscricao']);

        Route::get('pdf_fichas_inscricao/{id_evento}/{sort}',
            ['as' => 'pdffichasinscricaoadminsort', 'uses' => 'ListagensController@pdfFichasInscricaoSort']);


        Route::group(['prefix' => 'criador'], function () {
            Route::get('pdf_ficha_inscricao/{id_evento}', [
                'as' => 'pdffichainscricao',
                'uses' => 'ListagensController@pdfFichaInscricao'
            ])->middleware(['eventoActivo', 'inscricaoUserActivo']);
        });

//        Route::group(['prefix' => 'criador'], function () {
//            Route::get('pdf_ficha_inscricao/{id_evento}', ['as' => 'pdffichainscricao', 'uses' => 'ListagensController@pdfFichaInscricao'])->middleware(['userIsEventManagerOrAdmin']);
//        });


        Route::get('pdf_ficha_inscricao/{id_evento}/{id_inscricao}',
            ['as' => 'pdffichainscricaoindividual', 'uses' => 'ListagensController@pdfFichaInscricaoIndividual']);

        Route::get('pdf_etiquetas_provisorias/{id_evento}/{id_inscricao?}',
            ['as' => 'pdfetiquetasprovisorias', 'uses' => 'ListagensController@pdfEtiquetasProvisorias']);

        Route::get('pdf_etiquetas_provisorias_tipo/{id_evento}/{tipo}/{gaiola_propria_assinalada?}',
            ['as' => 'pdfetiquetasprovisoriastipo', 'uses' => 'ListagensController@pdfEtiquetasProvisoriasPorTipo']);
//
//        Route::get('pdf_etiquetas_provisorias_tipo/{id_evento}/{tipo}', ['as' => 'pdfetiquetasprovisoriasindividuais', 'uses' => 'ListagensController@pdfEtiquetasProvisoriasPorTipo']);

        Route::get('pdf_fichas_controlo_julgamento_seccao_juiz/{id_evento}', [
            'as' => 'pdffichascontrolojulgamentoseccaojuiz',
            'uses' => 'ListagensController@pdfFichasControloJulgamentoSeccaoJuiz'
        ])->middleware(['userIsAdmin']);;

        Route::get('pdf_fichas_controlo_julgamento/{id_evento}',
            ['as' => 'pdffichascontrolojulgamento', 'uses' => 'ListagensController@pdfFichasControloJulgamento'])->middleware(['userIsAdmin']);;

        Route::get('pdf_fichas_julgamento_evento/{id_evento}/{sort?}', [
            'as' => 'pdffichasjulgamentoevento',
            'uses' => 'ListagensController@pdfFichasJulgamentoEvento'
        ])->middleware(['userIsEventManagerOrAdmin']);

        Route::get('pdf_fichas_julgamento_expositor/{id_evento}', [
            'as' => 'pdffichasjulgamentoexpositor',
            'uses' => 'ListagensController@pdfFichasJulgamentoExpositor'
        ])->middleware(['UserIsRegisteredInEvent', 'DaysPassedAfterEventJudging']);

        Route::post('pdf_etiquetas_definitivas_individuais', [
            'as' => 'pdfetiquetasdefinitivasindividuais',
            'uses' => 'EtiquetasController@pdfEtiquetasDefinitivasIndividuais'
        ]);

        // APENAS PARA O EVENTO DE NAPOLES 2022

        // listagem 14.1 e 14.2 (ordem por gaiolas)
        Route::get('pdf_etiquetas_definitivas_nap/{id_evento}/{order}',
            ['as' => 'pdfetiquetasdefinitivasnapoles', 'uses' => 'ListagensController@pdfEtiquetasDefinitivasNapoles']);

        // listagem 8.1 e 8.2 (ordem por gaiolas)
        Route::get('pdf_etiquetas_provisorias_nap/{id_evento}/{order}',
            ['as' => 'pdfetiquetasprovisoriasnapoles', 'uses' => 'ListagensController@pdfEtiquetasProvisoriasNapoles']);

        Route::get('pdf_etiquetas_escaparates/{id_evento}',
            ['as' => 'pdfetiquetasescaparates', 'uses' => 'ListagensController@pdfEtiquetasEscaparates']);


// todo: proteger as listagens apenas para admins e gestores

        Route::get('pdf_etiquetas_definitivas/{id_evento}',
            ['as' => 'pdfetiquetasdefinitivas', 'uses' => 'ListagensController@pdfEtiquetasDefinitivas']);

        Route::get('pdf_etiquetas_definitivas_tipo/{id_evento}/{tipo}',
            ['as' => 'pdfetiquetasdefinitivastipo', 'uses' => 'ListagensController@pdfEtiquetasDefinitivasPorTipo']);

        Route::get('pdf_fichas_devolucao/{id_evento}',
            ['as' => 'pdffichasdevolucao', 'uses' => 'ListagensController@pdfFichasDevolucao']);

        Route::get('pdf_fichas_devolucao_pais/{id_evento}',
            ['as' => 'pdffichasdevolucaopais', 'uses' => 'ListagensController@pdfFichasDevolucaoPaisExpositor']);

        Route::get('exporta_num_gaiolas_tipo/{id_evento}/{formato}',
            ['as' => 'exportanumgaiolastipo', 'uses' => 'ListagensController@exportaNumGaiolasTipo']);

        Route::get('exporta_expositores/{id_evento}/{ordenacao}', [
            'as' => 'exportaexpositores',
            'uses' => 'ListagensController@exportaExpositores'
        ])->middleware(['userIsEventManagerOrAdmin']);


        Route::get('exporta_expositores_contactos/{id_evento}', [
            'as' => 'exportaexpositorescontactos',
            'uses' => 'ListagensController@exportaExpositoresContactos'
        ])->middleware(['userIsEventManagerOrAdmin']);


        Route::get('exporta_expositores_contactos_pais_excel/{id_evento}', [
            'as' => 'exportaexpositorescontactospaisexcel',
            'uses' => 'ListagensController@exportaExpositoresContactosPorPaisExcel'
        ])->middleware(['userIsEventManagerOrAdmin']);

        Route::get('exporta_expositores_geral_excel/{id_evento}', [
            'as' => 'exportaexpositoresgeralexcel',
            'uses' => 'ListagensController@exportaExpositoresInformacaoGeralExcel'
        ])->middleware(['userIsEventManagerOrAdmin']);

        Route::get('exporta_expositores_contactos_pais/{id_evento}', [
            'as' => 'exportaexpositorescontactospais',
            'uses' => 'ListagensController@exportaExpositoresContactosPorPais'
        ])->middleware(['userIsEventManagerOrAdmin']);

        Route::get('exporta_expositores_classificacoes/{id_evento}', [
            'as' => 'exportaexpositoresclassificacoes',
            'uses' => 'ListagensController@exportaExpositoresClassificacoes'
        ])->middleware(['userIsEventManagerOrAdmin']);

        // gestor nacional - exporta classificações de expositores
        Route::get('export_breeders_points/{id_evento}', [
            'as' => 'exportbreederspoints',
            'uses' => 'ListagensController@exportaExpositoresClassificacoesGestorN'
        ])->middleware(['userIsGestorN']);

        Route::get('exporta_numextrasexpositores/{id_evento}',
            ['as' => 'exportanumextrasexpositores', 'uses' => 'ListagensController@exportaNumExtrasExpositores']);

        Route::get('exporta_num_gaiolas_tipo_seccao/{id_evento}',
            ['as' => 'exportanumgaiolastiposeccao', 'uses' => 'ListagensController@exportaNumGaiolasTipoSeccao']);

        Route::get('exporta_num_passaros_classe/{id_evento}',
            ['as' => 'exportanumpassarosclasse', 'uses' => 'ListagensController@exportaNumPassarosClasse']);

        Route::get('exporta_expositoresdespesas/{id_evento}/{formato?}',
            ['as' => 'exportaexpositoresdespesas', 'uses' => 'ListagensController@exportaExpositoresDespesas'])->middleware(['userIsAdmin']);;

        Route::get('ficha_engaiolamento_individual/{id_evento}',
            ['as' => 'fichaengaiolamentoindividual', 'uses' => 'EventoController@etiquetasProvisoriasIndividuais']);

        Route::post('exporta_fichaengaiolamentoindividual', [
            'as' => 'exportafichaengaiolamentoindividual',
            'uses' => 'ListagensController@exportaFichaEngaiolamentoIndividual'
        ]);

        Route::get('pdf_fichas_controlo_julgamento/{id_evento}/{sort?}',
            ['as' => 'pdffichascontrolojulgamento', 'uses' => 'ListagensController@pdfFichasControloJulgamento'])->middleware(['userIsAdmin']);;

        Route::get('exporta_controloanilhas/{id_evento}',
            ['as' => 'exportacontroloanilhas', 'uses' => 'ListagensController@exportaControloAnilhas']);

        Route::get('exporta_premioscriador/{id_evento}',
            ['as' => 'exportapremioscriador', 'uses' => 'ListagensController@exportaPremiosCriador'])->middleware(['userIsAdmin']);;

        Route::get('exporta_premiosprestigio/{id_evento}',
            ['as' => 'exportapremiosprestigio', 'uses' => 'ListagensController@exportaPremiosPrestigio'])->middleware(['userIsAdmin']);;

        Route::get('exporta_diplomas/{id_evento}/{tipo}/{extras?}/{sort?}',
            ['as' => 'exportadiplomas', 'uses' => 'ListagensController@exportaDiplomas'])->middleware(['userIsAdmin']);;

        // gestor nacional - exporta diplomas
        Route::get('export_prizes_docs/{id_evento}/{tipo}/{extras?}/{sort?}', [
            'as' => 'exportprizesdocs',
            'uses' => 'ListagensController@exportaDiplomasGestorN'
        ])->middleware(['userIsGestorN']);


        Route::get('exporta_pagamentos_criador/{id_evento}/{user_id}',
            ['as' => 'exportapagamentoscriador', 'uses' => 'ListagensController@exportaPagamentosCriador']);

        Route::get('lista_medalhas/{id_evento}/{sort?}',
            ['as' => 'listamedalhas', 'uses' => 'ListagensController@listaMedalhas'])->middleware(['userIsAdmin']);;

        // gestor nacional - listar medalhas
        Route::get('prizes_list/{id_evento}/{sort?}', [
            'as' => 'prizeslist',
            'uses' => 'ListagensController@listaMedalhasGestorN'
        ])->middleware(['userIsGestorN']);


        Route::get('lista_totais_classes/{id_evento}/{sort?}',
            ['as' => 'listatotaisclasses', 'uses' => 'ListagensController@listaTotaisClasses']);

        //Route::get('pdf_fichas_julgamento_evento/{id_evento}', ['as' => 'pdffichasjulgamentoevento', 'uses' => 'ListagensController@pdfFichasJulgamentoEvento'])->middleware(['userIsEventManagerOrAdmin']);

        /*
         * FIM PDFS EXPORTS
         *
         */

        Route::get('clube_eventos/{id_clube}', [
            'as' => 'clube_eventos_home',
            'uses' => 'ClubeController@eventosHome'
        ])->middleware(['userIsClubEventManager']);

        // 20250817 - TODO - preciso fazer um middlware para verificar se o user é admin
        // OU gestor de club de evento (clubeventmanager
        // a rota abaixo verifica se o user é admin e clubeventmanager, e deve ser OU

//        Route::get('clube_eventos/{id_clube}', [
//            'as' => 'clube_eventos_home',
//            'uses' => 'ClubeController@eventosHome'
//        ])->middleware(['userIsClubEventManager'])->middleware(['userIsAdmin']);


        /*
         *  CRIADORES
         *
         */

        // home page evento - vista criador

        Route::get('criador_evento/{id_evento}',
            [
                'as' => 'eventohomecriador',
                'uses' => 'EventoController@gerirEventoCriador'
            ])->middleware(['eventoActivo']);

        // editar perfil criador

        Route::get('criador_perfil', ['as' => 'criadorperfil', 'uses' => 'UserController@editOwnProfile']);


        // inscrever criador - abre popup ajax

        Route::get('inscrever_criador/{id_evento}/{id_criador}',
            ['as' => 'inscrevercriador', 'uses' => 'EventoController@formInscreverCriador']);
        // grava inscrição
        Route::post('gravarInscricaoCriadorEvento',
            ['as' => 'gravarinscricaocriadorevento', 'uses' => 'EventoController@gravarInscricaoCriadorEvento']);
        // editar inscrição de criador

        // abre ajax
        Route::get('editar_inscricao/{id_inscricao}/editar',
            ['as' => 'editarinscricaocriador', 'uses' => 'EventoController@formEditarInscricaoCriador']);


        Route::post('updateInscricaoCriadorEvento',
            ['as' => 'updateinscricaocriadorevento', 'uses' => 'EventoController@updateInscricaoCriadorEvento']);

        Route::post('removerInscricaoCriadorEvento',
            ['as' => 'removerinscricaocriadorevento', 'uses' => 'EventoController@removerInscricaoCriadorEvento']);

        // historico de eventos


        Route::get('eventos/historico',
            ['as' => 'eventoshistoricocriador', 'uses' => 'EventoController@listaEventosHistoricoCriador']);


        Route::get('eventos/historico/{id_evento}', [
            'as' => 'vereventohistoricocriador',
            'uses' => 'EventoController@verEventoHistoricoCriador'
        ])->middleware(['UserIsRegisteredInEvent']);


// inscrição de pássaros - vista criador

        Route::get('gerir_passaros/{id_inscricao}', [
            'as' => 'gerirpassaroscriador',
            'uses' => 'EventoController@gerirPassarosCriador'
        ])->middleware(['inscricaoUserActivo', 'eventoActivoParaInscricao']);

        /*
         *
         * GESTOR NACIONAL
         * TODAS AS ROUTES
         */

        Route::match(['get', 'post'], 'gestornusers/list',
            ['as' => 'gestornuserslist', 'uses' => 'UserController@getUserListGestorN'])->middleware(['userIsGestorN']);

        Route::get('gestornusers/{id_user}/edit',
            ['as' => 'gestornusereditorigem', 'uses' => 'UserController@gestornuseredit'])->middleware([
            'userIsGestorN',
            'UserHasSameCountryAsGestorN'
        ]);

        Route::get('ajax_users_gestorn/{pais_id}', [
            'as' => 'ajaxusersgestorn',
            'uses' => 'UserController@getUsersByAjaxGestorN'
        ])->middleware(['userIsGestorN']);


        Route::get('gestornusers/create', ['as' => 'gestornusercreate', 'uses' => 'UserController@gestorncreate']);


        // dashboard de gestornacional
        Route::get('/gestornhome',
            ['as' => 'homegestornacional', 'uses' => 'HomeController@indexgestorn'])->middleware(['userIsGestorN']);


        Route::get('eventogestorn/{id_evento}',
            [
                'as' => 'eventohomegestorn',
                'uses' => 'EventoController@gerirEventoGestorN'
            ])->middleware(['userIsGestorN']);


        Route::get('ajax_events_gestorn',
            ['as' => 'ajaxeventsgestorn', 'uses' => 'EventoController@getEventsByAjaxGestorN']);
        // Route::post('ajax_events_admin', ['as' => 'ajaxeventsadmin', 'uses' => 'EventoController@getEventsByAjaxAdmin']);

        Route::get('getusersinscritosgestorn/{id_evento}/{estado?}',
            ['as' => 'getusersinscritosgestorn', 'uses' => 'UserController@getUsersInscritosGestorN']);

        Route::put('getusersinscritosgestorn/{id_inscricao}/{estado?}',
            ['as' => 'getusersinscritosgestorn', 'uses' => 'UserController@gravarInscricoesByAjaxGestorN']);


        // gestor nacional - fichas inscricao
        Route::get('pdf_registrations/{id_evento}/{sort}', [
            'as' => 'pdfregistrationsgestornsort',
            'uses' => 'ListagensController@pdfRegistrationsSort'
        ])->middleware(['userIsGestorN']);


        // gestor nacional - exporta criadores no evento
        Route::get('export_breeders/{id_evento}/{ordenacao}', [
            'as' => 'exportbreeders',
            'uses' => 'ListagensController@exportaExpositoresGestorN'
        ])->middleware(['userIsGestorN']);


        // gestor nacional - exporta expositores contactos
        Route::get('export_breeders_contacts/{id_evento}', [
            'as' => 'exportbreederscontacts',
            'uses' => 'ListagensController@exportaExpositoresContactosGestorN'
        ])->middleware(['userIsGestorN']);

        //gestor nacional - exporta contactos criadores em excel
        Route::get('export_breeders_contacts_excel/{id_evento}', [
            'as' => 'exportbreederscontactsexcel',
            'uses' => 'ListagensController@exportaExpositoresContactosExcelGestorN'
        ])->middleware(['userIsGestorN']);


        /*
         * FIM GESTOR NACIONAL - todas as routes
         *
         */


        /*             _
        *     /\      | |         (_)
        *    /  \   __| |_ __ ___  _ _ __
        *   / /\ \ / _` | '_ ` _ \| | '_ \
        *  / ____ \ (_| | | | | | | | | | |
        * /_/    \_\__,_|_| |_| |_|_|_| |_|
        */

        /*
         * PDFS EXPORTS
         *
         */

        /*
         *
         * FIM PDFS EXPORTS
         *
         */

        /*
        * USERS
        */
        Route::match(['get', 'post'], 'users/list',
            ['as' => 'adminuserslist', 'uses' => 'UserController@getUserList'])->middleware(['userIsAdmin']);

        // criar criador
        Route::get('users/create', ['as' => 'adminusercreate', 'uses' => 'UserController@create']);

        Route::post('users/store', ['as' => 'adminuserstore', 'uses' => 'UserController@store']);

// editar associações a clubes e suas permissões
        Route::get('users/{id_user}/editperm', ['as' => 'adminusereditperm', 'uses' => 'UserController@editPerm']);

// associar criador a clube
        Route::post('users/associaclube', ['as' => 'usersassociaclube', 'uses' => 'UserController@userAssociaClube']);

// remove associação de criador a clube
        Route::post('users/associaclube/remove',
            ['as' => 'removeuserassociaclube', 'uses' => 'UserController@removeUserAssociaClube']);

// grava alteração a permissão de criador em clube
        Route::post('users/permclube/edit',
            ['as' => 'edituserpermclube', 'uses' => 'UserController@editUserPermClube']);


// lista total de utilizadores
        //  Route::get('ajax_users_admin', ['as' => 'ajaxusersadmin', 'uses' => 'UserController@getUsersByAjaxAdmin']);
// editar user

        Route::patch('users/{id_user}', 'UserController@update');

        Route::get('ajax_users_admin/{user_estado_id?}/{pais_id?}',
            ['as' => 'ajaxusersadmin', 'uses' => 'UserController@getUsersByAjaxAdmin']);


// lista de utilizadores em estado pendente
        Route::get('ajax_users_admin_pending',
            ['as' => 'ajaxusersadminpending', 'uses' => 'UserController@getUsersByAjaxAdminPending']);

        Route::get('users/{id_user}/edit/{origem}', ['as' => 'adminusereditorigem', 'uses' => 'UserController@edit']);


        /*
         *
         * JUIZES
         *
         */


        Route::get('getjuizessistema', ['as' => 'getjuizessistema', 'uses' => 'EventoController@getJuizesSistema']);
        Route::get('getjuizesatribuidos/{id_evento}',
            ['as' => 'getjuizesatribuidos', 'uses' => 'EventoController@getJuizesAtribuidos']);


        Route::get('atribuir_juiz_classes/{id_evento}',
            ['as' => 'atribuir_juiz_classes', 'uses' => 'EventoController@atribuirJuizesClasses']);
        Route::post('atribuir_juiz_classes_gravar',
            ['as' => 'gravaratribuirjuizesclasses', 'uses' => 'EventoController@gravarAtribuirJuizesClasses']);


//Route::get('remover_juiz/{id_evento}', 'EventoController@formRemoverJuiz')
// já existe o método no controller
        Route::post('atribuir_juiz_eventos_gravar', 'EventoController@gravarAtribuirJuizesEventos');
        Route::get('ajax_juizes_evento_classes/{id_evento}',
            ['as' => 'ajaxjuizeseventoclasses', 'uses' => 'EventoController@ajaxJuizesEventoClasses']);

        Route::get('ajax_remover_juiz_evento_classe/{id_juiz_classe}',
            ['as' => 'ajaxremoverjuizeventoclasse', 'uses' => 'EventoController@ajaxRemoverJuizClasse']);


        /*
         * CLUBES E ASSOCIAÇÕES
         */

        //admin clubes
        Route::get('clubes/list', ['as' => 'adminclubeslist', 'uses' => 'ClubeController@getClubesList']);
        Route::get('ajax_clubes_admin', ['as' => 'ajaxclubesadmin', 'uses' => 'ClubeController@getClubesByAjax']);
        Route::get('ajax_clubes_federacao_admin/{id_federacao}',
            ['as' => 'ajaxclubesfederacaoadmin', 'uses' => 'ClubeController@getClubesByFederacaoByAjax']);
        Route::get('ajax_numusersclube_admin/{id_clube}',
            ['as' => 'ajaxnumusersclubeadmin', 'uses' => 'ClubeController@getNumUsersByClubeId']);


        Route::get('clubes/create', ['as' => 'adminclubecreate', 'uses' => 'ClubeController@create']);
        Route::post('clubes/store', ['as' => 'adminclubestore', 'uses' => 'ClubeController@store']);

        Route::get('clubes/{id_clube}/edit', ['as' => 'adminclubeedit', 'uses' => 'ClubeController@edit']);
        Route::patch('clubes/{id_clube}', ['as' => 'adminclubeupdate', 'uses' => 'ClubeController@update']);

        Route::get('ajax_deleteclube_admin/{id_clube}',
            ['as' => 'ajaxdeleteclubeadmin', 'uses' => 'ClubeController@destroy']);
        /*
         * FEDERAÇÕES
         */

        Route::get('federacoes/list', [
            'as' => 'adminfederacoeslist',
            'uses' => 'FederacaoController@getFederacoesList'
        ])->middleware(['userIsAdmin']);
        Route::get('ajax_federacoes_admin', [
            'as' => 'ajaxfederacoesadmin',
            'uses' => 'FederacaoController@getFederacoesByAjax'
        ])->middleware(['userIsAdmin']);
        Route::get('ajax_numclubesfederacao_admin/{id_federacao}', [
            'as' => 'ajaxnumclubesfederacaoadmin',
            'uses' => 'FederacaoController@getNumClubesByFederacaoId'
        ])->middleware(['userIsAdmin']);

        Route::get('federacoes/create',
            ['as' => 'adminfederacaocreate', 'uses' => 'FederacaoController@create'])->middleware(['userIsAdmin']);
        Route::post('federacoes/store',
            ['as' => 'adminfederacaostore', 'uses' => 'FederacaoController@store'])->middleware(['userIsAdmin']);

        Route::get('federacoes/{id_federacao}/edit',
            ['as' => 'adminfederacaoedit', 'uses' => 'FederacaoController@edit'])->middleware(['userIsAdmin']);
        Route::patch('federacoes/{id_federacao}',
            ['as' => 'adminfederacaoupdate', 'uses' => 'FederacaoController@update'])->middleware(['userIsAdmin']);

        Route::get('ajax_deletefederacao_admin/{id_federacao}',
            ['as' => 'ajaxdeletefederacaoadmin', 'uses' => 'FederacaoController@destroy'])->middleware(['userIsAdmin']);

        /*
         *
         * GESTOR NACIONAL ROUTES
         */


        Route::get('users/notineventogestorn/{id_evento}',
            ['as' => 'usersnotineventogestorn', 'uses' => 'UserController@getUsersNotInEventoSelectGestorN']);

        Route::get('eventos/{id_evento}/inscrevergestorn',
            ['as' => 'gestorninscreverc', 'uses' => 'EventoController@formInscreverCriadorGestorN']);


        Route::post('gravarInscricaoCriadorEventoAdmin', [
            'as' => 'gravarinscricaocriadoreventoadmin',
            'uses' => 'EventoController@gravarInscricaoCriadorEventoAdmin'
        ]);

        Route::post('ajax_alterar_estado_inscricao_criador_gestorn', [
            'as' => 'ajaxalterarestadoinscricaocriadorgestorn',
            'uses' => 'EventoController@ajaxAlterarEstadoInscricao'
        ])->middleware(['userIsGestorN']);


        /*
        *
        * FIM ROUTES - GESTOR NACIONAL ROUTES
        */

        /*
         * EVENTOS
         */

        Route::get('editclassepassaroajax/{id_evento}/{id_passaro}',
            ['as' => 'editclassepassaro', 'uses' => 'EventoController@editClassePassaroAjax']);

        Route::post('gravar_classe_passaro',
            ['as' => 'gravaralteracaoclassepassaro', 'uses' => 'EventoController@gravarAlteracaoClassePassaro']);


        Route::get('/eventosadmin/list',
            ['as' => 'eventosadminlist', 'uses' => 'EventoController@gestaoEventosAdmin'])->middleware(['userIsAdmin']);

        Route::get('eventos/create', ['as' => 'admineventocreate', 'uses' => 'EventoController@create'])->middleware(['userIsAdmin']);
        Route::post('eventos/store', ['as' => 'admineventostore', 'uses' => 'EventoController@store'])->middleware(['userIsAdmin']);

        Route::get('eventos/{id_evento}/edit', ['as' => 'admineventoedit', 'uses' => 'EventoController@edit'])->middleware(['userIsAdmin']);
        Route::patch('eventos/{id_evento}', ['as' => 'admineventoupdate', 'uses' => 'EventoController@update'])->middleware(['userIsAdmin']);

        Route::get('ajax_verificaeventoeliminacao_admin/{id_evento}',
            ['as' => 'ajaxverificaeventoeliminacaoadmin', 'uses' => 'EventoController@verificaEventoEliminacaoAdmin']);

        Route::get('ajax_deleteevento_admin/{id_evento}',
            ['as' => 'ajaxdeleteeventoadmin', 'uses' => 'EventoController@destroy']);

        Route::get('ajax_check_local_recolha/{evento_id}/{id_local}',
            ['as' => 'ajaxchecklocalrecolha', 'uses' => 'EventoController@checkLocalRecolha']);


        /*
         * INSCRIÇÕES
         */

        Route::get('eventos/{id_evento}/inscrever',
            ['as' => 'admininscreverc', 'uses' => 'EventoController@formInscreverCriador']);


        Route::get('users/notinevento/{id_evento}',
            ['as' => 'usersnotinevento', 'uses' => 'UserController@getUsersNotInEventoSelect']);

        Route::get('ajaxusersinscritos/{id_evento}/{pais_id?}',
            ['as' => 'ajaxusersinscritos', 'uses' => 'UserController@ajaxUsersInscritos']);

        Route::put('ajaxusersinscritos/{id_inscricao}/{pais_id?}',
            ['as' => 'ajaxinscricoes', 'uses' => 'EventoController@gravarInscricoesByAjax']);


        Route::post('gravarInscricaoCriadorEventoAdmin', [
            'as' => 'gravarinscricaocriadoreventoadmin',
            'uses' => 'EventoController@gravarInscricaoCriadorEventoAdmin'
        ]);


        /*
        *
        * INSCRIÇÃO DE PASSAROS EM INSCRIÇÃO DE CRIADOR
        *
        */

        Route::get('clube_evento/inscrever_passaros/{id_evento}/{id_inscricao?}',
            ['as' => 'clube_evento_inscrever_passaros', 'uses' => 'ClubeController@inscreverPassaroEvento']);

        Route::post('gravarInscricaoPassaroEvento',
            ['as' => 'gravarinscricaopassaroevento', 'uses' => 'ClubeController@gravarInscricaoPassaroEvento']);

        Route::post('editarInscricaoPassaroEvento',
            ['as' => 'editarinscricaopassaroevento', 'uses' => 'ClubeController@editarInscricaoPassaroEvento']);

        Route::get('ajax_getpassaro/{id_passaro}',
            ['as' => 'ajaxgetpassarobyid', 'uses' => 'EventoController@getPassaroById']);

        // chamado quando user clica num criador da lista de inscritos do evento
        Route::get('ajax_passaros/{id_inscricao}',
            ['as' => 'ajaxpassarosbyinscricao', 'uses' => 'EventoController@getPassarosByInscricaoByAjax']);
        Route::put('ajax_passaros/{id_inscricao}',
            ['as' => 'ajaxpassarosbyinscricao', 'uses' => 'EventoController@gravarPassarosByAjax']);

        // Route::get('ajax_inscrever_passaro/{id_evento}/{id_inscricao}', ['as' => 'ajaxinscreverpassaro', 'uses' => 'ClubeController@formInscreverPassaro']);

        Route::post('removerInscricaoPassaroEvento',
            ['as' => 'removerinscricaopassaroevento', 'uses' => 'ClubeController@removerInscricaoPassaroEvento']);
        Route::get('ajax_remover_inscricao_passaro_criador/{evento_id}/{id_passaro}',
            ['as' => 'ajaxremoverinscricaopassaro', 'uses' => 'ClubeController@ajaxRemoverInscricaoPassaroPorCriador']);
        Route::get('ajax_remover_inscricao_passaro/{evento_id}/{id_passaro}',
            ['as' => 'ajaxremoverinscricaopassaro', 'uses' => 'ClubeController@ajaxRemoverInscricaoPassaro']);

        Route::get('ajax_remover_inscricao_criador/{id_evento}/{id_inscricao}',
            ['as' => 'ajaxremoverinscricaocriador', 'uses' => 'ClubeController@formRemoverInscricaoCriador']);

        Route::get('ajax_remover_inscricao_criador_admin/{id_evento}/{id_inscricao}',
            ['as' => 'ajaxremoverinscricaocriador', 'uses' => 'EventoController@removerInscricaoCriadorAdmin']);

        Route::post('ajax_gravar_passaros',
            ['as' => 'ajaxgravarpassaros', 'uses' => 'EventoController@gravarPassarosByAjax']);

        //Route::get('ajax_gravar_passaros', ['as' => 'ajaxgravarpassaros', 'uses' => 'EventoController@gravarPassarosByAjax2']);


// ------------- secções / classes

        // abrir view se seccoes classes
        Route::get('seccoesclasses/{id_evento}', [
            'as' => 'eventos_seccoes_import',
            'uses' => 'EventoController@seccoesClasses'
        ])->middleware(['userIsAdmin']);

        // export de seccoes e classes
        Route::get('downloadExcelClasses/{id_evento}/{type}', [
            'as' => 'downloadexcelclasses',
            'uses' => 'MaatwebsiteController@downloadExcelClasses'
        ])->middleware(['userIsAdmin']);

        // get lista de seccoes classes por ajax para grid
        Route::get('ajax_seccoesclasses_admin/{id_evento}',
            ['as' => 'ajaxseccoesclassesadmin', 'uses' => 'EventoController@getSeccoesClassesByEventoByAjaxAdmin']);

        /*
         * FERRAMENTAS eventos
         *
         */

        Route::get('gerirfichasjulgamento/{id_evento}', [
            'as' => 'gerirfichasjulgamento',
            'uses' => 'EventoController@gerirFichasJulgamentoClasses'
        ])->middleware(['userIsAdmin']);

        Route::post('importfichasjulgamentoclasses', [
            'as' => 'importfichasjulgamentoclasses',
            'uses' => 'MaatwebsiteController@importFichasJulgamentoPorClasses'
        ])->middleware(['userIsAdmin']);


        /*
         * fim ferramentas
         */
        /*
         * USERS ferramentas
         */
        Route::get('adminimportusers',
            ['as' => 'adminimportusers', 'uses' => 'UserController@adminImportUsers'])->middleware(['userIsAdmin']);

        Route::post('importusers',
            ['as' => 'importusers', 'uses' => 'MaatwebsiteController@importUtilizadores'])->middleware(['userIsAdmin']);

        /*
         * fim ferramentas
         */


        /*
         *
         * JULGAMENTOS
         *
         */

        Route::get('julgar_evento_home/{id_evento}',
            ['as' => 'julgareventohome', 'uses' => 'EventoController@julgarEventoHome']);
        Route::get('julgar_evento_seccao/{id_evento}/{seccao}',
            ['as' => 'julgareventoseccao', 'uses' => 'EventoController@julgarEventoSeccao']);
        Route::get('julgar_evento_gaiolas/{id_evento}/{rescid}',
            ['as' => 'julgareventogaiolas', 'uses' => 'EventoController@julgarEventoGaiolas']);
        Route::get('julgar_gaiola_ficha/{id_evento}/{passaroid}',
            ['as' => 'julgargaiolaficha', 'uses' => 'EventoController@julgarGaiolaFicha']);

        Route::post('gravar_gaiola_comparativo',
            ['as' => 'gravargaiolacomparativo', 'uses' => 'EventoController@gravarGaiolaComparativo']);


        Route::post('gravar_gaiola_pontuacao',
            ['as' => 'gravargaiolapontuacao', 'uses' => 'EventoController@gravarGaiolaPontuacao']);

        Route::post('gravar_gaiola_pontuacao_equipa',
            ['as' => 'gravargaiolapontuacaoequipa', 'uses' => 'EventoController@gravarGaiolaPontuacaoEquipa']);

        Route::get('recalcular_classificacoes/{id_evento}', [
            'as' => 'recalcularclassificacoes',
            'uses' => 'EventoController@recalcularClassificacoes'
        ])->middleware(['userIsAdmin']);;

// home page de evento - ADMIN

        Route::post('fileUploadPatrocinador/{id_evento}', [
            'as' => 'patrocinadorUpload',
            'uses' => 'EventoController@uploadImagemPatrocinador'
        ]);

        Route::post('fileRemovePatrocinador/{id_evento}', [
            'as' => 'patrocinadorRemove',
            'uses' => 'EventoController@removeImagemPatrocinador'
        ]);


        Route::get('evento/{id_evento}',
            [
                'as' => 'eventohome',
                'uses' => 'EventoController@gerirEvento'
            ])->middleware(['userIsAdmin']);


        Route::get('ajax_events_admin', ['as' => 'ajaxeventsadmin', 'uses' => 'EventoController@getEventsByAjaxAdmin']);
        // Route::post('ajax_events_admin', ['as' => 'ajaxeventsadmin', 'uses' => 'EventoController@getEventsByAjaxAdmin']);

        Route::post('procurargaiola', ['as' => 'procurar_gaiola', 'uses' => 'EventoController@procurarGaiola']);

        Route::post('atribuir_numeracao_gaiolas', [
            'as' => 'atribuirnumeracaogaiolas',
            'uses' => 'EventoController@atribuirNumeracaoGaiolas'
        ])->middleware(['userIsAdmin']);

        // TODO: para possibilidade de, em cada evento, seleccionar o tipo de atribuição de gaiolas em qualquer momento do processo
        Route::post('atribuir_numeracao_gaiolasm2', [
            'as' => 'atribuirnumeracaogaiolasm2',
            'uses' => 'EventoController@atribuirNumeracaoGaiolasMetodo2'
        ])->middleware(['userIsAdmin']);

        Route::post('atribuir_numeracao_gaiolasm3', [
            'as' => 'atribuirnumeracaogaiolasm3',
            'uses' => 'EventoController@atribuirNumeracaoGaiolasMetodo3'
        ])->middleware(['userIsAdmin']);

        /*
         * QR codes generation
         */

        Route::post('gerar_qr_codes',
            ['as' => 'gerarqrcodes', 'uses' => 'EventoController@gerarQrCodes'])->middleware(['userIsAdmin']);

        /*
        * ANILHAS
        */

        //admin clubes
        Route::get('anilhas', ['as' => 'adminanilhas', 'uses' => 'AnilhaController@gerirAnilhas']);

        /*
         * ROTAS ANTIGAS
         *
         */

        require base_path('routes/old_fonp.php');

    });

Route::get('qrcode_ficha_julgamento/{id_evento}/{numgaiola}',
    [
        'as' => 'pdffichajulgamentoindividual',
        'uses' => 'QrController@pdfFichaJulgamentoPorGaiola'
    ])->middleware(['eventoActivo']);

/*
 * clean install original root route
 */

//Route::get('/', function () {
//    return view('welcome');
//});


//Route::get('/home', 'HomeController@index');
