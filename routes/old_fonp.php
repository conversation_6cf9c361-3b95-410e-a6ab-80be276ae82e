<?php

// CRIADORES

Route::get('users', ['as' => 'utilizadores', 'uses' => 'UserController@index']);
Route::get('ajax_users/{id_evento}/{tipo}', ['as' => 'ajaxusers', 'uses' => 'UserController@getUsersByAjax']);

// editar perfil criador

// todo: edição de perfil de utilizador
//Route::get('users/{id_user}/edit/{origem}', ['as' => 'adminusereditorigem', 'uses' => 'UserController@edit']);

// EVENTOS

Route::get('eventos', 'EventoController@index');

Route::post('importExcel', ['as' => 'importexcelseccoes', 'uses' => 'MaatwebsiteController@importExcel']);

// CLUBES

Route::get('clube_evento/{id_evento}/listagens', ['as' => 'clube_evento_listagens', 'uses' => 'ClubeController@gerirEventoClubeListagens']);

// gestor evento de clube - gerir evento
// todo: adicionar middleware para verificar se criador é admin de um dos clubes que gere este evento
Route::get('clube_evento/{id_evento}', ['as' => 'clube_evento_gestao', 'uses' => 'ClubeController@gerirEventoClube'])->middleware(['userIsClubEventManager'])->middleware(['userIsAdmin']);;

/*
 *
 *
 * ROUTES ANTIGAS - VERIFICAR VALIDADE OU IR SUBSTITUINDO
 *
 */

Route::get('clube_evento/inscrever_criadores/{id_evento}',
    ['as' => 'clube_evento_inscrever_criadores', 'uses' => 'ClubeController@inscreverCriadorEvento']);

Route::post('clube_evento/inscrever/gravar',
    ['as' => 'clube_evento_gravar_inscricoes_criadores', 'uses' => 'ClubeController@gravarInscricoesCriadorEvento']);

//Route::get('clubes', ['as' => 'clubes', 'uses' => 'ClubeController@indexClubes']);
Route::get('ajax_clubes', ['as' => 'ajaxclubes', 'uses' => 'ClubeController@getClubesByAjax']);

/*
 * FIM ROTAS ANTIGAS - VER VALIDADE OU SUBSTITUIR
 *
 */

// FICHAS DE JULGAMENTO

Route::get('atribuir_fichas_classes/{id_evento}', ['as' => 'atribuir_fichas_classes', 'uses' => 'EventoController@atribuirFichasClasses']);
Route::post('atribuir_fichas_classes_gravar', ['as' => 'gravaratribuirfichasclasses', 'uses' => 'EventoController@gravarAtribuirFichasClasses']);
Route::get('ajax_fichas_evento_classes/{id_evento}', ['as' => 'ajaxfichaseventoclasses', 'uses' => 'EventoController@ajaxFichasEventoClasses']);
Route::get('ajax_remover_ficha_evento_classe/{id_juiz}', ['as' => 'ajaxremoverfichaeventoclasse', 'uses' => 'EventoController@ajaxRemoverFichaEventoClasse']);

/*
 * FICHAS DE JULGAMENTO
 */

Route::get('fichas_julgamento_detalhadas/{id_evento}', ['as' => 'fichasjulgamentodetalhadas', 'uses' => 'EventoController@fichasJulgamentoDetalhadas']);

/*
 * FIM DE FICHAS DE JULGAMENTO
 */

Route::get('pdf_ficha_inscricao_admin/{id_evento}/{id_inscricao}', ['as' => 'pdffichainscricaoadmin', 'uses' => 'ListagensController@pdfFichasInscricao'])->middleware(['userIsEventManagerOrAdmin']);

//Route::get('pdf_fichas_devolucao/{id_clube}/{id_evento}', ['as' => 'pdffichasdevolucao', 'uses' => 'ListagensController@pdfFichasDevolucao']);

/*
 * LISTAGENS
 */
Route::get('ajuste_etiquetas', ['as' => 'ajusteetiquetas', 'uses' => 'EtiquetasController@ajustarEtiquetas'])->middleware(['userIsAdmin']);

Route::post('gravar_ajuste_etiquetas', ['as' => 'gravarajustesetiquetas', 'uses' => 'EtiquetasController@gravarAjustesEtiquetas']);

Route::get('etiquetas_provisorias_individuais/{id_evento}', ['as' => 'etiquetasprovisoriasindividuais', 'uses' => 'EtiquetasController@etiquetasProvisoriasIndividuais']);

Route::post('pdf_etiquetas_provisorias_individuais', ['as' => 'pdfetiquetasprovisoriasindividuais', 'uses' => 'EtiquetasController@pdfEtiquetasProvisoriasIndividuais']);

Route::post('pdf_etiquetas_provisorias_individuais_comparativo', ['as' => 'pdfetiquetasprovisoriasindividuaiscomparativo', 'uses' => 'ListagensController@pdfEtiquetasProvisoriasIndividuaisComparativo']);

Route::get('etiquetas_definitivas_individuais/{id_evento}', ['as' => 'etiquetasdefinitivasindividuais', 'uses' => 'EtiquetasController@etiquetasDefinitivasIndividuais']);

// route para ficha de inscrição individual - com inscricao_id
//Route::get('pdf_fichas_inscricao/{id_evento}/{id_inscricao}', ['as' => 'pdffichasinscricao', 'uses' => 'ListagensController@pdfFichasInscricao'])->middleware(['eventoActivo','inscricaoUserActivo']);


Route::get('pdf_etiquetas_provisorias_comparativo/{id_evento}/{id_inscricao?}', ['as' => 'pdfetiquetasprovisoriascomparativo', 'uses' => 'ListagensController@pdfEtiquetasProvisoriasComparativo']);

/*
 * FIM LISTAGENS
 */

// INSCRIÇÕES

Route::get('ajax_remover_inscricao_criador/{id_evento}/{id_inscricao}', ['as' => 'ajaxremoverinscricaocriador', 'uses' => 'ClubeController@formRemoverInscricaoCriador']);

// route antiga que apenas perguntava "tem a certeza?"
Route::get('ajax_inscrever_criador/{id_evento}/{id_criador}', ['as' => 'ajaxinscrevercriador', 'uses' => 'ClubeController@formInscreverCriador']);

/*
Route::get('ajax_remover_inscricao_criador/{id_clube}/{id_evento}/{id_inscricao}', ['as' => 'ajaxremoverinscricaocriador', 'uses' => 'ClubeController@formRemoverInscricaoCriador']);
*/

/*
 * ADMINISTRADOR
 */

// lista histórico de eventos - eventos inactivos

Route::get('eventos_historico_admin', ['as' => 'eventoshistoricoadmin', 'uses' => 'EventoController@listaEventosHistoricoAdmin'])->middleware(['userIsAdmin']);

/*
 * FIM ADMINISTRADOR
 */

// admin users
//Route::get('adminhome', ['as' => 'casaadmin', 'uses' => 'HomeController@indexadmin'])->middleware(['userIsAdmin']);

//Route::get('users/{id_user}/edit', ['as' => 'adminuseredit', 'uses' => 'UserController@edit']);

Route::put('users/{id_user}', ['as' => 'user.update', 'user.update' => 'UserController@update']);

//Route::post('users', 'UserController@store');

Route::get('ajax_activar_criadores', ['as' => 'ajaxactivarcriadores', 'uses' => 'UserController@activarCriadoresPendentes']);
