# This is a sample build configuration for PHP.
# Check our guides at https://confluence.atlassian.com/x/e8YWN for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: php:7.3

pipelines:
  default:
    - step:
        caches:
          - composer
        script:
          # FIRST RUN 
          #- apt-get update
          #- apt-get -qq install git-ftp
          #- git ftp init --user $FTP_USERNAME --passwd $FTP_PASSWORD ftp://www.conforni.com/public_html/fonpadmin/
          # next command fails because its run on sudo
          #- php composer.phar install

          # CONTINUOUS RUN
          - apt-get update
          - apt-get -qq install git-ftp

          # CONTINUOUS RUN - use the next line after you first use the init
          - git ftp push --user $FTP_USERNAME --passwd $FTP_PASSWORD ftp://www.conforni.com/public_html/fonpadmin/

          # FIRST RUN - use the following line to initialize the ftp repo
          #- git ftp init --user $FTP_USERNAME --passwd $FTP_PASSWORD ftp://www.conforni.com/public_html/fonpadmin/
          #- php composer.phar self-update
          #- php composer.phar update
