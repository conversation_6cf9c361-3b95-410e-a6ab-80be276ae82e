# PHP CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-php/ for more details
#
version: 2
jobs:
  deploy_develop:
    docker:
      - image: cimg/php:8.2.11

    working_directory: ~/repo
 
    steps:
      - checkout
 
      - run: 
          name: Deploy Dev Branch
          command: |
            sudo apt-get update
            sudo apt-get -qq install git-ftp
            echo "Deploying project to develop...."
            echo $(git status)
            
            # command for first time upload - uploads everything (fails on circleci because of timeout without activity
            # git ftp init -u "${username}" -p "${password}" "${hostname_dev}"
                        
            # command to "mark" directory - from then on we can use the push
            # git ftp catchup -u "${username}" -p "${password}" "${hostname_dev}"
                        
            # push only the changes commited and pushed to the develop branch
            git ftp push -u "${username}" -p "${password}" "${hostname_dev}"
  deploy_master:
    docker:
      - image: cimg/php:8.2.11

    working_directory: ~/repo

    steps:
      - checkout

      - run: 
          name: Deploy Master Branch
          command: |
            sudo apt-get update
            sudo apt-get -qq install git-ftp
            echo "Deploying project to master ...."
            echo $(git status)
            
            # command for first time upload - uploads everything (fails on circleci because of timeout without activity
            # git ftp init -u "${username}" -p "${password}" "${hostname_master}"
                        
            # command to "mark" directory - from then on we can use the push
            # git ftp catchup -u "${username}" -p "${password}" "${hostname_master}"
                        
            # push only the changes commited and pushed to the master branch
            git ftp push -u "${username}" -p "${password}" "${hostname_master}"
 
workflows:
  version: 2
  just-deploy:
    jobs:
      - deploy_develop:
          filters:
            branches:
              only: develop
      - deploy_master:
          filters:
            branches:
              only: master